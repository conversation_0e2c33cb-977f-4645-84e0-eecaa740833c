import fs from 'fs';
import path from 'path';
import os from 'os';
import { Logger } from '../utils/Logger';
import { Experience, LearningPattern } from '../agent/MemoryManager';

export interface KnowledgeNode {
  id: string;
  domain: string;
  concept: string;
  description: string;
  confidence: number;
  sources: string[];
  connections: string[];
  lastUpdated: Date;
  usageCount: number;
}

export interface KnowledgeDomain {
  name: string;
  description: string;
  nodes: KnowledgeNode[];
  expertise: number;
  lastActivity: Date;
}

export interface CrossDomainConnection {
  sourceNodeId: string;
  targetNodeId: string;
  connectionType: 'similar' | 'dependent' | 'complementary' | 'analogous';
  strength: number;
  reasoning: string;
}

export interface KnowledgeInsight {
  id: string;
  type: 'pattern' | 'optimization' | 'discovery' | 'connection';
  description: string;
  impact: number;
  domains: string[];
  timestamp: Date;
}

export class KnowledgeRepository {
  private logger: Logger;
  private knowledgePath: string;
  private domains: Map<string, KnowledgeDomain> = new Map();
  private nodes: Map<string, KnowledgeNode> = new Map();
  private connections: CrossDomainConnection[] = [];
  private insights: KnowledgeInsight[] = [];

  constructor(logger: Logger) {
    this.logger = logger;
    this.knowledgePath = path.join(os.homedir(), '.goc-agent', 'knowledge.json');
    this.loadKnowledge();
  }

  public async storeKnowledge(domain: string, concept: string, description: string, sources: string[] = []): Promise<string> {
    const nodeId = this.generateId();
    
    const node: KnowledgeNode = {
      id: nodeId,
      domain,
      concept,
      description,
      confidence: 0.8,
      sources,
      connections: [],
      lastUpdated: new Date(),
      usageCount: 0
    };

    this.nodes.set(nodeId, node);
    
    // Update or create domain
    const existingDomain = this.domains.get(domain);
    if (existingDomain) {
      existingDomain.nodes.push(node);
      existingDomain.lastActivity = new Date();
      existingDomain.expertise = Math.min(existingDomain.expertise + 0.1, 1.0);
    } else {
      this.domains.set(domain, {
        name: domain,
        description: `Knowledge domain for ${domain}`,
        nodes: [node],
        expertise: 0.5,
        lastActivity: new Date()
      });
    }

    // Find and create connections
    await this.findAndCreateConnections(nodeId);
    
    this.saveKnowledge();
    this.logger.debug(`📚 Stored knowledge: ${concept} in ${domain}`);
    
    return nodeId;
  }

  public async storePatternAnalysis(successPatterns: LearningPattern[], failurePatterns: LearningPattern[]): Promise<void> {
    this.logger.info('📊 Storing pattern analysis in knowledge repository...');

    // Store success patterns as knowledge
    for (const pattern of successPatterns) {
      await this.storeKnowledge(
        'patterns',
        `Success Pattern: ${pattern.pattern}`,
        `Successful approach with ${pattern.frequency} occurrences and ${(pattern.successRate * 100).toFixed(1)}% success rate`,
        pattern.examples
      );
    }

    // Store failure patterns for learning
    for (const pattern of failurePatterns) {
      await this.storeKnowledge(
        'patterns',
        `Failure Pattern: ${pattern.pattern}`,
        `Pattern to avoid with ${pattern.frequency} occurrences and ${(pattern.successRate * 100).toFixed(1)}% success rate`,
        pattern.examples
      );
    }

    // Generate insights from patterns
    await this.generatePatternInsights(successPatterns, failurePatterns);
  }

  public async integrateExperiences(experiences: Experience[]): Promise<void> {
    this.logger.info('🔗 Integrating experiences into knowledge repository...');

    for (const experience of experiences) {
      if (experience.action && experience.outcome === 'success') {
        const domain = this.extractDomainFromTask(experience.task);
        const concept = experience.action.type;
        const description = `${experience.action.instruction} - ${experience.action.reasoning}`;
        
        await this.storeKnowledge(domain, concept, description, experience.filesMentioned || []);
      }
    }
  }

  public async expandKnowledgeConnections(): Promise<void> {
    this.logger.info('🌐 Expanding knowledge connections...');

    const nodeIds = Array.from(this.nodes.keys());
    
    for (let i = 0; i < nodeIds.length; i++) {
      for (let j = i + 1; j < nodeIds.length; j++) {
        const node1 = this.nodes.get(nodeIds[i])!;
        const node2 = this.nodes.get(nodeIds[j])!;
        
        const connectionStrength = this.calculateConnectionStrength(node1, node2);
        
        if (connectionStrength > 0.6) {
          await this.createConnection(node1.id, node2.id, connectionStrength);
        }
      }
    }
  }

  public getKnowledgeForDomain(domain: string): KnowledgeDomain | undefined {
    return this.domains.get(domain);
  }

  public searchKnowledge(query: string, maxResults: number = 10): KnowledgeNode[] {
    const queryLower = query.toLowerCase();
    const results: { node: KnowledgeNode; relevance: number }[] = [];

    for (const node of this.nodes.values()) {
      let relevance = 0;
      
      // Check concept match
      if (node.concept.toLowerCase().includes(queryLower)) {
        relevance += 0.8;
      }
      
      // Check description match
      if (node.description.toLowerCase().includes(queryLower)) {
        relevance += 0.6;
      }
      
      // Check domain match
      if (node.domain.toLowerCase().includes(queryLower)) {
        relevance += 0.4;
      }

      if (relevance > 0) {
        results.push({ node, relevance });
      }
    }

    return results
      .sort((a, b) => b.relevance - a.relevance)
      .slice(0, maxResults)
      .map(r => r.node);
  }

  public getRelatedKnowledge(nodeId: string): KnowledgeNode[] {
    const node = this.nodes.get(nodeId);
    if (!node) return [];

    const related: KnowledgeNode[] = [];
    
    // Get directly connected nodes
    for (const connectionId of node.connections) {
      const connectedNode = this.nodes.get(connectionId);
      if (connectedNode) {
        related.push(connectedNode);
      }
    }

    // Get nodes from same domain
    const domain = this.domains.get(node.domain);
    if (domain) {
      related.push(...domain.nodes.filter(n => n.id !== nodeId).slice(0, 5));
    }

    return related;
  }

  public getDomainExpertise(): Map<string, number> {
    const expertise = new Map<string, number>();
    
    for (const [domainName, domain] of this.domains) {
      expertise.set(domainName, domain.expertise);
    }
    
    return expertise;
  }

  public getKnowledgeStats(): {
    totalNodes: number;
    totalDomains: number;
    totalConnections: number;
    totalInsights: number;
    averageConfidence: number;
  } {
    const totalNodes = this.nodes.size;
    const totalDomains = this.domains.size;
    const totalConnections = this.connections.length;
    const totalInsights = this.insights.length;
    
    let totalConfidence = 0;
    for (const node of this.nodes.values()) {
      totalConfidence += node.confidence;
    }
    const averageConfidence = totalNodes > 0 ? totalConfidence / totalNodes : 0;

    return {
      totalNodes,
      totalDomains,
      totalConnections,
      totalInsights,
      averageConfidence
    };
  }

  private async findAndCreateConnections(nodeId: string): Promise<void> {
    const newNode = this.nodes.get(nodeId);
    if (!newNode) return;

    for (const [existingId, existingNode] of this.nodes) {
      if (existingId === nodeId) continue;

      const strength = this.calculateConnectionStrength(newNode, existingNode);
      if (strength > 0.7) {
        await this.createConnection(nodeId, existingId, strength);
      }
    }
  }

  private calculateConnectionStrength(node1: KnowledgeNode, node2: KnowledgeNode): number {
    let strength = 0;

    // Same domain bonus
    if (node1.domain === node2.domain) {
      strength += 0.3;
    }

    // Concept similarity
    const conceptSimilarity = this.calculateTextSimilarity(node1.concept, node2.concept);
    strength += conceptSimilarity * 0.4;

    // Description similarity
    const descSimilarity = this.calculateTextSimilarity(node1.description, node2.description);
    strength += descSimilarity * 0.3;

    return Math.min(strength, 1.0);
  }

  private calculateTextSimilarity(text1: string, text2: string): number {
    const words1 = text1.toLowerCase().split(/\s+/);
    const words2 = text2.toLowerCase().split(/\s+/);
    
    const intersection = words1.filter(word => words2.includes(word));
    const union = [...new Set([...words1, ...words2])];
    
    return intersection.length / union.length;
  }

  private async createConnection(nodeId1: string, nodeId2: string, strength: number): Promise<void> {
    const connection: CrossDomainConnection = {
      sourceNodeId: nodeId1,
      targetNodeId: nodeId2,
      connectionType: 'similar',
      strength,
      reasoning: 'Automatically detected similarity'
    };

    this.connections.push(connection);

    // Update node connections
    const node1 = this.nodes.get(nodeId1);
    const node2 = this.nodes.get(nodeId2);
    
    if (node1 && !node1.connections.includes(nodeId2)) {
      node1.connections.push(nodeId2);
    }
    
    if (node2 && !node2.connections.includes(nodeId1)) {
      node2.connections.push(nodeId1);
    }
  }

  private async generatePatternInsights(successPatterns: LearningPattern[], failurePatterns: LearningPattern[]): Promise<void> {
    // Generate insights from successful patterns
    for (const pattern of successPatterns.slice(0, 3)) {
      const insight: KnowledgeInsight = {
        id: this.generateId(),
        type: 'pattern',
        description: `High success pattern identified: ${pattern.pattern} with ${(pattern.successRate * 100).toFixed(1)}% success rate`,
        impact: pattern.successRate * pattern.frequency,
        domains: ['patterns'],
        timestamp: new Date()
      };
      
      this.insights.push(insight);
    }
  }

  private extractDomainFromTask(task: string): string {
    const taskLower = task.toLowerCase();
    
    if (taskLower.includes('file') || taskLower.includes('edit') || taskLower.includes('create')) {
      return 'file-operations';
    } else if (taskLower.includes('web') || taskLower.includes('search') || taskLower.includes('browse')) {
      return 'web-research';
    } else if (taskLower.includes('code') || taskLower.includes('function') || taskLower.includes('class')) {
      return 'programming';
    } else if (taskLower.includes('chat') || taskLower.includes('conversation')) {
      return 'communication';
    }
    
    return 'general';
  }

  private loadKnowledge(): void {
    try {
      if (fs.existsSync(this.knowledgePath)) {
        const data = JSON.parse(fs.readFileSync(this.knowledgePath, 'utf8'));
        
        if (data.domains) {
          this.domains = new Map(data.domains);
        }
        
        if (data.nodes) {
          this.nodes = new Map(data.nodes);
        }
        
        if (data.connections) {
          this.connections = data.connections;
        }
        
        if (data.insights) {
          this.insights = data.insights;
        }
        
        this.logger.debug(`📚 Loaded knowledge: ${this.nodes.size} nodes, ${this.domains.size} domains`);
      }
    } catch (error) {
      this.logger.debug(`Failed to load knowledge: ${error}`);
    }
  }

  private saveKnowledge(): void {
    try {
      const dir = path.dirname(this.knowledgePath);
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }

      const data = {
        domains: Array.from(this.domains.entries()),
        nodes: Array.from(this.nodes.entries()),
        connections: this.connections,
        insights: this.insights,
        lastSaved: new Date()
      };

      fs.writeFileSync(this.knowledgePath, JSON.stringify(data, null, 2));
    } catch (error) {
      this.logger.error(`Failed to save knowledge: ${error}`);
    }
  }

  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }
}
