<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\SessionController;
use App\Http\Controllers\AgentController;
use App\Http\Controllers\ContextController;
use App\Http\Controllers\ApiKeyController;
use App\Http\Controllers\SubscriptionController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Public routes
Route::prefix('auth')->group(function () {
    Route::post('register', [AuthController::class, 'register']);
    Route::post('login', [AuthController::class, 'login']);
});

// Protected routes (temporarily disabled auth for testing)
Route::group([], function () {
    // Authentication routes
    Route::prefix('auth')->group(function () {
        Route::post('logout', [AuthController::class, 'logout']);
        Route::get('user', [AuthController::class, 'user']);
    });

    // Session management routes
    Route::prefix('sessions')->group(function () {
        Route::get('/', [SessionController::class, 'index']);
        Route::post('/', [SessionController::class, 'store']);
        Route::get('{session}', [SessionController::class, 'show']);
        Route::put('{session}', [SessionController::class, 'update']);
        Route::delete('{session}', [SessionController::class, 'destroy']);
        Route::post('{session}/messages', [SessionController::class, 'addMessage']);
    });

    // Agent routes with rate limiting
    Route::prefix('agent')->middleware('rate.limit')->group(function () {
        Route::post('chat', [AgentController::class, 'chat']);
        Route::post('task', [AgentController::class, 'executeTask']);
        Route::get('status', [AgentController::class, 'status']);
        Route::get('providers', [AgentController::class, 'providers']);
        Route::post('tools/{tool}', [AgentController::class, 'executeTool']);
    });

    // Context engine routes
    Route::prefix('context')->group(function () {
        Route::post('index', [ContextController::class, 'indexProject']);
        Route::post('search', [ContextController::class, 'searchCode']);
        Route::get('stats', [ContextController::class, 'getProjectStats']);
    });

    // API Key management routes
    Route::prefix('api-keys')->group(function () {
        Route::get('/', [ApiKeyController::class, 'index']);
        Route::post('/', [ApiKeyController::class, 'store']);
        Route::put('{apiKey}', [ApiKeyController::class, 'update']);
        Route::delete('{apiKey}', [ApiKeyController::class, 'destroy']);
        Route::post('{apiKey}/toggle', [ApiKeyController::class, 'toggle']);
        Route::get('{apiKey}/usage', [ApiKeyController::class, 'usage']);
    });

    // Subscription management routes
    Route::prefix('subscription')->group(function () {
        Route::get('/', [SubscriptionController::class, 'index']);
        Route::post('subscribe/{plan}', [SubscriptionController::class, 'subscribe']);
        Route::post('cancel', [SubscriptionController::class, 'cancelSubscription']);
        Route::post('resume', [SubscriptionController::class, 'resumeSubscription']);
    });
});

// Health check route
Route::get('health', function () {
    return response()->json([
        'status' => 'ok',
        'timestamp' => now(),
        'service' => 'GOC Agent API'
    ]);
});
