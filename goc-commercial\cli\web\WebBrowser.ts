import axios, { AxiosInstance } from 'axios';
import * as cheerio from 'cheerio';
import { Logger } from '../utils/Logger';

export interface SearchResult {
  title: string;
  url: string;
  snippet: string;
  source: string;
}

export interface WebContent {
  url: string;
  title: string;
  content: string;
  summary: string;
  links: string[];
  images: string[];
  metadata: {
    description?: string;
    keywords?: string[];
    author?: string;
    publishDate?: string;
  };
}

export interface WebSearchOptions {
  maxResults?: number;
  region?: string;
  safeSearch?: 'strict' | 'moderate' | 'off';
  timeRange?: 'd' | 'w' | 'm' | 'y'; // day, week, month, year
}

export class WebBrowser {
  private logger: Logger;
  private httpClient: AxiosInstance;
  private userAgent: string;

  constructor(logger: Logger) {
    this.logger = logger;
    this.userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36';
    
    this.httpClient = axios.create({
      timeout: 10000, // Reduced timeout to 10 seconds
      headers: {
        'User-Agent': this.userAgent,
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
      }
    });
  }

  public async searchWeb(query: string, options: WebSearchOptions = {}): Promise<SearchResult[]> {
    try {
      // Removed verbose logging - handled at higher level
      
      const {
        maxResults = 10,
        region = 'us-en',
        safeSearch = 'moderate',
        timeRange
      } = options;

      // Use DuckDuckGo search
      const searchUrl = 'https://html.duckduckgo.com/html/';
      const params = new URLSearchParams({
        q: query,
        kl: region,
        safe: safeSearch,
        ...(timeRange && { df: timeRange })
      });

      const response = await this.httpClient.get(`${searchUrl}?${params}`);
      const $ = cheerio.load(response.data);
      
      const results: SearchResult[] = [];
      
      // Try multiple selectors for different DuckDuckGo layouts
      const resultSelectors = ['.result', '.web-result', '.result__body'];

      for (const selector of resultSelectors) {
        $(selector).each((_, element) => {
          if (results.length >= maxResults) return false;

          const $element = $(element);

          // Try different title selectors
          const titleSelectors = ['.result__title a', '.result__a', 'h2 a', 'a[href]'];
          let titleElement = null;
          let title = '';
          let url = '';

          for (const titleSel of titleSelectors) {
            titleElement = $element.find(titleSel).first();
            if (titleElement.length > 0) {
              title = titleElement.text().trim();
              url = titleElement.attr('href') || '';
              break;
            }
          }

          // Try different snippet selectors
          const snippetSelectors = ['.result__snippet', '.result__body', '.snippet'];
          let snippet = '';

          for (const snippetSel of snippetSelectors) {
            const snippetElement = $element.find(snippetSel);
            if (snippetElement.length > 0) {
              snippet = snippetElement.text().trim();
              break;
            }
          }

          // Clean up DuckDuckGo redirect URLs
          if (url.startsWith('/l/?uddg=') || url.includes('uddg=')) {
            const urlMatch = url.match(/uddg=([^&]+)/);
            if (urlMatch) {
              url = decodeURIComponent(urlMatch[1]);
            }
          }

          // Ensure URL has protocol
          if (url.startsWith('//')) {
            url = 'https:' + url;
          } else if (!url.startsWith('http://') && !url.startsWith('https://')) {
            url = 'https://' + url;
          }

          if (title && url && snippet && this.isValidUrl(url)) {
            results.push({
              title,
              url,
              snippet,
              source: 'DuckDuckGo'
            });
          }
        });

        if (results.length > 0) break; // Found results with this selector
      }

      // If no results found, try a simpler approach with direct URLs
      if (results.length === 0) {
        this.logger.info('🔄 Trying alternative search approach...');
        return await this.fallbackSearch(query, maxResults);
      }

      // Removed verbose logging - handled at higher level
      return results;

    } catch (error) {
      this.logger.error(`❌ Web search failed: ${error instanceof Error ? error.message : String(error)}`);
      return [];
    }
  }

  public async fetchWebContent(url: string): Promise<WebContent | null> {
    try {
      // Removed verbose logging - handled at higher level
      
      const response = await this.httpClient.get(url);
      const $ = cheerio.load(response.data);
      
      // Remove script and style elements
      $('script, style, nav, header, footer, aside, .advertisement, .ads').remove();
      
      // Extract title
      const title = $('title').text().trim() || 
                   $('h1').first().text().trim() || 
                   'Untitled';
      
      // Extract main content
      const contentSelectors = [
        'article',
        'main',
        '.content',
        '.post-content',
        '.entry-content',
        '.article-content',
        '#content',
        '.main-content'
      ];
      
      let content = '';
      for (const selector of contentSelectors) {
        const element = $(selector);
        if (element.length > 0) {
          content = element.text().trim();
          break;
        }
      }
      
      // Fallback to body content if no specific content area found
      if (!content) {
        content = $('body').text().trim();
      }
      
      // Clean up content
      content = content
        .replace(/\s+/g, ' ')
        .replace(/\n\s*\n/g, '\n')
        .trim();
      
      // Extract metadata
      const description = $('meta[name="description"]').attr('content') || 
                         $('meta[property="og:description"]').attr('content') || '';
      
      const keywords = $('meta[name="keywords"]').attr('content')?.split(',').map(k => k.trim()) || [];
      const author = $('meta[name="author"]').attr('content') || 
                    $('meta[property="article:author"]').attr('content') || '';
      
      const publishDate = $('meta[property="article:published_time"]').attr('content') || 
                         $('meta[name="date"]').attr('content') || '';
      
      // Extract links
      const links: string[] = [];
      $('a[href]').each((_, element) => {
        const href = $(element).attr('href');
        if (href && href.startsWith('http')) {
          links.push(href);
        }
      });
      
      // Extract images
      const images: string[] = [];
      $('img[src]').each((_, element) => {
        const src = $(element).attr('src');
        if (src && src.startsWith('http')) {
          images.push(src);
        }
      });
      
      // Create summary (first 500 characters)
      const summary = content.length > 500 ? 
        content.substring(0, 500) + '...' : 
        content;
      
      const webContent: WebContent = {
        url,
        title,
        content,
        summary,
        links: [...new Set(links)].slice(0, 20), // Unique links, max 20
        images: [...new Set(images)].slice(0, 10), // Unique images, max 10
        metadata: {
          description,
          keywords,
          author,
          publishDate
        }
      };
      
      // Removed verbose logging - handled at higher level
      return webContent;
      
    } catch (error) {
      this.logger.error(`❌ Failed to fetch content from ${url}: ${error instanceof Error ? error.message : String(error)}`);
      return null;
    }
  }

  public async searchAndFetch(query: string, maxResults: number = 3): Promise<WebContent[]> {
    try {
      // Removed verbose logging - handled at higher level

      // Add timeout wrapper to prevent hanging
      const timeoutPromise = new Promise<WebContent[]>((_, reject) => {
        setTimeout(() => reject(new Error('Web search timeout after 60 seconds')), 60000);
      });

      const searchPromise = this.performSearchAndFetch(query, maxResults);

      return await Promise.race([searchPromise, timeoutPromise]);

    } catch (error) {
      this.logger.error(`❌ Search and fetch failed: ${error instanceof Error ? error.message : String(error)}`);
      return [];
    }
  }

  private async performSearchAndFetch(query: string, maxResults: number): Promise<WebContent[]> {
    // Search for results
    const searchResults = await this.searchWeb(query, { maxResults });

    if (searchResults.length === 0) {
      this.logger.warn('No search results found');
      return [];
    }

    // Fetch content from top results
    const contents: WebContent[] = [];

    for (const result of searchResults.slice(0, maxResults)) {
      try {
        const content = await this.fetchWebContent(result.url);
        if (content) {
          contents.push(content);
        }
      } catch (error) {
        this.logger.warn(`Failed to fetch ${result.url}: ${error instanceof Error ? error.message : String(error)}`);
      }

      // Add small delay to be respectful to servers
      await new Promise(resolve => setTimeout(resolve, 500)); // Reduced delay
    }

    // Removed verbose logging - handled at higher level
    return contents;
  }

  public isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return url.startsWith('http://') || url.startsWith('https://');
    } catch {
      return false;
    }
  }

  public extractDomain(url: string): string {
    try {
      return new URL(url).hostname;
    } catch {
      return '';
    }
  }

  private async fallbackSearch(query: string, maxResults: number): Promise<SearchResult[]> {
    // Fallback: Create some common search URLs for the query
    const searchTerms = query.toLowerCase().replace(/\s+/g, '+');
    const fallbackUrls = [
      `https://stackoverflow.com/search?q=${searchTerms}`,
      `https://github.com/search?q=${searchTerms}`,
      `https://developer.mozilla.org/en-US/search?q=${searchTerms}`,
      `https://docs.microsoft.com/en-us/search/?terms=${searchTerms}`
    ];

    const results: SearchResult[] = [];

    for (let i = 0; i < Math.min(fallbackUrls.length, maxResults); i++) {
      const url = fallbackUrls[i];
      const domain = this.extractDomain(url);

      results.push({
        title: `${query} - ${domain}`,
        url: url,
        snippet: `Search results for "${query}" on ${domain}`,
        source: 'Fallback'
      });
    }

    this.logger.info(`🔄 Generated ${results.length} fallback search results`);
    return results;
  }
}
