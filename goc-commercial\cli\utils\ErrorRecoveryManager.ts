import { Logger } from './Logger';
import { AIProviderManager } from '../ai/AIProviderManager';
import { ConfigManager } from '../config/ConfigManager';

export interface ErrorRecoveryStrategy {
  name: string;
  canHandle: (error: Error) => boolean;
  recover: (error: Error, context: ErrorContext) => Promise<RecoveryResult>;
  priority: number;
}

export interface ErrorContext {
  operation: string;
  provider?: string;
  model?: string;
  retryCount: number;
  maxRetries: number;
  originalInput?: any;
  metadata?: Record<string, any>;
}

export interface RecoveryResult {
  success: boolean;
  result?: any;
  newStrategy?: string;
  message?: string;
  shouldRetry?: boolean;
}

export class ErrorRecoveryManager {
  private logger: Logger;
  private aiManager: AIProviderManager;
  private config: ConfigManager;
  private strategies: ErrorRecoveryStrategy[] = [];
  private recoveryHistory: Map<string, number> = new Map();

  constructor(logger: Logger, aiManager: AIProviderManager, config: ConfigManager) {
    this.logger = logger;
    this.aiManager = aiManager;
    this.config = config;
    this.initializeStrategies();
  }

  private initializeStrategies(): void {
    this.strategies = [
      {
        name: 'ProviderFallback',
        priority: 1,
        canHandle: (error) => this.isProviderError(error),
        recover: async (error, context) => this.recoverWithProviderFallback(error, context)
      },
      {
        name: 'ModelFallback',
        priority: 2,
        canHandle: (error) => this.isModelError(error),
        recover: async (error, context) => this.recoverWithModelFallback(error, context)
      },
      {
        name: 'NetworkRetry',
        priority: 3,
        canHandle: (error) => this.isNetworkError(error),
        recover: async (error, context) => this.recoverWithNetworkRetry(error, context)
      },
      {
        name: 'RateLimitBackoff',
        priority: 4,
        canHandle: (error) => this.isRateLimitError(error),
        recover: async (error, context) => this.recoverWithBackoff(error, context)
      },
      {
        name: 'ConfigurationFix',
        priority: 5,
        canHandle: (error) => this.isConfigurationError(error),
        recover: async (error, context) => this.recoverWithConfigFix(error, context)
      },
      {
        name: 'GracefulDegradation',
        priority: 10,
        canHandle: () => true, // Catch-all strategy
        recover: async (error, context) => this.recoverWithGracefulDegradation(error, context)
      }
    ];

    // Sort by priority
    this.strategies.sort((a, b) => a.priority - b.priority);
  }

  public async recoverFromError(
    error: Error,
    context: ErrorContext
  ): Promise<RecoveryResult> {
    this.logger.debug(`Attempting error recovery for: ${error.message}`);

    // Find applicable strategy
    const strategy = this.strategies.find(s => s.canHandle(error));
    if (!strategy) {
      return {
        success: false,
        message: 'No recovery strategy available'
      };
    }

    try {
      // Track recovery attempts
      const recoveryKey = `${strategy.name}_${context.operation}`;
      const attempts = this.recoveryHistory.get(recoveryKey) || 0;
      this.recoveryHistory.set(recoveryKey, attempts + 1);

      this.logger.info(`🔄 Attempting recovery with strategy: ${strategy.name}`);
      
      const result = await strategy.recover(error, context);
      
      if (result.success) {
        this.logger.success(`✅ Recovery successful with ${strategy.name}`);
        // Reset recovery count on success
        this.recoveryHistory.set(recoveryKey, 0);
      } else {
        this.logger.warn(`⚠️ Recovery failed with ${strategy.name}: ${result.message}`);
      }

      return result;
    } catch (recoveryError) {
      this.logger.error(`Recovery strategy ${strategy.name} failed: ${recoveryError}`);
      return {
        success: false,
        message: `Recovery strategy failed: ${recoveryError instanceof Error ? recoveryError.message : String(recoveryError)}`
      };
    }
  }

  private isProviderError(error: Error): boolean {
    const message = error.message.toLowerCase();
    return message.includes('provider') || 
           message.includes('not found') ||
           message.includes('not configured') ||
           message.includes('unavailable');
  }

  private isModelError(error: Error): boolean {
    const message = error.message.toLowerCase();
    return message.includes('model') ||
           message.includes('invalid model') ||
           message.includes('model not found');
  }

  private isNetworkError(error: Error): boolean {
    const message = error.message.toLowerCase();
    return message.includes('network') ||
           message.includes('timeout') ||
           message.includes('connection') ||
           message.includes('econnrefused') ||
           message.includes('fetch failed');
  }

  private isRateLimitError(error: Error): boolean {
    const message = error.message.toLowerCase();
    return message.includes('rate limit') ||
           message.includes('too many requests') ||
           message.includes('quota exceeded') ||
           message.includes('429');
  }

  private isConfigurationError(error: Error): boolean {
    const message = error.message.toLowerCase();
    return message.includes('configuration') ||
           message.includes('config') ||
           message.includes('api key') ||
           message.includes('authentication');
  }

  private async recoverWithProviderFallback(error: Error, context: ErrorContext): Promise<RecoveryResult> {
    const availableProviders = await this.aiManager.getAvailableProviders();
    const currentProvider = context.provider;
    
    // Find alternative providers
    const alternativeProviders = availableProviders.filter(p => p !== currentProvider);
    
    if (alternativeProviders.length === 0) {
      return {
        success: false,
        message: 'No alternative providers available'
      };
    }

    // Try first alternative provider
    const fallbackProvider = alternativeProviders[0];
    
    try {
      // If this is a chat operation, retry with fallback provider
      if (context.originalInput && Array.isArray(context.originalInput)) {
        const response = await this.aiManager.chat(context.originalInput, fallbackProvider);
        return {
          success: true,
          result: response,
          newStrategy: `Switched to ${fallbackProvider}`,
          message: `Successfully recovered using ${fallbackProvider}`
        };
      }
      
      return {
        success: true,
        newStrategy: `Use ${fallbackProvider}`,
        message: `Fallback to ${fallbackProvider} provider`
      };
    } catch (fallbackError) {
      return {
        success: false,
        message: `Fallback provider ${fallbackProvider} also failed: ${fallbackError}`
      };
    }
  }

  private async recoverWithModelFallback(error: Error, context: ErrorContext): Promise<RecoveryResult> {
    if (!context.provider) {
      return { success: false, message: 'No provider specified for model fallback' };
    }

    const provider = this.aiManager.getProvider(context.provider);
    if (!provider) {
      return { success: false, message: 'Provider not available for model fallback' };
    }

    try {
      const models = await provider.listModels();
      const currentModel = context.model;
      const alternativeModels = models.filter(m => m !== currentModel);

      if (alternativeModels.length === 0) {
        return { success: false, message: 'No alternative models available' };
      }

      const fallbackModel = alternativeModels[0];

      if (context.originalInput && Array.isArray(context.originalInput)) {
        const response = await this.aiManager.chat(context.originalInput, context.provider, fallbackModel);
        return {
          success: true,
          result: response,
          newStrategy: `Switched to ${fallbackModel}`,
          message: `Successfully recovered using model ${fallbackModel}`
        };
      }

      return {
        success: true,
        newStrategy: `Use model ${fallbackModel}`,
        message: `Fallback to ${fallbackModel} model`
      };
    } catch (fallbackError) {
      return {
        success: false,
        message: `Model fallback failed: ${fallbackError}`
      };
    }
  }

  private async recoverWithNetworkRetry(error: Error, context: ErrorContext): Promise<RecoveryResult> {
    if (context.retryCount >= context.maxRetries) {
      return {
        success: false,
        message: 'Maximum retry attempts reached'
      };
    }

    // Exponential backoff
    const delay = Math.min(1000 * Math.pow(2, context.retryCount), 10000);
    
    this.logger.info(`⏳ Network retry in ${delay}ms (attempt ${context.retryCount + 1}/${context.maxRetries})`);
    
    await new Promise(resolve => setTimeout(resolve, delay));

    return {
      success: false,
      shouldRetry: true,
      message: `Retrying after ${delay}ms delay`
    };
  }

  private async recoverWithBackoff(error: Error, context: ErrorContext): Promise<RecoveryResult> {
    // Rate limit backoff - longer delays
    const delay = Math.min(5000 * Math.pow(2, context.retryCount), 60000);
    
    this.logger.info(`⏳ Rate limit backoff: ${delay}ms (attempt ${context.retryCount + 1}/${context.maxRetries})`);
    
    await new Promise(resolve => setTimeout(resolve, delay));

    return {
      success: false,
      shouldRetry: true,
      message: `Rate limit backoff: ${delay}ms`
    };
  }

  private async recoverWithConfigFix(error: Error, context: ErrorContext): Promise<RecoveryResult> {
    this.logger.warn('🔧 Configuration error detected. Please check your provider settings.');
    
    // Could implement automatic config validation/fixing here
    return {
      success: false,
      message: 'Configuration error - please run "goc config" to fix settings'
    };
  }

  private async recoverWithGracefulDegradation(error: Error, context: ErrorContext): Promise<RecoveryResult> {
    // Provide a graceful fallback response
    const fallbackMessage = this.generateFallbackResponse(context.operation, error);
    
    return {
      success: true,
      result: { content: fallbackMessage },
      message: 'Graceful degradation - provided fallback response'
    };
  }

  private generateFallbackResponse(operation: string, error: Error): string {
    switch (operation) {
      case 'chat':
        return `I apologize, but I'm experiencing technical difficulties right now. The error was: ${error.message}. Please try again in a moment or check your configuration with "goc config".`;
      case 'analysis':
        return `I'm unable to perform the analysis at the moment due to: ${error.message}. Please try again later.`;
      case 'code_generation':
        return `I can't generate code right now due to: ${error.message}. Please check your AI provider configuration.`;
      default:
        return `I encountered an error: ${error.message}. Please try again or check your configuration.`;
    }
  }

  public getRecoveryStats(): Record<string, number> {
    return Object.fromEntries(this.recoveryHistory);
  }

  public clearRecoveryHistory(): void {
    this.recoveryHistory.clear();
    this.logger.debug('Recovery history cleared');
  }
}
