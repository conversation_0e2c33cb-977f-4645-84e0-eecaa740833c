import { ConfigManager, UserGuidelines, ProjectContext } from '../config/ConfigManager';
import { Logger } from '../utils/Logger';
import fs from 'fs';
import path from 'path';

export interface GuidelinesContext {
  userGuidelines: UserGuidelines;
  projectGuidelines?: Partial<UserGuidelines>;
  effectiveGuidelines: UserGuidelines;
  projectContext?: ProjectContext;
}

export class GuidelinesManager {
  private config: ConfigManager;
  private logger: Logger;

  constructor(config: ConfigManager, logger: Logger) {
    this.config = config;
    this.logger = logger;
  }

  public getGuidelinesContext(projectPath?: string): GuidelinesContext {
    const userGuidelines = this.config.getConfig().userGuidelines;
    let projectContext: ProjectContext | undefined;
    let projectGuidelines: Partial<UserGuidelines> | undefined;

    if (projectPath) {
      projectContext = this.config.getConfig().projects.find(p => p.path === projectPath);
      projectGuidelines = projectContext?.guidelines;
    }

    const effectiveGuidelines = this.config.getEffectiveGuidelines();

    return {
      userGuidelines,
      projectGuidelines,
      effectiveGuidelines,
      projectContext
    };
  }

  public generateGuidelinesPrompt(projectPath?: string, mentionedFiles?: string[]): string {
    const context = this.getGuidelinesContext(projectPath);
    const guidelines = context.effectiveGuidelines;

    let prompt = `## User Guidelines & Preferences

**Coding Style:**
- Language: ${guidelines.codingStyle.language}
- Framework: ${guidelines.codingStyle.framework}
- Conventions: ${guidelines.codingStyle.conventions.join(', ')}
- Patterns: ${guidelines.codingStyle.patterns.join(', ')}

**Preferences:**
- Testing: ${guidelines.preferences.testingFramework}
- Documentation: ${guidelines.preferences.documentationStyle}
- Error Handling: ${guidelines.preferences.errorHandling}
- Logging: ${guidelines.preferences.logging ? 'Enabled' : 'Disabled'}
- Backward Compatibility: ${guidelines.preferences.backwardCompatibility ? 'Required' : 'Not Required'}

**Project Rules:**
- File Naming: ${guidelines.projectRules.fileNaming}
- Directory Structure: ${guidelines.projectRules.directoryStructure.join(', ')}
- Dependencies: ${guidelines.projectRules.dependencies.join(', ') || 'None specified'}

**Custom Instructions:**
${guidelines.customInstructions.map(instruction => `- ${instruction}`).join('\n')}
`;

    if (context.projectContext) {
      prompt += `\n**Current Project:** ${context.projectContext.name} (${context.projectContext.type})`;
      prompt += `\n**Project Path:** ${context.projectContext.path}`;
      prompt += `\n**Framework Version:** ${context.projectContext.version}`;
    }

    if (mentionedFiles && mentionedFiles.length > 0) {
      prompt += `\n**Files Mentioned:** ${mentionedFiles.join(', ')}`;
    }

    prompt += `\n\n**Important:** Follow these guidelines strictly when generating code or making suggestions.`;

    return prompt;
  }

  public detectProjectType(projectPath: string): ProjectContext['type'] {
    try {
      const packageJsonPath = path.join(projectPath, 'package.json');
      const composerJsonPath = path.join(projectPath, 'composer.json');
      const requirementsTxtPath = path.join(projectPath, 'requirements.txt');

      if (fs.existsSync(composerJsonPath)) {
        const composer = JSON.parse(fs.readFileSync(composerJsonPath, 'utf8'));
        if (composer.require && composer.require['laravel/framework']) {
          return 'laravel';
        }
      }

      if (fs.existsSync(packageJsonPath)) {
        const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
        if (packageJson.dependencies) {
          if (packageJson.dependencies.react || packageJson.dependencies['@types/react']) {
            return 'react';
          }
          if (packageJson.dependencies.express || packageJson.dependencies.fastify) {
            return 'node';
          }
        }
      }

      if (fs.existsSync(requirementsTxtPath)) {
        return 'python';
      }

      return 'generic';
    } catch (error) {
      this.logger.debug(`Failed to detect project type: ${error}`);
      return 'generic';
    }
  }

  public autoDetectProjectInfo(projectPath: string): Omit<ProjectContext, 'lastAccessed'> {
    const projectName = path.basename(projectPath);
    const projectType = this.detectProjectType(projectPath);
    let framework = 'unknown';
    let version = 'unknown';

    try {
      if (projectType === 'laravel') {
        const composerPath = path.join(projectPath, 'composer.json');
        if (fs.existsSync(composerPath)) {
          const composer = JSON.parse(fs.readFileSync(composerPath, 'utf8'));
          framework = 'Laravel';
          version = composer.require?.['laravel/framework']?.replace(/[^0-9.]/g, '') || 'unknown';
        }
      } else if (projectType === 'react' || projectType === 'node') {
        const packagePath = path.join(projectPath, 'package.json');
        if (fs.existsSync(packagePath)) {
          const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
          if (projectType === 'react') {
            framework = 'React';
            version = packageJson.dependencies?.react?.replace(/[^0-9.]/g, '') || 'unknown';
          } else {
            framework = 'Node.js';
            version = packageJson.engines?.node?.replace(/[^0-9.]/g, '') || 'unknown';
          }
        }
      }
    } catch (error) {
      this.logger.debug(`Failed to detect project info: ${error}`);
    }

    return {
      name: projectName,
      path: projectPath,
      type: projectType,
      framework,
      version,
      guidelines: this.getDefaultProjectGuidelines(projectType)
    };
  }

  private getDefaultProjectGuidelines(projectType: ProjectContext['type']): Partial<UserGuidelines> {
    switch (projectType) {
      case 'laravel':
        return {
          codingStyle: {
            language: 'php',
            framework: 'laravel',
            conventions: ['PSR-4', 'camelCase for methods', 'PascalCase for classes'],
            patterns: ['MVC', 'Repository Pattern', 'Service Layer']
          },
          preferences: {
            testingFramework: 'phpunit',
            documentationStyle: 'phpdoc',
            errorHandling: 'exceptions',
            logging: true,
            backwardCompatibility: false
          }
        };
      case 'react':
        return {
          codingStyle: {
            language: 'typescript',
            framework: 'react',
            conventions: ['camelCase', 'PascalCase for components'],
            patterns: ['Hooks', 'Component Composition']
          },
          preferences: {
            testingFramework: 'jest',
            documentationStyle: 'jsdoc',
            errorHandling: 'try-catch',
            logging: false,
            backwardCompatibility: false
          }
        };
      case 'node':
        return {
          codingStyle: {
            language: 'typescript',
            framework: 'express',
            conventions: ['camelCase', 'kebab-case for routes'],
            patterns: ['MVC', 'Middleware Pattern']
          },
          preferences: {
            testingFramework: 'jest',
            documentationStyle: 'jsdoc',
            errorHandling: 'try-catch',
            logging: true,
            backwardCompatibility: false
          }
        };
      default:
        return {};
    }
  }

  public extractMentionedFiles(text: string): string[] {
    const filePatterns = [
      /[\w\-\.\/]+\.(ts|js|tsx|jsx|php|py|java|cpp|c|h|cs|rb|go|rs|md|json|yaml|yml)/g,
      /(?:file|path|directory):\s*([\w\-\.\/]+)/gi,
      /`([^`]+\.(ts|js|tsx|jsx|php|py|java|cpp|c|h|cs|rb|go|rs|md|json|yaml|yml))`/g
    ];

    const files = new Set<string>();
    
    filePatterns.forEach(pattern => {
      const matches = text.match(pattern);
      if (matches) {
        matches.forEach(match => {
          // Clean up the match
          const cleaned = match.replace(/[`'"]/g, '').trim();
          if (cleaned.length > 0) {
            files.add(cleaned);
          }
        });
      }
    });

    return Array.from(files);
  }
}
