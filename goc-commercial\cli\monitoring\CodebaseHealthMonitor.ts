import { Logger } from '../utils/Logger';
import { CodebaseAnalyzer } from '../codebase/CodebaseAnalyzer';
import { AIProviderManager } from '../ai/AIProviderManager';
import fs from 'fs';
import path from 'path';

export interface HealthMetric {
  name: string;
  value: number;
  status: 'excellent' | 'good' | 'warning' | 'critical';
  description: string;
  suggestions?: string[];
}

export interface CodeQualityReport {
  overallScore: number;
  metrics: HealthMetric[];
  trends: {
    metric: string;
    change: number;
    period: string;
  }[];
  recommendations: string[];
  timestamp: Date;
}

export interface FileHealthInfo {
  path: string;
  size: number;
  complexity: number;
  maintainabilityIndex: number;
  testCoverage?: number;
  issues: string[];
  lastModified: Date;
}

export class CodebaseHealthMonitor {
  private logger: Logger;
  private codebaseAnalyzer: CodebaseAnalyzer;
  private aiManager: AIProviderManager;
  private healthHistory: CodeQualityReport[] = [];
  private readonly historyFile: string;

  constructor(
    logger: Logger,
    codebaseAnalyzer: Codebase<PERSON><PERSON>y<PERSON>,
    aiManager: AIProviderManager
  ) {
    this.logger = logger;
    this.codebaseAnalyzer = codebaseAnalyzer;
    this.aiManager = aiManager;
    this.historyFile = path.join(process.cwd(), '.goc-agent', 'health-history.json');
    this.loadHealthHistory();
  }

  public async generateHealthReport(): Promise<CodeQualityReport> {
    this.logger.info('🏥 Analyzing codebase health...');

    const context = await this.codebaseAnalyzer.scanCodebase();
    const metrics = await this.calculateHealthMetrics(context);
    const trends = this.calculateTrends(metrics);
    const recommendations = await this.generateRecommendations(metrics, context);

    const report: CodeQualityReport = {
      overallScore: this.calculateOverallScore(metrics),
      metrics,
      trends,
      recommendations,
      timestamp: new Date()
    };

    this.healthHistory.push(report);
    this.saveHealthHistory();

    this.logger.success(`🏥 Health report generated - Overall Score: ${report.overallScore}/100`);
    return report;
  }

  private async calculateHealthMetrics(context: any): Promise<HealthMetric[]> {
    const metrics: HealthMetric[] = [];

    // Code size metrics
    metrics.push(this.calculateCodeSizeMetric(context));
    
    // File organization metrics
    metrics.push(this.calculateFileOrganizationMetric(context));
    
    // Complexity metrics
    metrics.push(await this.calculateComplexityMetric(context));
    
    // Documentation metrics
    metrics.push(await this.calculateDocumentationMetric(context));
    
    // Dependency metrics
    metrics.push(await this.calculateDependencyMetric(context));
    
    // Code duplication metrics
    metrics.push(await this.calculateDuplicationMetric(context));

    return metrics;
  }

  private calculateCodeSizeMetric(context: any): HealthMetric {
    const avgFileSize = context.totalSize / context.totalFiles;
    const largeFiles = context.files?.filter((f: any) => f.size > 50000).length || 0;
    
    let status: 'excellent' | 'good' | 'warning' | 'critical' = 'excellent';
    let suggestions: string[] = [];

    if (avgFileSize > 20000) {
      status = 'warning';
      suggestions.push('Consider breaking down large files into smaller modules');
    }

    if (largeFiles > context.totalFiles * 0.1) {
      status = 'critical';
      suggestions.push('Too many large files detected - refactor for better maintainability');
    }

    return {
      name: 'Code Size',
      value: Math.max(0, 100 - (avgFileSize / 1000)),
      status,
      description: `Average file size: ${(avgFileSize / 1000).toFixed(1)}KB, Large files: ${largeFiles}`,
      suggestions
    };
  }

  private calculateFileOrganizationMetric(context: any): HealthMetric {
    const hasProperStructure = context.structure.includes('src/') || context.structure.includes('lib/');
    const hasTests = context.structure.includes('test') || context.structure.includes('spec');
    const hasConfig = context.structure.includes('config') || context.files?.some((f: any) => 
      f.relativePath.includes('package.json') || f.relativePath.includes('tsconfig.json')
    );

    let score = 0;
    const suggestions: string[] = [];

    if (hasProperStructure) score += 40;
    else suggestions.push('Consider organizing code in src/ or lib/ directory');

    if (hasTests) score += 30;
    else suggestions.push('Add test directory and test files');

    if (hasConfig) score += 30;
    else suggestions.push('Add configuration files for better project setup');

    return {
      name: 'File Organization',
      value: score,
      status: score >= 80 ? 'excellent' : score >= 60 ? 'good' : score >= 40 ? 'warning' : 'critical',
      description: `Structure: ${hasProperStructure ? '✅' : '❌'}, Tests: ${hasTests ? '✅' : '❌'}, Config: ${hasConfig ? '✅' : '❌'}`,
      suggestions
    };
  }

  private async calculateComplexityMetric(context: any): Promise<HealthMetric> {
    // Simplified complexity calculation based on file analysis
    let totalComplexity = 0;
    let fileCount = 0;

    for (const file of context.files?.slice(0, 10) || []) {
      try {
        const content = await this.codebaseAnalyzer.getFileContent(file.relativePath);
        const complexity = this.estimateFileComplexity(content);
        totalComplexity += complexity;
        fileCount++;
      } catch (error) {
        // Skip files that can't be read
      }
    }

    const avgComplexity = fileCount > 0 ? totalComplexity / fileCount : 0;
    const suggestions: string[] = [];

    if (avgComplexity > 15) {
      suggestions.push('High complexity detected - consider refactoring complex functions');
    }

    return {
      name: 'Code Complexity',
      value: Math.max(0, 100 - avgComplexity * 2),
      status: avgComplexity < 10 ? 'excellent' : avgComplexity < 15 ? 'good' : avgComplexity < 20 ? 'warning' : 'critical',
      description: `Average complexity: ${avgComplexity.toFixed(1)}`,
      suggestions
    };
  }

  private async calculateDocumentationMetric(context: any): Promise<HealthMetric> {
    const hasReadme = context.files?.some((f: any) => f.relativePath.toLowerCase().includes('readme'));
    const hasComments = await this.checkForComments(context);
    const hasApiDocs = context.files?.some((f: any) => 
      f.relativePath.includes('docs/') || f.relativePath.includes('.md')
    );

    let score = 0;
    const suggestions: string[] = [];

    if (hasReadme) score += 40;
    else suggestions.push('Add a README.md file');

    if (hasComments) score += 40;
    else suggestions.push('Add more code comments and documentation');

    if (hasApiDocs) score += 20;
    else suggestions.push('Consider adding API documentation');

    return {
      name: 'Documentation',
      value: score,
      status: score >= 80 ? 'excellent' : score >= 60 ? 'good' : score >= 40 ? 'warning' : 'critical',
      description: `README: ${hasReadme ? '✅' : '❌'}, Comments: ${hasComments ? '✅' : '❌'}, Docs: ${hasApiDocs ? '✅' : '❌'}`,
      suggestions
    };
  }

  private async calculateDependencyMetric(context: any): Promise<HealthMetric> {
    // Check for package.json or similar dependency files
    const packageFile = context.files?.find((f: any) => f.relativePath.includes('package.json'));
    
    if (!packageFile) {
      return {
        name: 'Dependencies',
        value: 100,
        status: 'excellent',
        description: 'No dependency management detected',
        suggestions: []
      };
    }

    try {
      const content = await this.codebaseAnalyzer.getFileContent(packageFile.relativePath);
      const packageJson = JSON.parse(content);
      const depCount = Object.keys(packageJson.dependencies || {}).length;
      const devDepCount = Object.keys(packageJson.devDependencies || {}).length;
      
      const suggestions: string[] = [];
      if (depCount > 50) {
        suggestions.push('Consider reducing the number of dependencies');
      }

      return {
        name: 'Dependencies',
        value: Math.max(0, 100 - depCount),
        status: depCount < 20 ? 'excellent' : depCount < 40 ? 'good' : depCount < 60 ? 'warning' : 'critical',
        description: `Dependencies: ${depCount}, Dev Dependencies: ${devDepCount}`,
        suggestions
      };
    } catch (error) {
      return {
        name: 'Dependencies',
        value: 50,
        status: 'warning',
        description: 'Could not analyze dependencies',
        suggestions: ['Check package.json format']
      };
    }
  }

  private async calculateDuplicationMetric(context: any): Promise<HealthMetric> {
    // Simplified duplication detection
    const fileHashes = new Map<string, string[]>();
    
    for (const file of context.files?.slice(0, 20) || []) {
      try {
        const content = await this.codebaseAnalyzer.getFileContent(file.relativePath);
        const hash = this.simpleHash(content);
        
        if (!fileHashes.has(hash)) {
          fileHashes.set(hash, []);
        }
        fileHashes.get(hash)!.push(file.relativePath);
      } catch (error) {
        // Skip files that can't be read
      }
    }

    const duplicates = Array.from(fileHashes.values()).filter(files => files.length > 1);
    const duplicationRate = duplicates.length / fileHashes.size;

    return {
      name: 'Code Duplication',
      value: Math.max(0, 100 - duplicationRate * 100),
      status: duplicationRate < 0.1 ? 'excellent' : duplicationRate < 0.2 ? 'good' : duplicationRate < 0.3 ? 'warning' : 'critical',
      description: `Duplication rate: ${(duplicationRate * 100).toFixed(1)}%`,
      suggestions: duplicationRate > 0.2 ? ['Consider refactoring duplicated code'] : []
    };
  }

  private estimateFileComplexity(content: string): number {
    // Simple complexity estimation based on control structures
    const lines = content.split('\n');
    let complexity = 1; // Base complexity

    for (const line of lines) {
      const trimmed = line.trim();
      
      // Count control structures
      if (trimmed.includes('if ') || trimmed.includes('else')) complexity++;
      if (trimmed.includes('for ') || trimmed.includes('while ')) complexity++;
      if (trimmed.includes('switch ') || trimmed.includes('case ')) complexity++;
      if (trimmed.includes('catch ') || trimmed.includes('try ')) complexity++;
      if (trimmed.includes('&&') || trimmed.includes('||')) complexity++;
    }

    return complexity;
  }

  private async checkForComments(context: any): Promise<boolean> {
    // Check a few files for comments
    for (const file of context.files?.slice(0, 5) || []) {
      try {
        const content = await this.codebaseAnalyzer.getFileContent(file.relativePath);
        if (content.includes('//') || content.includes('/*') || content.includes('#')) {
          return true;
        }
      } catch (error) {
        // Skip files that can't be read
      }
    }
    return false;
  }

  private simpleHash(content: string): string {
    // Simple hash function for duplicate detection
    let hash = 0;
    for (let i = 0; i < content.length; i++) {
      const char = content.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return hash.toString();
  }

  private calculateOverallScore(metrics: HealthMetric[]): number {
    const totalScore = metrics.reduce((sum, metric) => sum + metric.value, 0);
    return Math.round(totalScore / metrics.length);
  }

  private calculateTrends(currentMetrics: HealthMetric[]): { metric: string; change: number; period: string; }[] {
    if (this.healthHistory.length < 2) {
      return [];
    }

    const previousReport = this.healthHistory[this.healthHistory.length - 1];
    const trends = [];

    for (const metric of currentMetrics) {
      const previousMetric = previousReport.metrics.find(m => m.name === metric.name);
      if (previousMetric) {
        const change = metric.value - previousMetric.value;
        trends.push({
          metric: metric.name,
          change,
          period: '1 report'
        });
      }
    }

    return trends;
  }

  private async generateRecommendations(metrics: HealthMetric[], context: any): Promise<string[]> {
    const recommendations: string[] = [];

    // Collect suggestions from metrics
    for (const metric of metrics) {
      if (metric.suggestions) {
        recommendations.push(...metric.suggestions);
      }
    }

    // Add general recommendations based on overall health
    const overallScore = this.calculateOverallScore(metrics);
    
    if (overallScore < 60) {
      recommendations.push('Consider a comprehensive code review and refactoring session');
    }

    if (overallScore < 40) {
      recommendations.push('Urgent: Address critical code quality issues immediately');
    }

    return [...new Set(recommendations)]; // Remove duplicates
  }

  private loadHealthHistory(): void {
    try {
      if (fs.existsSync(this.historyFile)) {
        const data = fs.readFileSync(this.historyFile, 'utf8');
        this.healthHistory = JSON.parse(data);
      }
    } catch (error) {
      this.logger.debug('Could not load health history:', error);
      this.healthHistory = [];
    }
  }

  private saveHealthHistory(): void {
    try {
      const dir = path.dirname(this.historyFile);
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }

      // Keep only last 10 reports
      const recentHistory = this.healthHistory.slice(-10);
      fs.writeFileSync(this.historyFile, JSON.stringify(recentHistory, null, 2));
    } catch (error) {
      this.logger.debug('Could not save health history:', error);
    }
  }

  public getHealthHistory(): CodeQualityReport[] {
    return [...this.healthHistory];
  }

  public clearHealthHistory(): void {
    this.healthHistory = [];
    this.saveHealthHistory();
  }
}
