<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('password')->nullable()->change(); // Make password nullable for OAuth users
            $table->string('avatar')->nullable()->after('password');
            $table->string('github_id')->nullable()->after('avatar');
            $table->string('google_id')->nullable()->after('github_id');
            $table->string('provider')->nullable()->after('google_id'); // oauth provider
            $table->string('provider_id')->nullable()->after('provider');
            $table->json('provider_data')->nullable()->after('provider_id');
            $table->boolean('is_active')->default(true)->after('preferences');
            $table->timestamp('last_login_at')->nullable()->after('is_active');
            $table->string('timezone')->default('UTC')->after('last_login_at');

            $table->index(['provider', 'provider_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropIndex(['provider', 'provider_id']);
            $table->dropColumn([
                'avatar',
                'github_id',
                'google_id',
                'provider',
                'provider_id',
                'provider_data',
                'is_active',
                'last_login_at',
                'timezone'
            ]);
        });
    }
};
