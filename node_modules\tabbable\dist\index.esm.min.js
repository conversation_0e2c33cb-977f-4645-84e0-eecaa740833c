/*!
* tabbable 5.3.3
* @license MIT, https://github.com/focus-trap/tabbable/blob/master/LICENSE
*/
var e=["input","select","textarea","a[href]","button","[tabindex]:not(slot)","audio[controls]","video[controls]",'[contenteditable]:not([contenteditable="false"])',"details>summary:first-of-type","details"],t=e.join(","),n="undefined"==typeof Element,o=n?function(){}:Element.prototype.matches||Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector,r=!n&&Element.prototype.getRootNode?function(e){return e.getRootNode()}:function(e){return e.ownerDocument},a=function(e,n,r){var a=Array.prototype.slice.apply(e.querySelectorAll(t));return n&&o.call(e,t)&&a.unshift(e),a=a.filter(r)},i=function e(n,r,a){for(var i=[],u=Array.from(n);u.length;){var l=u.shift();if("SLOT"===l.tagName){var c=l.assignedElements(),d=e(c.length?c:l.children,!0,a);a.flatten?i.push.apply(i,d):i.push({scope:l,candidates:d})}else{o.call(l,t)&&a.filter(l)&&(r||!n.includes(l))&&i.push(l);var s=l.shadowRoot||"function"==typeof a.getShadowRoot&&a.getShadowRoot(l),f=!a.shadowRootFilter||a.shadowRootFilter(l);if(s&&f){var p=e(!0===s?l.children:s.children,!0,a);a.flatten?i.push.apply(i,p):i.push({scope:l,candidates:p})}else u.unshift.apply(u,l.children)}}return i},u=function(e,t){return e.tabIndex<0&&(t||/^(AUDIO|VIDEO|DETAILS)$/.test(e.tagName)||e.isContentEditable)&&isNaN(parseInt(e.getAttribute("tabindex"),10))?0:e.tabIndex},l=function(e,t){return e.tabIndex===t.tabIndex?e.documentOrder-t.documentOrder:e.tabIndex-t.tabIndex},c=function(e){return"INPUT"===e.tagName},d=function(e){return function(e){return c(e)&&"radio"===e.type}(e)&&!function(e){if(!e.name)return!0;var t,n=e.form||r(e),o=function(e){return n.querySelectorAll('input[type="radio"][name="'+e+'"]')};if("undefined"!=typeof window&&void 0!==window.CSS&&"function"==typeof window.CSS.escape)t=o(window.CSS.escape(e.name));else try{t=o(e.name)}catch(e){return console.error("Looks like you have a radio button with a name attribute containing invalid CSS selector characters and need the CSS.escape polyfill: %s",e.message),!1}var a=function(e,t){for(var n=0;n<e.length;n++)if(e[n].checked&&e[n].form===t)return e[n]}(t,e.form);return!a||a===e}(e)},s=function(e){var t=e.getBoundingClientRect(),n=t.width,o=t.height;return 0===n&&0===o},f=function(e,t){return!(t.disabled||function(e){return c(e)&&"hidden"===e.type}(t)||function(e,t){var n=t.displayCheck,a=t.getShadowRoot;if("hidden"===getComputedStyle(e).visibility)return!0;var i=o.call(e,"details>summary:first-of-type")?e.parentElement:e;if(o.call(i,"details:not([open]) *"))return!0;var u=r(e).host,l=(null==u?void 0:u.ownerDocument.contains(u))||e.ownerDocument.contains(e);if(n&&"full"!==n){if("non-zero-area"===n)return s(e)}else{if("function"==typeof a){for(var c=e;e;){var d=e.parentElement,f=r(e);if(d&&!d.shadowRoot&&!0===a(d))return s(e);e=e.assignedSlot?e.assignedSlot:d||f===e.ownerDocument?d:f.host}e=c}if(l)return!e.getClientRects().length}return!1}(t,e)||function(e){return"DETAILS"===e.tagName&&Array.prototype.slice.apply(e.children).some((function(e){return"SUMMARY"===e.tagName}))}(t)||function(e){if(/^(INPUT|BUTTON|SELECT|TEXTAREA)$/.test(e.tagName))for(var t=e.parentElement;t;){if("FIELDSET"===t.tagName&&t.disabled){for(var n=0;n<t.children.length;n++){var r=t.children.item(n);if("LEGEND"===r.tagName)return!!o.call(t,"fieldset[disabled] *")||!r.contains(e)}return!0}t=t.parentElement}return!1}(t))},p=function(e,t){return!(d(t)||u(t)<0||!f(e,t))},h=function(e){var t=parseInt(e.getAttribute("tabindex"),10);return!!(isNaN(t)||t>=0)},m=function(e,t){return function e(t){var n=[],o=[];return t.forEach((function(t,r){var a=!!t.scope,i=a?t.scope:t,l=u(i,a),c=a?e(t.candidates):i;0===l?a?n.push.apply(n,c):n.push(i):o.push({documentOrder:r,tabIndex:l,item:t,isScope:a,content:c})})),o.sort(l).reduce((function(e,t){return t.isScope?e.push.apply(e,t.content):e.push(t.content),e}),[]).concat(n)}((t=t||{}).getShadowRoot?i([e],t.includeContainer,{filter:p.bind(null,t),flatten:!1,getShadowRoot:t.getShadowRoot,shadowRootFilter:h}):a(e,t.includeContainer,p.bind(null,t)))},g=function(e,t){return(t=t||{}).getShadowRoot?i([e],t.includeContainer,{filter:f.bind(null,t),flatten:!0,getShadowRoot:t.getShadowRoot}):a(e,t.includeContainer,f.bind(null,t))},y=function(e,n){if(n=n||{},!e)throw new Error("No node provided");return!1!==o.call(e,t)&&p(n,e)},S=e.concat("iframe").join(","),w=function(e,t){if(t=t||{},!e)throw new Error("No node provided");return!1!==o.call(e,S)&&f(t,e)};export{g as focusable,w as isFocusable,y as isTabbable,m as tabbable};
//# sourceMappingURL=index.esm.min.js.map
