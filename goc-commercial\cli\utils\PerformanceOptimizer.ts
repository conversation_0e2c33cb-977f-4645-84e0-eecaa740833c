import { Logger } from './Logger';

export interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number;
  accessCount: number;
}

export interface PerformanceMetrics {
  cacheHitRate: number;
  averageResponseTime: number;
  memoryUsage: number;
  operationCounts: Map<string, number>;
}

export class PerformanceOptimizer {
  private cache: Map<string, CacheEntry<any>> = new Map();
  private logger: Logger;
  private metrics: PerformanceMetrics;
  private operationTimes: Map<string, number[]> = new Map();
  private readonly maxCacheSize = 1000;
  private readonly defaultTtl = 300000; // 5 minutes

  constructor(logger: Logger) {
    this.logger = logger;
    this.metrics = {
      cacheHitRate: 0,
      averageResponseTime: 0,
      memoryUsage: 0,
      operationCounts: new Map()
    };

    // Periodic cache cleanup
    setInterval(() => this.cleanupCache(), 60000); // Every minute
  }

  public async withCache<T>(
    key: string,
    operation: () => Promise<T>,
    ttl: number = this.defaultTtl
  ): Promise<T> {
    const cached = this.getFromCache<T>(key);
    if (cached) {
      this.incrementOperationCount('cache_hit');
      return cached;
    }

    const startTime = Date.now();
    try {
      const result = await operation();
      const endTime = Date.now();
      
      this.setCache(key, result, ttl);
      this.recordOperationTime('cache_miss', endTime - startTime);
      this.incrementOperationCount('cache_miss');
      
      return result;
    } catch (error) {
      this.incrementOperationCount('cache_error');
      throw error;
    }
  }

  public async withPerformanceTracking<T>(
    operationName: string,
    operation: () => Promise<T>
  ): Promise<T> {
    const startTime = Date.now();
    const startMemory = process.memoryUsage().heapUsed;

    try {
      const result = await operation();
      const endTime = Date.now();
      const endMemory = process.memoryUsage().heapUsed;
      
      this.recordOperationTime(operationName, endTime - startTime);
      this.incrementOperationCount(operationName);
      
      // Log performance if operation took too long
      const duration = endTime - startTime;
      if (duration > 5000) { // 5 seconds
        this.logger.warn(`Slow operation detected: ${operationName} took ${duration}ms`);
      }
      
      return result;
    } catch (error) {
      this.incrementOperationCount(`${operationName}_error`);
      throw error;
    }
  }

  public debounce<T extends (...args: any[]) => any>(
    func: T,
    delay: number
  ): (...args: Parameters<T>) => void {
    let timeoutId: NodeJS.Timeout;
    
    return (...args: Parameters<T>) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => func(...args), delay);
    };
  }

  public throttle<T extends (...args: any[]) => any>(
    func: T,
    limit: number
  ): (...args: Parameters<T>) => void {
    let inThrottle: boolean;
    
    return (...args: Parameters<T>) => {
      if (!inThrottle) {
        func(...args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  }

  private getFromCache<T>(key: string): T | null {
    const entry = this.cache.get(key);
    if (!entry) return null;

    const now = Date.now();
    if (now - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      return null;
    }

    entry.accessCount++;
    return entry.data;
  }

  private setCache<T>(key: string, data: T, ttl: number): void {
    // Implement LRU eviction if cache is full
    if (this.cache.size >= this.maxCacheSize) {
      this.evictLeastRecentlyUsed();
    }

    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl,
      accessCount: 1
    });
  }

  private evictLeastRecentlyUsed(): void {
    let oldestKey = '';
    let oldestTime = Date.now();

    for (const [key, entry] of this.cache.entries()) {
      if (entry.timestamp < oldestTime) {
        oldestTime = entry.timestamp;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey);
    }
  }

  private cleanupCache(): void {
    const now = Date.now();
    const keysToDelete: string[] = [];

    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        keysToDelete.push(key);
      }
    }

    keysToDelete.forEach(key => this.cache.delete(key));
    
    if (keysToDelete.length > 0) {
      this.logger.debug(`Cleaned up ${keysToDelete.length} expired cache entries`);
    }
  }

  private recordOperationTime(operation: string, time: number): void {
    if (!this.operationTimes.has(operation)) {
      this.operationTimes.set(operation, []);
    }
    
    const times = this.operationTimes.get(operation)!;
    times.push(time);
    
    // Keep only last 100 measurements
    if (times.length > 100) {
      times.shift();
    }
  }

  private incrementOperationCount(operation: string): void {
    const current = this.metrics.operationCounts.get(operation) || 0;
    this.metrics.operationCounts.set(operation, current + 1);
  }

  public getMetrics(): PerformanceMetrics {
    const cacheHits = this.metrics.operationCounts.get('cache_hit') || 0;
    const cacheMisses = this.metrics.operationCounts.get('cache_miss') || 0;
    const totalCacheOperations = cacheHits + cacheMisses;
    
    this.metrics.cacheHitRate = totalCacheOperations > 0 ? cacheHits / totalCacheOperations : 0;
    this.metrics.memoryUsage = process.memoryUsage().heapUsed / 1024 / 1024; // MB
    
    // Calculate average response time
    let totalTime = 0;
    let totalOperations = 0;
    
    for (const times of this.operationTimes.values()) {
      totalTime += times.reduce((sum, time) => sum + time, 0);
      totalOperations += times.length;
    }
    
    this.metrics.averageResponseTime = totalOperations > 0 ? totalTime / totalOperations : 0;
    
    return { ...this.metrics };
  }

  public clearCache(): void {
    this.cache.clear();
    this.logger.debug('Performance cache cleared');
  }

  public getCacheStats(): { size: number; hitRate: number; memoryUsage: number } {
    const metrics = this.getMetrics();
    return {
      size: this.cache.size,
      hitRate: metrics.cacheHitRate,
      memoryUsage: metrics.memoryUsage
    };
  }
}
