import fs from 'fs';
import path from 'path';
import { FileChange, FileChangeAnalyzer } from './FileChangeAnalyzer';
import { Logger } from './Logger';

export interface ChangesSummary {
  totalFiles: number;
  totalLinesAdded: number;
  totalLinesDeleted: number;
  changes: FileChange[];
}

export interface RevertResult {
  success: number;
  failed: number;
}

export type FileOperation = 'creating' | 'editing' | 'deleting';
export type FileOperationResult = 'created' | 'edited' | 'deleted';

export class ChangeTracker {
  private changes: FileChange[] = [];
  private logger: Logger;
  private sessionId: string;
  private autoTrainingCallback?: (operation: string, filePath: string, success: boolean) => Promise<void>;

  constructor(logger: Logger) {
    this.logger = logger;
    this.sessionId = Date.now().toString();
  }

  public setAutoTrainingCallback(callback: (operation: string, filePath: string, success: boolean) => Promise<void>): void {
    this.autoTrainingCallback = callback;
  }
  
  public trackFileOperation(
    filePath: string,
    operation: FileOperation,
    originalContent?: string
  ): void {
    // Show real-time operation to user
    this.logger.fileOperation(operation, path.relative(process.cwd(), filePath));
  }
  
  public async recordFileChange(
    filePath: string,
    operation: FileOperationResult,
    originalContent?: string,
    newContent?: string,
    backupPath?: string
  ): Promise<FileChange> {
    const change = FileChangeAnalyzer.analyzeFileChange(
      path.relative(process.cwd(), filePath),
      originalContent || null,
      newContent || null,
      operation
    );
    
    if (backupPath) {
      change.backupPath = backupPath;
    }
    
    this.changes.push(change);
    
    // Show immediate feedback for the completed operation
    const icon = operation === 'created' ? '✅' : operation === 'edited' ? '✅' : '🗑️';
    const addedText = change.linesAdded > 0 ? ` (+${change.linesAdded})` : '';
    const deletedText = change.linesDeleted > 0 ? ` (-${change.linesDeleted})` : '';
    
    this.logger.success(`${icon} ${operation} ${change.filename}${addedText}${deletedText}`);

    // Auto-training: Learn from file operation
    if (this.autoTrainingCallback) {
      await this.autoTrainingCallback(operation, filePath, true);
    }

    return change;
  }
  
  public getSessionChanges(): FileChange[] {
    return [...this.changes];
  }
  
  public getChangesSummary(): ChangesSummary {
    const totalLinesAdded = this.changes.reduce((sum, change) => sum + change.linesAdded, 0);
    const totalLinesDeleted = this.changes.reduce((sum, change) => sum + change.linesDeleted, 0);
    
    return {
      totalFiles: this.changes.length,
      totalLinesAdded,
      totalLinesDeleted,
      changes: this.changes
    };
  }
  
  public displayChangesSummary(): void {
    if (this.changes.length === 0) {
      this.logger.info('No changes recorded in this session');
      return;
    }
    
    this.logger.changesSummary(this.changes.map(change => ({
      filename: change.filename,
      linesAdded: change.linesAdded,
      linesDeleted: change.linesDeleted,
      operation: change.operation
    })));
  }
  
  public async revertFile(filename: string): Promise<boolean> {
    const change = this.changes.find(c => c.filename === filename);
    
    if (!change) {
      this.logger.error(`No changes found for file: ${filename}`);
      return false;
    }
    
    if (!change.backupPath || !fs.existsSync(change.backupPath)) {
      this.logger.error(`No backup available for file: ${filename}`);
      return false;
    }
    
    try {
      this.logger.revertOperation(filename);
      
      const fullPath = path.resolve(process.cwd(), filename);
      FileChangeAnalyzer.restoreFromBackup(fullPath, change.backupPath);
      
      // Remove the change from tracking
      const index = this.changes.indexOf(change);
      this.changes.splice(index, 1);
      
      this.logger.success(`✅ Reverted ${filename}`);
      return true;
    } catch (error) {
      this.logger.error(`Failed to revert ${filename}: ${error instanceof Error ? error.message : String(error)}`);
      return false;
    }
  }
  
  public async revertAllChanges(): Promise<RevertResult> {
    let success = 0;
    let failed = 0;
    
    // Create a copy of changes array since revertFile modifies it
    const changesToRevert = [...this.changes];
    
    for (const change of changesToRevert) {
      const result = await this.revertFile(change.filename);
      if (result) {
        success++;
      } else {
        failed++;
      }
    }
    
    return { success, failed };
  }
  
  public clearSession(): void {
    this.changes = [];
    this.sessionId = Date.now().toString();
    this.logger.info('Change tracking session cleared');
  }
  
  public getSessionId(): string {
    return this.sessionId;
  }
  
  public hasChanges(): boolean {
    return this.changes.length > 0;
  }
}
