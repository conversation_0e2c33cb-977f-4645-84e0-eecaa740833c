// Context Engine Factory - Creates and manages different context engine implementations

import { Logger } from '../utils/Logger';
import { IContextEngine, IContextEngineFactory, ContextEngineConfig } from './IContextEngine';
import { AugmentContextEngine } from './engines/AugmentContextEngine';
import { TreeSitterContextEngine } from './engines/TreeSitterContextEngine';
import { CodeBERTContextEngine } from './engines/CodeBERTContextEngine';
import { BasicSemanticEngine } from './engines/BasicSemanticEngine';
import { IntelligentSemanticEngine } from './engines/IntelligentSemanticEngine';
import { CodebaseAnalyzer } from '../codebase/CodebaseAnalyzer';

export class ContextEngineFactory implements IContextEngineFactory {
  private logger: Logger;
  private engineCache: Map<string, IContextEngine> = new Map();
  private codebaseAnalyzer?: CodebaseAnalyzer;

  constructor(logger: Logger, codebaseAnalyzer?: CodebaseAnalyzer) {
    this.logger = logger;
    this.codebaseAnalyzer = codebaseAnalyzer;
  }

  public async createEngine(provider: string, config: ContextEngineConfig): Promise<IContextEngine> {
    const cacheKey = `${provider}_${JSON.stringify(config)}`;
    
    // Return cached engine if available
    if (this.engineCache.has(cacheKey)) {
      const cached = this.engineCache.get(cacheKey)!;
      if (await cached.isAvailable()) {
        return cached;
      } else {
        this.engineCache.delete(cacheKey);
      }
    }

    let engine: IContextEngine;

    switch (provider.toLowerCase()) {
      case 'augment':
        engine = new AugmentContextEngine(this.logger);
        break;

      case 'intelligent':
        if (!this.codebaseAnalyzer) {
          throw new Error('CodebaseAnalyzer required for Intelligent Semantic Engine');
        }
        engine = new IntelligentSemanticEngine(this.logger, this.codebaseAnalyzer);
        break;

      case 'tree-sitter':
        engine = new TreeSitterContextEngine(this.logger);
        break;

      case 'codebert':
        engine = new CodeBERTContextEngine(this.logger);
        break;

      case 'basic':
      case 'fallback':
        engine = new BasicSemanticEngine(this.logger);
        break;

      default:
        throw new Error(`Unsupported context engine provider: ${provider}`);
    }

    // Initialize the engine
    await engine.initialize(config);
    
    // Cache if successful
    if (await engine.isAvailable()) {
      this.engineCache.set(cacheKey, engine);
    }

    return engine;
  }

  public getSupportedProviders(): string[] {
    return ['augment', 'intelligent', 'tree-sitter', 'codebert', 'basic'];
  }

  public async getProviderCapabilities(provider: string): Promise<ProviderCapabilities> {
    switch (provider.toLowerCase()) {
      case 'augment':
        return {
          semanticSearch: true,
          crossLanguage: true,
          realTimeIndexing: true,
          architecturalPatterns: true,
          dependencyAnalysis: true,
          performanceLevel: 'high',
          requiresApi: true,
          requiresInternet: true
        };

      case 'intelligent':
        return {
          semanticSearch: true,
          crossLanguage: true,
          realTimeIndexing: true,
          architecturalPatterns: true,
          dependencyAnalysis: true,
          performanceLevel: 'high',
          requiresApi: false,
          requiresInternet: false
        };
      
      case 'tree-sitter':
        return {
          semanticSearch: true,
          crossLanguage: true,
          realTimeIndexing: true,
          architecturalPatterns: false,
          dependencyAnalysis: true,
          performanceLevel: 'medium',
          requiresApi: false,
          requiresInternet: false
        };
      
      case 'codebert':
        return {
          semanticSearch: true,
          crossLanguage: true,
          realTimeIndexing: false,
          architecturalPatterns: true,
          dependencyAnalysis: false,
          performanceLevel: 'medium',
          requiresApi: false,
          requiresInternet: false
        };
      
      case 'basic':
        return {
          semanticSearch: false,
          crossLanguage: false,
          realTimeIndexing: true,
          architecturalPatterns: false,
          dependencyAnalysis: false,
          performanceLevel: 'high',
          requiresApi: false,
          requiresInternet: false
        };
      
      default:
        throw new Error(`Unknown provider: ${provider}`);
    }
  }

  public async testProvider(provider: string, config: ContextEngineConfig): Promise<ProviderTestResult> {
    const startTime = Date.now();
    
    try {
      const engine = await this.createEngine(provider, config);
      const isAvailable = await engine.isAvailable();
      
      if (!isAvailable) {
        return {
          success: false,
          provider,
          error: 'Provider not available',
          testTime: Date.now() - startTime
        };
      }

      // Run a simple test
      const testResult = await this.runProviderTest(engine);
      
      return {
        success: true,
        provider,
        testTime: Date.now() - startTime,
        capabilities: await this.getProviderCapabilities(provider),
        testResults: testResult
      };
      
    } catch (error) {
      return {
        success: false,
        provider,
        error: error instanceof Error ? error.message : String(error),
        testTime: Date.now() - startTime
      };
    }
  }

  private async runProviderTest(engine: IContextEngine): Promise<any> {
    try {
      // Test basic functionality
      const stats = await engine.getStats();
      
      return {
        statsRetrieved: true,
        engineName: engine.name,
        engineVersion: engine.version,
        ...stats
      };
    } catch (error) {
      return {
        statsRetrieved: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  public clearCache(): void {
    this.engineCache.clear();
  }

  public getCachedEngines(): string[] {
    return Array.from(this.engineCache.keys());
  }
}

export interface ProviderCapabilities {
  semanticSearch: boolean;
  crossLanguage: boolean;
  realTimeIndexing: boolean;
  architecturalPatterns: boolean;
  dependencyAnalysis: boolean;
  performanceLevel: 'low' | 'medium' | 'high';
  requiresApi: boolean;
  requiresInternet: boolean;
}

export interface ProviderTestResult {
  success: boolean;
  provider: string;
  error?: string;
  testTime: number;
  capabilities?: ProviderCapabilities;
  testResults?: any;
}
