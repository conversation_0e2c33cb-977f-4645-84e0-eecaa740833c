import { Logger } from '../utils/Logger';
import { WebBrowser, WebContent, SearchResult } from './WebBrowser';
import { AutoTrainingEngine } from '../training/AutoTrainingEngine';
import { MemoryManager } from '../agent/MemoryManager';
import { AIProviderManager } from '../ai/AIProviderManager';

export interface WebLearningSession {
  id: string;
  query: string;
  searchResults: SearchResult[];
  fetchedContent: WebContent[];
  insights: string[];
  timestamp: Date;
  learningOutcome: 'success' | 'partial' | 'failure';
}

export interface WebKnowledge {
  domain: string;
  topics: string[];
  keyInsights: string[];
  sources: string[];
  lastUpdated: Date;
  confidence: number;
}

export class WebLearningEngine {
  private logger: Logger;
  private webBrowser: WebBrowser;
  private autoTrainingEngine: AutoTrainingEngine;
  private memoryManager: MemoryManager;
  private aiManager: AIProviderManager;
  private learningSessions: WebLearningSession[] = [];
  private webKnowledge: Map<string, WebKnowledge> = new Map();

  constructor(
    logger: Logger,
    webBrowser: WebBrowser,
    autoTrainingEngine: AutoTrainingEngine,
    memoryManager: MemoryManager,
    aiManager: AIProviderManager
  ) {
    this.logger = logger;
    this.webBrowser = webBrowser;
    this.autoTrainingEngine = autoTrainingEngine;
    this.memoryManager = memoryManager;
    this.aiManager = aiManager;
  }

  public async processUserInput(input: string): Promise<{ hasWebContent: boolean; response?: string; webData?: WebContent[] }> {
    // Check if input contains URLs or web search requests
    const urls = this.extractUrls(input);
    const isSearchRequest = this.isWebSearchRequest(input);
    
    if (urls.length > 0) {
      return await this.processUrls(urls, input);
    } else if (isSearchRequest) {
      return await this.processSearchRequest(input);
    }
    
    return { hasWebContent: false };
  }

  public async processUrls(urls: string[], context: string): Promise<{ hasWebContent: boolean; response: string; webData: WebContent[] }> {
    try {
      this.logger.info(`🌐 Processing ${urls.length} URL(s) for learning`);
      
      const webContents: WebContent[] = [];
      
      for (const url of urls) {
        if (this.webBrowser.isValidUrl(url)) {
          const content = await this.webBrowser.fetchWebContent(url);
          if (content) {
            webContents.push(content);
          }
        }
      }
      
      if (webContents.length === 0) {
        return {
          hasWebContent: false,
          response: "I couldn't fetch content from the provided URLs.",
          webData: []
        };
      }
      
      // Learn from the web content
      await this.learnFromWebContent(webContents, context);
      
      // Generate insights
      const insights = await this.generateInsights(webContents, context);
      
      // Record learning session
      this.recordLearningSession({
        query: context,
        searchResults: [],
        fetchedContent: webContents,
        insights,
        learningOutcome: 'success'
      });
      
      const response = await this.generateResponse(webContents, insights, context);
      
      return {
        hasWebContent: true,
        response,
        webData: webContents
      };
      
    } catch (error) {
      this.logger.error(`❌ Failed to process URLs: ${error instanceof Error ? error.message : String(error)}`);
      return {
        hasWebContent: false,
        response: "I encountered an error while processing the URLs.",
        webData: []
      };
    }
  }

  public async processSearchRequest(query: string): Promise<{ hasWebContent: boolean; response: string; webData: WebContent[] }> {
    try {
      // Removed verbose logging - handled at higher level
      
      // Extract search query from natural language
      const searchQuery = this.extractSearchQuery(query);
      
      // Search and fetch content
      const webContents = await this.webBrowser.searchAndFetch(searchQuery, 3);
      
      if (webContents.length === 0) {
        return {
          hasWebContent: false,
          response: `I couldn't find any relevant information for "${searchQuery}".`,
          webData: []
        };
      }
      
      // Learn from the web content
      await this.learnFromWebContent(webContents, query);
      
      // Generate insights
      const insights = await this.generateInsights(webContents, query);
      
      // Record learning session
      this.recordLearningSession({
        query: searchQuery,
        searchResults: [], // Would be populated in a full implementation
        fetchedContent: webContents,
        insights,
        learningOutcome: 'success'
      });
      
      const response = await this.generateResponse(webContents, insights, query);
      
      return {
        hasWebContent: true,
        response,
        webData: webContents
      };
      
    } catch (error) {
      this.logger.error(`❌ Failed to process search request: ${error instanceof Error ? error.message : String(error)}`);
      return {
        hasWebContent: false,
        response: "I encountered an error while searching the web.",
        webData: []
      };
    }
  }

  private async learnFromWebContent(webContents: WebContent[], context: string): Promise<void> {
    try {
      this.logger.info('🧠 Learning from web content...');
      
      for (const content of webContents) {
        // Extract domain knowledge
        const domain = this.webBrowser.extractDomain(content.url);
        const topics = await this.extractTopics(content);
        const insights = await this.extractKeyInsights(content, context);
        
        // Update web knowledge
        this.updateWebKnowledge(domain, topics, insights, content.url);
        
        // Add to memory as learning experience
        this.memoryManager.addExperience(
          `Web learning: ${content.title}`,
          {
            type: 'analyze',
            instruction: `Learn from web content: ${content.title}`,
            target: content.url,
            reasoning: 'Web content analysis for learning',
            confidence: 0.8
          },
          'success',
          undefined,
          undefined,
          [content.url],
          [`Web content from ${domain}`]
        );
        
        // Trigger auto-training with web learning
        await this.autoTrainingEngine.onChatInteraction(
          `Learning from: ${content.title}`,
          content.summary,
          true
        );
      }
      
      // Removed verbose logging - handled at higher level
      
    } catch (error) {
      this.logger.error(`❌ Failed to learn from web content: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  private async generateInsights(webContents: WebContent[], context: string): Promise<string[]> {
    try {
      const combinedContent = webContents.map(c => `${c.title}\n${c.summary}`).join('\n\n');
      
      const messages = [
        {
          role: 'system' as const,
          content: 'You are an AI that extracts key insights from web content. Provide 3-5 concise, actionable insights.'
        },
        {
          role: 'user' as const,
          content: `Context: ${context}\n\nWeb Content:\n${combinedContent}\n\nExtract key insights:`
        }
      ];
      
      const response = await this.aiManager.chat(messages);
      const insights = response.content.split('\n').filter(line => line.trim().length > 0);
      
      return insights.slice(0, 5); // Max 5 insights
      
    } catch (error) {
      this.logger.debug(`Failed to generate insights: ${error}`);
      return ['Web content processed for learning'];
    }
  }

  private async generateResponse(webContents: WebContent[], insights: string[], context: string): Promise<string> {
    try {
      const sources = webContents.map(c => `${c.title} (${c.url})`).join('\n');
      const insightsList = insights.map(insight => `• ${insight}`).join('\n');
      
      const messages = [
        {
          role: 'system' as const,
          content: 'You are an AI assistant that synthesizes web content into helpful responses. Be concise and informative.'
        },
        {
          role: 'user' as const,
          content: `User request: ${context}\n\nKey insights:\n${insightsList}\n\nSources:\n${sources}\n\nProvide a helpful response:`
        }
      ];
      
      const response = await this.aiManager.chat(messages);
      return response.content;
      
    } catch (error) {
      this.logger.debug(`Failed to generate response: ${error}`);
      return `I've learned from the web content and found ${insights.length} key insights. The information has been integrated into my knowledge base.`;
    }
  }

  private extractUrls(text: string): string[] {
    const urlRegex = /https?:\/\/[^\s<>"{}|\\^`[\]]+/g;
    return text.match(urlRegex) || [];
  }

  private isWebSearchRequest(text: string): boolean {
    const lowerText = text.toLowerCase();

    // Exclude self-referential and personal questions
    const personalExclusions = [
      'yourself', 'about you', 'who are you', 'what are you',
      'tell me about yourself', 'about yourself', 'your capabilities',
      'your features', 'your purpose', 'your role', 'your function'
    ];

    // Check if it's a personal question first
    if (personalExclusions.some(exclusion => lowerText.includes(exclusion))) {
      return false;
    }

    const searchKeywords = [
      'search for', 'look up', 'find information about', 'research',
      'search the web', 'google', 'browse for'
    ];

    // More specific patterns for "what is" and "tell me about"
    const specificPatterns = [
      /what is (?!.*\b(this|that|it|here|there)\b).+/i, // "what is X" but not "what is this/that/it"
      /tell me about (?!.*\b(yourself|you|your)\b).+/i, // "tell me about X" but not about the agent
      /learn about (?!.*\b(yourself|you|your)\b).+/i,   // "learn about X" but not about the agent
      /find out about (?!.*\b(yourself|you|your)\b).+/i // "find out about X" but not about the agent
    ];

    // Check direct search keywords
    if (searchKeywords.some(keyword => lowerText.includes(keyword))) {
      return true;
    }

    // Check specific patterns
    return specificPatterns.some(pattern => pattern.test(lowerText));
  }

  private extractSearchQuery(text: string): string {
    // Enhanced extraction with context awareness
    const patterns = [
      /search for (.+)/i,
      /look up (.+)/i,
      /find information about (.+)/i,
      /research (.+)/i,
      /search the web for (.+)/i,
      /google (.+)/i,
      /browse for (.+)/i,
      // More specific patterns that exclude personal references
      /what is (?!.*\b(this|that|it|here|there|yourself|you|your)\b)(.+)/i,
      /tell me about (?!.*\b(yourself|you|your)\b)(.+)/i,
      /learn about (?!.*\b(yourself|you|your)\b)(.+)/i,
      /find out about (?!.*\b(yourself|you|your)\b)(.+)/i
    ];

    for (const pattern of patterns) {
      const match = text.match(pattern);
      if (match) {
        // Return the last capture group (the actual query)
        const captureGroups = match.slice(1).filter(group => group !== undefined);
        if (captureGroups.length > 0) {
          return captureGroups[captureGroups.length - 1].trim();
        }
      }
    }

    return text; // Fallback to original text
  }

  private async extractTopics(content: WebContent): Promise<string[]> {
    // Simple topic extraction - could be enhanced with NLP
    const keywords = content.metadata.keywords || [];
    const titleWords = content.title.split(' ').filter(word => word.length > 3);
    
    return [...keywords, ...titleWords].slice(0, 10);
  }

  private async extractKeyInsights(content: WebContent, context: string): Promise<string[]> {
    // Extract key sentences or points from content
    const sentences = content.summary.split('.').filter(s => s.trim().length > 20);
    return sentences.slice(0, 3);
  }

  private updateWebKnowledge(domain: string, topics: string[], insights: string[], source: string): void {
    const existing = this.webKnowledge.get(domain);
    
    if (existing) {
      existing.topics = [...new Set([...existing.topics, ...topics])];
      existing.keyInsights = [...new Set([...existing.keyInsights, ...insights])];
      existing.sources = [...new Set([...existing.sources, source])];
      existing.lastUpdated = new Date();
      existing.confidence = Math.min(existing.confidence + 0.1, 1.0);
    } else {
      this.webKnowledge.set(domain, {
        domain,
        topics,
        keyInsights: insights,
        sources: [source],
        lastUpdated: new Date(),
        confidence: 0.7
      });
    }
  }

  private recordLearningSession(session: Omit<WebLearningSession, 'id' | 'timestamp'>): void {
    const fullSession: WebLearningSession = {
      ...session,
      id: this.generateId(),
      timestamp: new Date()
    };
    
    this.learningSessions.push(fullSession);
    
    // Keep only last 50 sessions
    if (this.learningSessions.length > 50) {
      this.learningSessions = this.learningSessions.slice(-50);
    }
  }

  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  public getWebKnowledge(): Map<string, WebKnowledge> {
    return new Map(this.webKnowledge);
  }

  public getLearningSessions(): WebLearningSession[] {
    return [...this.learningSessions];
  }

  public async conductAutonomousResearch(topics: string[]): Promise<void> {
    this.logger.info(`🔬 Conducting autonomous research on ${topics.length} topics...`);

    for (const topic of topics) {
      try {
        await this.researchTopicAutonomously(topic);

        // Add delay between research sessions to be respectful
        await this.delay(2000);
      } catch (error) {
        this.logger.error(`❌ Failed to research topic "${topic}": ${error}`);
      }
    }

    this.logger.success(`✅ Autonomous research completed for ${topics.length} topics`);
  }

  private async researchTopicAutonomously(topic: string): Promise<void> {
    this.logger.info(`🔍 Researching topic autonomously: ${topic}`);

    // Generate research questions
    const questions = await this.generateResearchQuestions(topic);

    // Research each question
    const allFindings: string[] = [];
    const allContent: WebContent[] = [];

    for (const question of questions.slice(0, 3)) {
      const searchResults = await this.webBrowser.searchAndFetch(question, 2);
      allContent.push(...searchResults);

      // Extract key findings from content
      const findings = await this.extractKeyFindings(searchResults, question);
      allFindings.push(...findings);
    }

    // Learn from the research
    if (allContent.length > 0) {
      await this.learnFromWebContent(allContent, topic);

      // Generate and store insights
      const insights = await this.generateInsights(allContent, topic);

      // Record autonomous learning session
      this.recordLearningSession({
        query: topic,
        searchResults: [],
        fetchedContent: allContent,
        insights,
        learningOutcome: 'success'
      });

      // Removed verbose logging - handled at higher level
    }
  }

  private async generateResearchQuestions(topic: string): Promise<string[]> {
    try {
      const messages = [
        {
          role: 'system' as const,
          content: 'Generate 3-5 specific research questions about the given topic. Focus on practical, actionable knowledge that would improve coding and development skills.'
        },
        {
          role: 'user' as const,
          content: `Topic: ${topic}\n\nGenerate research questions:`
        }
      ];

      const response = await this.aiManager.chat(messages);
      const questions = response.content
        .split('\n')
        .filter(line => line.trim().length > 0 && (line.includes('?') || line.includes('how') || line.includes('what')))
        .slice(0, 5);

      return questions.length > 0 ? questions : [`What are the key concepts and best practices for ${topic}?`];
    } catch (error) {
      this.logger.debug(`Failed to generate research questions: ${error}`);
      return [`What are the key concepts and best practices for ${topic}?`];
    }
  }

  private async extractKeyFindings(contents: WebContent[], question: string): Promise<string[]> {
    if (contents.length === 0) return [];

    try {
      const combinedContent = contents.map(c => c.summary).join('\n\n');

      const messages = [
        {
          role: 'system' as const,
          content: 'Extract 2-3 key findings that answer the research question. Be concise and focus on actionable insights.'
        },
        {
          role: 'user' as const,
          content: `Question: ${question}\n\nContent: ${combinedContent}\n\nKey findings:`
        }
      ];

      const response = await this.aiManager.chat(messages);
      return response.content
        .split('\n')
        .filter(line => line.trim().length > 0)
        .slice(0, 3);
    } catch (error) {
      this.logger.debug(`Failed to extract key findings: ${error}`);
      return [`Research finding for: ${question}`];
    }
  }

  public async enableContinuousLearning(): Promise<void> {
    this.logger.info('🔄 Enabling continuous web learning...');

    // Set up periodic autonomous research
    setInterval(async () => {
      try {
        const researchTopics = await this.generateAutonomousResearchTopics();
        if (researchTopics.length > 0) {
          await this.conductAutonomousResearch(researchTopics.slice(0, 2));
        }
      } catch (error) {
        this.logger.debug(`Continuous learning cycle failed: ${error}`);
      }
    }, 30 * 60 * 1000); // Every 30 minutes

    this.logger.success('✅ Continuous web learning enabled');
  }

  private async generateAutonomousResearchTopics(): Promise<string[]> {
    const topics = [
      'latest software development trends',
      'emerging programming languages',
      'AI development tools',
      'software architecture patterns',
      'performance optimization techniques',
      'security best practices',
      'cloud computing innovations',
      'developer productivity tools'
    ];

    // Randomly select 2-3 topics for research
    const shuffled = topics.sort(() => 0.5 - Math.random());
    return shuffled.slice(0, Math.floor(Math.random() * 2) + 2);
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
