<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\SubscriptionPlan;
use App\Models\UserSubscription;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Laravel\Socialite\Facades\Socialite;

class SocialAuthController extends Controller
{
    /**
     * Redirect to OAuth provider.
     */
    public function redirect(string $provider)
    {
        if (!in_array($provider, ['google', 'github'])) {
            return redirect()->route('login')->with('error', 'Invalid provider');
        }

        return Socialite::driver($provider)->redirect();
    }

    /**
     * Handle OAuth callback.
     */
    public function callback(string $provider)
    {
        try {
            if (!in_array($provider, ['google', 'github'])) {
                return redirect()->route('login')->with('error', 'Invalid provider');
            }

            $socialUser = Socialite::driver($provider)->user();

            // Find existing user by provider ID or email
            $user = User::where('provider', $provider)
                       ->where('provider_id', $socialUser->getId())
                       ->first();

            if (!$user) {
                $user = User::where('email', $socialUser->getEmail())->first();

                if ($user) {
                    // Link existing account with OAuth provider
                    $user->update([
                        'provider' => $provider,
                        'provider_id' => $socialUser->getId(),
                        'provider_data' => [
                            'avatar' => $socialUser->getAvatar(),
                            'nickname' => $socialUser->getNickname(),
                        ],
                        'avatar' => $socialUser->getAvatar(),
                    ]);
                } else {
                    // Create new user
                    $user = User::create([
                        'name' => $socialUser->getName() ?: $socialUser->getNickname(),
                        'email' => $socialUser->getEmail(),
                        'provider' => $provider,
                        'provider_id' => $socialUser->getId(),
                        'provider_data' => [
                            'avatar' => $socialUser->getAvatar(),
                            'nickname' => $socialUser->getNickname(),
                        ],
                        'avatar' => $socialUser->getAvatar(),
                        'email_verified_at' => now(),
                        'is_active' => true,
                    ]);

                    // Assign free plan to new user
                    $this->assignFreePlan($user);
                }
            }

            // Update last login
            $user->update(['last_login_at' => now()]);

            Auth::login($user, true);

            return redirect()->intended('/dashboard');

        } catch (\Exception $e) {
            return redirect()->route('login')->with('error', 'Authentication failed. Please try again.');
        }
    }

    /**
     * Assign free plan to new user.
     */
    private function assignFreePlan(User $user): void
    {
        $freePlan = SubscriptionPlan::where('slug', 'free')->first();

        if ($freePlan) {
            UserSubscription::create([
                'user_id' => $user->id,
                'subscription_plan_id' => $freePlan->id,
                'status' => 'active',
                'billing_cycle' => 'monthly',
                'current_period_start' => now(),
                'current_period_end' => now()->addMonth(),
                'usage_reset_date' => now()->toDateString(),
            ]);
        }
    }
}
