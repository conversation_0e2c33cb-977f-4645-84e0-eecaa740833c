import chalk from 'chalk';
import boxen from 'boxen';

export interface UITheme {
  primary: (text: string) => string;
  secondary: (text: string) => string;
  accent: (text: string) => string;
  muted: (text: string) => string;
  success: (text: string) => string;
  warning: (text: string) => string;
  error: (text: string) => string;
  border: string;
  separator: string;
}

export class ClaudeUI {
  private theme: UITheme;

  constructor() {
    this.theme = {
      primary: chalk.white,
      secondary: chalk.gray,
      accent: chalk.cyan,
      muted: chalk.dim,
      success: chalk.green,
      warning: chalk.yellow,
      error: chalk.red,
      border: 'gray',
      separator: '─'
    };
  }

  public header(title: string, subtitle?: string): string {
    const content = subtitle ? `${title}\n${this.theme.muted(subtitle)}` : title;
    return boxen(content, {
      padding: { top: 0, bottom: 0, left: 2, right: 2 },
      borderStyle: 'round',
      borderColor: this.theme.border as any,
      title: '🤖 GOC Agent',
      titleAlignment: 'left'
    });
  }

  public sessionInfo(provider: string, model: string, contextEngine?: string): string {
    const parts = [
      this.theme.accent('Provider: ') + this.theme.primary(provider),
      this.theme.accent('Model: ') + this.theme.primary(model)
    ];

    if (contextEngine) {
      parts.push(this.theme.accent('Context: ') + this.theme.primary(contextEngine));
    }

    const content = parts.join('  •  ');
    const separator = this.theme.muted(this.theme.separator.repeat(80));
    
    return `\n${content}\n${separator}\n`;
  }

  public section(title: string, content: string): string {
    return `\n${this.theme.accent('▶ ' + title)}\n${content}\n`;
  }

  public list(items: string[], bullet: string = '•'): string {
    return items.map(item => `  ${this.theme.muted(bullet)} ${item}`).join('\n');
  }

  public table(headers: string[], rows: string[][]): string {
    const maxWidths = headers.map((header, i) => 
      Math.max(header.length, ...rows.map(row => (row[i] || '').length))
    );

    const headerRow = headers.map((header, i) => 
      this.theme.accent(header.padEnd(maxWidths[i]))
    ).join('  ');

    const separator = this.theme.muted(
      maxWidths.map(width => this.theme.separator.repeat(width)).join('  ')
    );

    const dataRows = rows.map(row => 
      row.map((cell, i) => 
        this.theme.primary((cell || '').padEnd(maxWidths[i]))
      ).join('  ')
    );

    return [headerRow, separator, ...dataRows].join('\n');
  }

  public status(type: 'success' | 'warning' | 'error' | 'info', message: string): string {
    const icons = {
      success: '✓',
      warning: '⚠',
      error: '✗',
      info: 'ℹ'
    };

    const colors = {
      success: this.theme.success,
      warning: this.theme.warning,
      error: this.theme.error,
      info: this.theme.accent
    };

    return colors[type](`${icons[type]} ${message}`);
  }

  public progress(message: string, step?: number, total?: number): string {
    const stepInfo = step && total ? ` (${step}/${total})` : '';
    return this.theme.accent(`⏳ ${message}${stepInfo}`);
  }

  public timer(message: string, startTime: number): string {
    const elapsed = Date.now() - startTime;
    const seconds = Math.floor(elapsed / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;

    let timeStr = '';
    if (minutes > 0) {
      timeStr = `${minutes}m ${remainingSeconds}s`;
    } else {
      timeStr = `${remainingSeconds}s`;
    }

    return this.theme.muted(`⏱️  ${message} (${timeStr})`);
  }

  public taskProgress(message: string, startTime: number, step?: number, total?: number): string {
    const elapsed = Date.now() - startTime;
    const seconds = Math.floor(elapsed / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;

    let timeStr = '';
    if (minutes > 0) {
      timeStr = `${minutes}m ${remainingSeconds}s`;
    } else {
      timeStr = `${remainingSeconds}s`;
    }

    const stepInfo = step && total ? ` (${step}/${total})` : '';
    return this.theme.accent(`⚡ ${message}${stepInfo} - ${timeStr}`);
  }

  public result(title: string, data: Record<string, any>): string {
    const content = Object.entries(data)
      .map(([key, value]) => `${this.theme.accent(key + ':')} ${this.theme.primary(String(value))}`)
      .join('\n');

    return boxen(content, {
      padding: 1,
      borderStyle: 'round',
      borderColor: this.theme.border as any,
      title: title,
      titleAlignment: 'center'
    });
  }

  public codeBlock(code: string, language?: string): string {
    const title = language ? `${language.toUpperCase()} Code` : 'Code';
    return boxen(code, {
      padding: 1,
      borderStyle: 'single',
      borderColor: 'gray',
      title: title,
      titleAlignment: 'left'
    });
  }

  public separator(text?: string): string {
    if (text) {
      const padding = Math.max(0, (80 - text.length - 4) / 2);
      const leftPad = this.theme.separator.repeat(Math.floor(padding));
      const rightPad = this.theme.separator.repeat(Math.ceil(padding));
      return this.theme.muted(`${leftPad}  ${text}  ${rightPad}`);
    }
    return this.theme.muted(this.theme.separator.repeat(80));
  }

  public chat(role: 'user' | 'assistant', message: string): string {
    const roleColors = {
      user: this.theme.accent,
      assistant: this.theme.success
    };

    const roleIcons = {
      user: '👤',
      assistant: '🤖'
    };

    // Always use "GOC Agent" for assistant role to maintain consistency
    const roleLabels = {
      user: 'You',
      assistant: 'GOC Agent'
    };

    const roleText = roleColors[role](`${roleIcons[role]} ${roleLabels[role]}:`);
    return `\n${roleText}\n${message}\n`;
  }

  public error(title: string, message: string, suggestion?: string): string {
    let content = `${this.theme.error('Error:')} ${message}`;
    
    if (suggestion) {
      content += `\n\n${this.theme.accent('Suggestion:')} ${suggestion}`;
    }

    return boxen(content, {
      padding: 1,
      borderStyle: 'double',
      borderColor: 'red',
      title: title,
      titleAlignment: 'center'
    });
  }

  public clear(): void {
    console.clear();
  }

  public newline(count: number = 1): string {
    return '\n'.repeat(count);
  }
}
