import { Logger } from '../utils/Logger';
import { MemoryManager } from '../agent/MemoryManager';
import { AutoTrainingEngine } from './AutoTrainingEngine';
import { IntelligenceEngine } from './IntelligenceEngine';
import { TrainingManager } from './TrainingManager';
import { KnowledgeRepository } from './KnowledgeRepository';
import { CuriosityEngine } from './CuriosityEngine';
import { PerformanceMonitor } from './PerformanceMonitor';
import { SelfTrainingOrchestrator } from './SelfTrainingOrchestrator';
import { WebLearningEngine } from '../web/WebLearningEngine';
import { WebBrowser } from '../web/WebBrowser';
import { AIProviderManager } from '../ai/AIProviderManager';
import { CodebaseAnalyzer } from '../codebase/CodebaseAnalyzer';
import { ChangeTracker } from '../utils/ChangeTracker';

export interface SelfTrainingComponents {
  orchestrator: SelfTrainingOrchestrator;
  knowledgeRepository: KnowledgeRepository;
  curiosityEngine: CuriosityEngine;
  performanceMonitor: PerformanceMonitor;
  webLearningEngine: WebLearningEngine;
  autoTrainingEngine: AutoTrainingEngine;
  intelligenceEngine: IntelligenceEngine;
  trainingManager: TrainingManager;
}

export class SelfTrainingFactory {
  private logger: Logger;
  private aiManager: AIProviderManager;
  private memoryManager: MemoryManager;
  private codebaseAnalyzer: CodebaseAnalyzer;

  constructor(
    logger: Logger,
    aiManager: AIProviderManager,
    memoryManager: MemoryManager,
    codebaseAnalyzer: CodebaseAnalyzer
  ) {
    this.logger = logger;
    this.aiManager = aiManager;
    this.memoryManager = memoryManager;
    this.codebaseAnalyzer = codebaseAnalyzer;
  }

  public async createSelfTrainingSystem(): Promise<SelfTrainingComponents> {
    this.logger.info('🏗️ Creating comprehensive self-training system...');

    try {
      // Create core components
      const knowledgeRepository = new KnowledgeRepository(this.logger);
      const webBrowser = new WebBrowser(this.logger);
      const changeTracker = new ChangeTracker(this.logger);

      // Create training components
      const trainingManager = new TrainingManager(
        this.logger,
        this.memoryManager,
        this.codebaseAnalyzer
      );

      const intelligenceEngine = new IntelligenceEngine(
        this.logger,
        this.memoryManager,
        trainingManager,
        this.codebaseAnalyzer,
        changeTracker
      );

      const autoTrainingEngine = new AutoTrainingEngine(
        this.logger,
        intelligenceEngine,
        this.memoryManager,
        trainingManager
      );

      // Create analysis components
      const performanceMonitor = new PerformanceMonitor(
        this.logger,
        this.memoryManager,
        knowledgeRepository
      );

      const curiosityEngine = new CuriosityEngine(
        this.logger,
        knowledgeRepository,
        this.memoryManager,
        this.aiManager
      );

      // Create web learning engine
      const webLearningEngine = new WebLearningEngine(
        this.logger,
        webBrowser,
        autoTrainingEngine,
        this.memoryManager,
        this.aiManager
      );

      // Create the orchestrator that coordinates everything
      const orchestrator = new SelfTrainingOrchestrator(
        this.logger,
        this.memoryManager,
        autoTrainingEngine,
        intelligenceEngine,
        trainingManager,
        knowledgeRepository,
        curiosityEngine,
        performanceMonitor,
        webLearningEngine,
        this.aiManager
      );

      // Enable auto-training
      autoTrainingEngine.enableAutoTraining({
        enabled: true,
        triggerThreshold: 3,
        backgroundTraining: true,
        adaptiveFrequency: true,
        silentMode: true
      });

      this.logger.success('✅ Self-training system created successfully');

      return {
        orchestrator,
        knowledgeRepository,
        curiosityEngine,
        performanceMonitor,
        webLearningEngine,
        autoTrainingEngine,
        intelligenceEngine,
        trainingManager
      };

    } catch (error) {
      this.logger.error(`❌ Failed to create self-training system: ${error}`);
      throw error;
    }
  }

  public async createMinimalSelfTrainingSystem(): Promise<SelfTrainingComponents> {
    // Debug: Creating minimal self-training system
    // this.logger.info('🏗️ Creating minimal self-training system...');

    try {
      // Create only essential components for basic self-training
      const knowledgeRepository = new KnowledgeRepository(this.logger);
      const webBrowser = new WebBrowser(this.logger);
      const changeTracker = new ChangeTracker(this.logger);

      const trainingManager = new TrainingManager(
        this.logger,
        this.memoryManager,
        this.codebaseAnalyzer
      );

      const intelligenceEngine = new IntelligenceEngine(
        this.logger,
        this.memoryManager,
        trainingManager,
        this.codebaseAnalyzer,
        changeTracker
      );

      const autoTrainingEngine = new AutoTrainingEngine(
        this.logger,
        intelligenceEngine,
        this.memoryManager,
        trainingManager
      );

      // Minimal analysis components
      const performanceMonitor = new PerformanceMonitor(
        this.logger,
        this.memoryManager,
        knowledgeRepository
      );

      const curiosityEngine = new CuriosityEngine(
        this.logger,
        knowledgeRepository,
        this.memoryManager,
        this.aiManager
      );

      const webLearningEngine = new WebLearningEngine(
        this.logger,
        webBrowser,
        autoTrainingEngine,
        this.memoryManager,
        this.aiManager
      );

      // Minimal orchestrator
      const orchestrator = new SelfTrainingOrchestrator(
        this.logger,
        this.memoryManager,
        autoTrainingEngine,
        intelligenceEngine,
        trainingManager,
        knowledgeRepository,
        curiosityEngine,
        performanceMonitor,
        webLearningEngine,
        this.aiManager
      );

      // Configure for minimal resource usage
      orchestrator.updateConfig({
        enabled: true,
        continuousLearning: true,
        autonomousResearch: false, // Disable for minimal system
        performanceOptimization: true,
        knowledgeSharing: true,
        adaptiveBehavior: true,
        backgroundProcessing: true,
        learningIntensity: 'low'
      });

      autoTrainingEngine.enableAutoTraining({
        enabled: true,
        triggerThreshold: 5,
        backgroundTraining: true,
        adaptiveFrequency: false,
        silentMode: true
      });

      // Debug: Minimal self-training system created successfully
      // this.logger.success('✅ Minimal self-training system created successfully');

      return {
        orchestrator,
        knowledgeRepository,
        curiosityEngine,
        performanceMonitor,
        webLearningEngine,
        autoTrainingEngine,
        intelligenceEngine,
        trainingManager
      };

    } catch (error) {
      this.logger.error(`❌ Failed to create minimal self-training system: ${error}`);
      throw error;
    }
  }

  public static async initializeWithAgent(
    logger: Logger,
    aiManager: AIProviderManager,
    memoryManager: MemoryManager,
    codebaseAnalyzer: CodebaseAnalyzer,
    minimal: boolean = false
  ): Promise<SelfTrainingComponents> {
    const factory = new SelfTrainingFactory(
      logger,
      aiManager,
      memoryManager,
      codebaseAnalyzer
    );

    if (minimal) {
      return await factory.createMinimalSelfTrainingSystem();
    } else {
      return await factory.createSelfTrainingSystem();
    }
  }

  public async validateSelfTrainingSystem(components: SelfTrainingComponents): Promise<boolean> {
    try {
      this.logger.info('🔍 Validating self-training system...');

      // Test basic functionality
      const metrics = components.orchestrator.getMetrics();
      const status = components.orchestrator.getTrainingStatus();
      const knowledgeStats = components.knowledgeRepository.getKnowledgeStats();

      // Validate components are working
      if (!metrics || !status || !knowledgeStats) {
        throw new Error('Core components not responding');
      }

      // Test knowledge storage
      await components.knowledgeRepository.storeKnowledge(
        'test',
        'validation',
        'System validation test',
        ['validation']
      );

      // Test curiosity engine
      const topics = await components.curiosityEngine.generateResearchTopics();
      if (!Array.isArray(topics)) {
        throw new Error('Curiosity engine not generating topics');
      }

      // Test performance monitoring
      const performance = await components.performanceMonitor.evaluateCurrentPerformance();
      if (!performance) {
        throw new Error('Performance monitor not working');
      }

      this.logger.success('✅ Self-training system validation passed');
      return true;

    } catch (error) {
      this.logger.error(`❌ Self-training system validation failed: ${error}`);
      return false;
    }
  }
}
