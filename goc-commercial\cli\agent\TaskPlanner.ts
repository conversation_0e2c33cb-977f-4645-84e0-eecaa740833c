import { AIProviderManager, ChatMessage } from '../ai/AIProviderManager';
import { Logger } from '../utils/Logger';

export interface TaskPlan {
  mainTask: string;
  subTasks: string[];
  estimatedComplexity: 'low' | 'medium' | 'high';
  estimatedTime: string;
  dependencies: string[];
  risks: string[];
}

export class TaskPlanner {
  private aiManager: AIProviderManager;
  private logger: Logger;

  constructor(aiManager: AIProviderManager, logger: Logger) {
    this.aiManager = aiManager;
    this.logger = logger;
  }

  public async createPlan(task: string, context: string): Promise<TaskPlan> {
    // Handle simple todo app creation with a predefined plan
    const lowerTask = task.toLowerCase();
    if (lowerTask.includes('todo') && lowerTask.includes('app') && (lowerTask.includes('create') || lowerTask.includes('simple'))) {
      return this.createTodoAppPlan(task);
    }

    const messages: ChatMessage[] = [
      {
        role: 'system',
        content: `You are an expert software architect and project planner. Break down the given task into actionable sub-tasks.

Consider:
- Code architecture and best practices
- Dependencies between tasks
- Potential risks and challenges
- Realistic time estimates

For web apps (HTML/CSS/JS):
- Create simple, standalone files (NO React, NO frameworks, NO build tools)
- Use ONLY vanilla HTML, CSS, and JavaScript
- Create separate files: index.html, style.css, script.js
- Make it work in any browser without compilation
- Focus on basic DOM manipulation and event handling

Context:
${context}

IMPORTANT: Respond with ONLY valid JSON. No explanations, no markdown, no code blocks. Keep it simple.

{
  "mainTask": "task description",
  "subTasks": ["step 1", "step 2", "step 3"],
  "estimatedComplexity": "low",
  "estimatedTime": "5 minutes",
  "dependencies": [],
  "risks": []
}`
      },
      {
        role: 'user',
        content: `Please create a detailed plan for: ${task}`
      }
    ];

    try {
      const response = await this.aiManager.chat(messages);
      const plan = this.parsePlanResponse(response.content);

      this.logger.debug(`📋 Plan created with ${plan.subTasks.length} sub-tasks`);
      return plan;
    } catch (error) {
      this.logger.error(`Failed to create plan: ${error}`);
      return this.createFallbackPlan(task);
    }
  }

  private createTodoAppPlan(task: string): TaskPlan {
    return {
      mainTask: task,
      subTasks: [
        'Create HTML structure for the todo application',
        'Create CSS styles for the todo application',
        'Create JavaScript functionality for the todo application'
      ],
      estimatedComplexity: 'low',
      estimatedTime: '10-15 minutes',
      dependencies: [],
      risks: ['File creation permissions']
    };
  }

  public async refinePlan(plan: TaskPlan, feedback: string): Promise<TaskPlan> {
    const messages: ChatMessage[] = [
      {
        role: 'system',
        content: 'Refine the existing plan based on the feedback provided.'
      },
      {
        role: 'user',
        content: `Current plan: ${JSON.stringify(plan)}\n\nFeedback: ${feedback}\n\nProvide refined plan:`
      }
    ];

    try {
      const response = await this.aiManager.chat(messages);
      return this.parsePlanResponse(response.content);
    } catch (error) {
      this.logger.error(`Failed to refine plan: ${error}`);
      return plan; // Return original plan if refinement fails
    }
  }

  public async adaptPlan(plan: TaskPlan, currentProgress: string[], obstacles: string[]): Promise<TaskPlan> {
    const messages: ChatMessage[] = [
      {
        role: 'system',
        content: 'Adapt the plan based on current progress and obstacles encountered.'
      },
      {
        role: 'user',
        content: `
Original plan: ${JSON.stringify(plan)}
Completed tasks: ${currentProgress.join(', ')}
Obstacles encountered: ${obstacles.join(', ')}

Provide adapted plan:`
      }
    ];

    try {
      const response = await this.aiManager.chat(messages);
      return this.parsePlanResponse(response.content);
    } catch (error) {
      this.logger.error(`Failed to adapt plan: ${error}`);
      return plan;
    }
  }

  private parsePlanResponse(response: string): TaskPlan {
    let cleaned = '';
    try {
      // Multiple parsing strategies
      cleaned = response.trim();

      // Remove markdown code blocks
      cleaned = cleaned.replace(/```json\n?|\n?```/g, '');
      cleaned = cleaned.replace(/```\n?|\n?```/g, '');

      // Try to find JSON object in the response - be more aggressive
      const jsonMatch = cleaned.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        cleaned = jsonMatch[0];
      } else {
        // If no JSON found, try to extract from any text that might contain JSON
        const lines = cleaned.split('\n');
        for (const line of lines) {
          if (line.trim().startsWith('{')) {
            cleaned = line.trim();
            break;
          }
        }
      }

      // Clean up common formatting issues
      cleaned = cleaned.replace(/^\s*[\r\n]+|[\r\n]+\s*$/g, '');

      // Fix common JSON issues
      cleaned = this.fixCommonJsonIssues(cleaned);

      const parsed = JSON.parse(cleaned);

      // Handle both simple and complex subTasks formats
      let subTasks: string[] = [];
      if (Array.isArray(parsed.subTasks)) {
        subTasks = parsed.subTasks.map((task: any) => {
          if (typeof task === 'string') {
            return task;
          } else if (typeof task === 'object' && task.task) {
            return task.task;
          } else {
            return String(task);
          }
        });
      }

      // Handle both simple and complex risks formats
      let risks: string[] = [];
      if (Array.isArray(parsed.risks)) {
        risks = parsed.risks.map((risk: any) => {
          if (typeof risk === 'string') {
            return risk;
          } else if (typeof risk === 'object' && risk.risk) {
            return risk.risk;
          } else {
            return String(risk);
          }
        });
      }

      return {
        mainTask: parsed.mainTask || 'Unknown task',
        subTasks,
        estimatedComplexity: parsed.estimatedComplexity || 'medium',
        estimatedTime: parsed.estimatedTime || 'Unknown',
        dependencies: Array.isArray(parsed.dependencies) ? parsed.dependencies : [],
        risks
      };
    } catch (error) {
      this.logger.warn(`Failed to parse plan response: ${error}`);
      this.logger.debug(`Raw response (first 500 chars): ${response.substring(0, 500)}`);
      this.logger.debug(`Cleaned response: ${cleaned.substring(0, 200)}`);
      return this.createFallbackPlan('Unknown task');
    }
  }

  private fixCommonJsonIssues(jsonStr: string): string {
    // Remove any text before the first { or after the last }
    const firstBrace = jsonStr.indexOf('{');
    const lastBrace = jsonStr.lastIndexOf('}');
    if (firstBrace !== -1 && lastBrace !== -1 && lastBrace > firstBrace) {
      jsonStr = jsonStr.substring(firstBrace, lastBrace + 1);
    }

    // Only fix trailing commas before closing braces/brackets
    jsonStr = jsonStr.replace(/,(\s*[}\]])/g, '$1');

    return jsonStr.trim();
  }

  private createFallbackPlan(task: string): TaskPlan {
    return {
      mainTask: task,
      subTasks: [
        'Analyze the current codebase',
        'Identify required changes',
        'Implement the changes',
        'Test the implementation',
        'Review and refine'
      ],
      estimatedComplexity: 'medium',
      estimatedTime: '1-2 hours',
      dependencies: [],
      risks: ['Unclear requirements', 'Potential breaking changes']
    };
  }
}
