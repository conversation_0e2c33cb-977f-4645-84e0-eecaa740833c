<?php

namespace Database\Seeders;

use App\Models\SubscriptionPlan;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class SubscriptionPlanSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $plans = [
            [
                'name' => 'Free',
                'slug' => 'free',
                'description' => 'Perfect for trying out GOC Agent',
                'price' => 0.00,
                'yearly_price' => 0.00,
                'api_requests_daily' => 100,
                'api_requests_monthly' => 1000,
                'max_projects' => 3,
                'max_team_members' => 1,
                'features' => [
                    'Basic AI assistance',
                    'Code generation',
                    'Up to 3 projects',
                    'Community support',
                ],
                'is_popular' => false,
                'is_active' => true,
                'sort_order' => 1,
            ],
            [
                'name' => 'Pro',
                'slug' => 'pro',
                'description' => 'For professional developers and small teams',
                'price' => 29.00,
                'yearly_price' => 290.00, // 2 months free
                'api_requests_daily' => 2000,
                'api_requests_monthly' => 50000,
                'max_projects' => 0, // Unlimited
                'max_team_members' => 5,
                'features' => [
                    'Advanced AI assistance',
                    'Unlimited projects',
                    'Priority support',
                    'Team collaboration',
                    'Advanced analytics',
                    'Custom integrations',
                ],
                'is_popular' => true,
                'is_active' => true,
                'sort_order' => 2,
            ],
            [
                'name' => 'Enterprise',
                'slug' => 'enterprise',
                'description' => 'For large teams and organizations',
                'price' => 99.00,
                'yearly_price' => 990.00, // 2 months free
                'api_requests_daily' => 0, // Unlimited
                'api_requests_monthly' => 0, // Unlimited
                'max_projects' => 0, // Unlimited
                'max_team_members' => 0, // Unlimited
                'features' => [
                    'Everything in Pro',
                    'Unlimited API requests',
                    'Unlimited team members',
                    'Dedicated support',
                    'Custom deployment',
                    'SLA guarantee',
                    'Advanced security',
                    'Custom training',
                ],
                'is_popular' => false,
                'is_active' => true,
                'sort_order' => 3,
            ],
        ];

        foreach ($plans as $plan) {
            SubscriptionPlan::updateOrCreate(
                ['slug' => $plan['slug']],
                $plan
            );
        }
    }
}
