<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class SubscriptionPlan extends Model
{
    protected $fillable = [
        'name',
        'slug',
        'description',
        'price',
        'yearly_price',
        'stripe_price_id',
        'stripe_yearly_price_id',
        'api_requests_daily',
        'api_requests_monthly',
        'max_projects',
        'max_team_members',
        'features',
        'is_popular',
        'is_active',
        'sort_order',
    ];

    protected function casts(): array
    {
        return [
            'price' => 'decimal:2',
            'yearly_price' => 'decimal:2',
            'features' => 'array',
            'is_popular' => 'boolean',
            'is_active' => 'boolean',
        ];
    }

    /**
     * Get the user subscriptions for this plan.
     */
    public function userSubscriptions(): HasMany
    {
        return $this->hasMany(UserSubscription::class);
    }

    /**
     * Get active plans only.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Get plans ordered by sort order.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order');
    }

    /**
     * Check if this is a free plan.
     */
    public function isFree(): bool
    {
        return $this->price == 0;
    }

    /**
     * Get yearly discount percentage.
     */
    public function getYearlyDiscountAttribute(): ?int
    {
        if (!$this->yearly_price || !$this->price || $this->price == 0) {
            return null;
        }

        $monthlyYearly = $this->price * 12;
        if ($monthlyYearly == 0) {
            return null;
        }

        return round((($monthlyYearly - $this->yearly_price) / $monthlyYearly) * 100);
    }
}
