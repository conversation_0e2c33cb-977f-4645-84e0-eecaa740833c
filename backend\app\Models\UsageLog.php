<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UsageLog extends Model
{
    public $timestamps = false; // We only use created_at

    protected $fillable = [
        'user_id',
        'api_key_id',
        'endpoint',
        'method',
        'ip_address',
        'user_agent',
        'request_data',
        'response_data',
        'response_status',
        'response_time_ms',
        'tokens_used',
        'cost',
        'session_id',
        'created_at',
    ];

    protected function casts(): array
    {
        return [
            'request_data' => 'array',
            'response_data' => 'array',
            'cost' => 'decimal:6',
            'created_at' => 'datetime',
        ];
    }

    /**
     * Get the user that made the request.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the API key used for the request.
     */
    public function apiKey(): BelongsTo
    {
        return $this->belongsTo(UserApiKey::class, 'api_key_id');
    }

    /**
     * Scope for recent logs.
     */
    public function scopeRecent($query, int $days = 30)
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }

    /**
     * Scope for successful requests.
     */
    public function scopeSuccessful($query)
    {
        return $query->whereBetween('response_status', [200, 299]);
    }

    /**
     * Scope for failed requests.
     */
    public function scopeFailed($query)
    {
        return $query->where('response_status', '>=', 400);
    }
}
