<?php

namespace App\Http\Controllers;

use App\Models\UserApiKey;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class ApiKeyController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display API keys.
     */
    public function index()
    {
        $user = Auth::user();
        $apiKeys = $user->userApiKeys()->latest()->get();

        return view('dashboard.api-keys', compact('apiKeys'));
    }

    /**
     * Create a new API key.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'permissions' => 'nullable|array',
        ]);

        $user = Auth::user();

        // Generate API key
        $apiKey = 'goc_' . Str::random(40);
        $keyHash = Hash::make($apiKey);
        $keyPrefix = substr($apiKey, 0, 8);

        // Create API key record
        $userApiKey = UserApiKey::create([
            'user_id' => $user->id,
            'name' => $request->name,
            'key_hash' => $keyHash,
            'key_prefix' => $keyPrefix,
            'permissions' => json_encode($request->permissions ?? []),
            'is_active' => true,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'API key created successfully',
            'api_key' => $apiKey, // Only shown once
            'key_id' => $userApiKey->id,
            'key_prefix' => $keyPrefix,
        ]);
    }

    /**
     * Update API key.
     */
    public function update(Request $request, UserApiKey $apiKey)
    {
        // Ensure user owns this API key
        if ($apiKey->user_id !== Auth::id()) {
            abort(403);
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'permissions' => 'nullable|array',
            'is_active' => 'boolean',
        ]);

        $apiKey->update([
            'name' => $request->name,
            'permissions' => json_encode($request->permissions ?? []),
            'is_active' => $request->boolean('is_active', true),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'API key updated successfully',
        ]);
    }

    /**
     * Delete API key.
     */
    public function destroy(UserApiKey $apiKey)
    {
        // Ensure user owns this API key
        if ($apiKey->user_id !== Auth::id()) {
            abort(403);
        }

        $apiKey->delete();

        return response()->json([
            'success' => true,
            'message' => 'API key deleted successfully',
        ]);
    }

    /**
     * Toggle API key status.
     */
    public function toggle(UserApiKey $apiKey)
    {
        // Ensure user owns this API key
        if ($apiKey->user_id !== Auth::id()) {
            abort(403);
        }

        $apiKey->update([
            'is_active' => !$apiKey->is_active,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'API key ' . ($apiKey->is_active ? 'activated' : 'deactivated') . ' successfully',
            'is_active' => $apiKey->is_active,
        ]);
    }

    /**
     * Get API key usage statistics.
     */
    public function usage(UserApiKey $apiKey)
    {
        // Ensure user owns this API key
        if ($apiKey->user_id !== Auth::id()) {
            abort(403);
        }

        $usage = [
            'requests_today' => $apiKey->requests_today,
            'requests_month' => $apiKey->requests_month,
            'total_requests' => $apiKey->total_requests,
            'last_used_at' => $apiKey->last_used_at,
            'last_used_ip' => $apiKey->last_used_ip,
        ];

        return response()->json($usage);
    }
}
