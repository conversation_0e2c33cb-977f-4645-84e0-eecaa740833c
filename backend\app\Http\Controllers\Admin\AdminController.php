<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\UserSubscription;
use App\Models\UsageLog;
use App\Models\SubscriptionPlan;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class AdminController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware(function ($request, $next) {
            if (!auth()->user()->isAdmin()) {
                abort(403, 'Access denied. Admin privileges required.');
            }
            return $next($request);
        });
    }

    /**
     * Admin dashboard.
     */
    public function dashboard()
    {
        $stats = [
            'total_users' => User::count(),
            'active_subscriptions' => UserSubscription::where('status', 'active')->count(),
            'total_revenue' => UserSubscription::join('subscription_plans', 'user_subscriptions.subscription_plan_id', '=', 'subscription_plans.id')
                ->where('user_subscriptions.status', 'active')
                ->sum('subscription_plans.price'),
            'api_requests_today' => UsageLog::whereDate('created_at', today())->count(),
        ];

        $recentUsers = User::latest()->take(10)->get();
        $recentSubscriptions = UserSubscription::with(['user', 'subscriptionPlan'])
            ->latest()
            ->take(10)
            ->get();

        return view('admin.dashboard', compact('stats', 'recentUsers', 'recentSubscriptions'));
    }

    /**
     * Manage users.
     */
    public function users(Request $request)
    {
        $query = User::with('subscription.subscriptionPlan');

        if ($request->search) {
            $query->where(function ($q) use ($request) {
                $q->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('email', 'like', '%' . $request->search . '%');
            });
        }

        if ($request->status) {
            $query->where('is_active', $request->status === 'active');
        }

        $users = $query->paginate(20);

        return view('admin.users', compact('users'));
    }

    /**
     * Manage subscriptions.
     */
    public function subscriptions(Request $request)
    {
        $query = UserSubscription::with(['user', 'subscriptionPlan']);

        if ($request->status) {
            $query->where('status', $request->status);
        }

        if ($request->plan) {
            $query->where('subscription_plan_id', $request->plan);
        }

        $subscriptions = $query->paginate(20);
        $plans = SubscriptionPlan::all();

        return view('admin.subscriptions', compact('subscriptions', 'plans'));
    }

    /**
     * Analytics dashboard.
     */
    public function analytics()
    {
        $dailySignups = User::selectRaw('DATE(created_at) as date, COUNT(*) as count')
            ->where('created_at', '>=', now()->subDays(30))
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        $dailyRevenue = UserSubscription::join('subscription_plans', 'user_subscriptions.subscription_plan_id', '=', 'subscription_plans.id')
            ->selectRaw('DATE(user_subscriptions.created_at) as date, SUM(subscription_plans.price) as revenue')
            ->where('user_subscriptions.created_at', '>=', now()->subDays(30))
            ->where('user_subscriptions.status', 'active')
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        $apiUsage = UsageLog::selectRaw('DATE(created_at) as date, COUNT(*) as requests')
            ->where('created_at', '>=', now()->subDays(30))
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        return view('admin.analytics', compact('dailySignups', 'dailyRevenue', 'apiUsage'));
    }

    /**
     * System settings.
     */
    public function settings()
    {
        return view('admin.settings');
    }
}
