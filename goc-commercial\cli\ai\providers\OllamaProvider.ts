import axios, { AxiosInstance } from 'axios';
import { AIProvider, ChatMessage, ChatResponse } from '../AIProviderManager';
import { AIProviderConfig } from '../../config/ConfigManager';
import { Logger } from '../../utils/Logger';

export class OllamaProvider implements AIProvider {
  public readonly name = '<PERSON>llama';
  private client: AxiosInstance;
  private config: AIProviderConfig;
  private logger: Logger;

  constructor(config: AIProviderConfig, logger: Logger) {
    this.config = config;
    this.logger = logger;
    this.client = axios.create({
      baseURL: config.baseUrl || 'http://localhost:11434',
      timeout: 180000, // Increased to 3 minutes for better reliability
      headers: {
        'Content-Type': 'application/json',
      },
    });
  }

  public async isAvailable(): Promise<boolean> {
    try {
      const response = await this.client.get('/api/tags');
      return response.status === 200;
    } catch (error) {
      this.logger.debug(`Ollama availability check failed: ${error}`);
      return false;
    }
  }

  public async chat(messages: ChatMessage[], model?: string): Promise<ChatResponse> {
    try {
      const modelName = model || this.config.defaultModel || 'llama3.2';
      
      // Convert messages to Ollama format
      const prompt = this.formatMessages(messages);
      
      const response = await this.client.post('/api/generate', {
        model: modelName,
        prompt: prompt,
        stream: false,
        options: {
          temperature: 0.7,
          top_p: 0.9,
        }
      });

      if (response.data && response.data.response) {
        return {
          content: response.data.response,
          usage: {
            promptTokens: response.data.prompt_eval_count || 0,
            completionTokens: response.data.eval_count || 0,
            totalTokens: (response.data.prompt_eval_count || 0) + (response.data.eval_count || 0)
          }
        };
      }

      throw new Error('Invalid response from Ollama');
    } catch (error) {
      if (axios.isAxiosError(error)) {
        if (error.response?.status === 404) {
          throw new Error(`Model '${model || this.config.defaultModel}' not found. Please pull the model first.`);
        }
        throw new Error(`Ollama API error: ${error.response?.data?.error || error.message}`);
      }
      throw error;
    }
  }

  public async listModels(): Promise<string[]> {
    try {
      const response = await this.client.get('/api/tags');
      
      if (response.data && response.data.models) {
        return response.data.models.map((model: any) => model.name);
      }
      
      return [];
    } catch (error) {
      this.logger.error(`Failed to list Ollama models: ${error}`);
      return [];
    }
  }

  public async pullModel(modelName: string): Promise<void> {
    try {
      await this.client.post('/api/pull', {
        name: modelName
      });
    } catch (error) {
      throw new Error(`Failed to pull model '${modelName}': ${error}`);
    }
  }

  private formatMessages(messages: ChatMessage[]): string {
    // Convert chat messages to a single prompt for Ollama
    let prompt = '';
    
    for (const message of messages) {
      switch (message.role) {
        case 'system':
          prompt += `System: ${message.content}\n\n`;
          break;
        case 'user':
          prompt += `Human: ${message.content}\n\n`;
          break;
        case 'assistant':
          prompt += `Assistant: ${message.content}\n\n`;
          break;
      }
    }
    
    // Add final prompt for assistant response
    prompt += 'Assistant: ';
    
    return prompt;
  }
}
