{"name": "goc-agent", "version": "1.0.0", "description": "A coding agent with support for multiple AI providers (Ollama, Groq, Gemini, ChatGPT)", "main": "dist/cli.js", "bin": {"goc": "goc.js"}, "scripts": {"build": "tsc", "start": "node dist/cli.js", "clean": "<PERSON><PERSON><PERSON> dist", "train": "node scripts/continuous-training.js", "train:daemon": "node scripts/training-daemon.js start", "train:stop": "node scripts/training-daemon.js stop", "train:status": "node scripts/training-daemon.js status"}, "keywords": ["ai", "coding", "agent", "ollama", "groq", "gemini", "chatgpt", "cli"], "author": "GOC Agent Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-org/goc-agent.git"}, "bugs": {"url": "https://github.com/your-org/goc-agent/issues"}, "homepage": "https://github.com/your-org/goc-agent#readme", "dependencies": {"@alpinejs/collapse": "^3.14.9", "@alpinejs/focus": "^3.14.9", "@babel/parser": "^7.27.5", "@babel/traverse": "^7.27.4", "@babel/types": "^7.27.6", "alpinejs": "^3.14.9", "axios": "^1.9.0", "boxen": "^7.1.1", "chalk": "^5.3.0", "cheerio": "^1.1.0", "chokidar": "^3.5.3", "cli-table3": "^0.6.3", "commander": "^11.1.0", "diff": "^5.1.0", "duckduckgo-search": "^1.0.7", "fast-glob": "^3.3.2", "fuse.js": "^7.1.0", "ignore": "^5.3.0", "inquirer": "^9.2.12", "mime-types": "^2.1.35", "ora": "^7.0.1", "puppeteer": "^24.10.0", "yaml": "^2.3.4"}, "devDependencies": {"@types/babel__parser": "^7.0.0", "@types/babel__traverse": "^7.20.7", "@types/cheerio": "^0.22.35", "@types/diff": "^5.0.8", "@types/inquirer": "^9.0.7", "@types/mime-types": "^2.1.4", "@types/node": "^20.10.0", "rimraf": "^5.0.5", "ts-node": "^10.9.1", "typescript": "^5.3.2"}, "engines": {"node": ">=18.0.0"}, "type": "commonjs", "files": ["dist/**/*", "goc.js", "production.config.yaml", "README.md"]}