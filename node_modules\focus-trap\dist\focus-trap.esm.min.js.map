{"version": 3, "file": "focus-trap.esm.min.js", "sources": ["../index.js"], "sourcesContent": ["import { tabbable, focusable, isFocusable, isTabbable } from 'tabbable';\n\nconst activeFocusTraps = (function () {\n  const trapQueue = [];\n  return {\n    activateTrap(trap) {\n      if (trapQueue.length > 0) {\n        const activeTrap = trapQueue[trapQueue.length - 1];\n        if (activeTrap !== trap) {\n          activeTrap.pause();\n        }\n      }\n\n      const trapIndex = trapQueue.indexOf(trap);\n      if (trapIndex === -1) {\n        trapQueue.push(trap);\n      } else {\n        // move this existing trap to the front of the queue\n        trapQueue.splice(trapIndex, 1);\n        trapQueue.push(trap);\n      }\n    },\n\n    deactivateTrap(trap) {\n      const trapIndex = trapQueue.indexOf(trap);\n      if (trapIndex !== -1) {\n        trapQueue.splice(trapIndex, 1);\n      }\n\n      if (trapQueue.length > 0) {\n        trapQueue[trapQueue.length - 1].unpause();\n      }\n    },\n  };\n})();\n\nconst isSelectableInput = function (node) {\n  return (\n    node.tagName &&\n    node.tagName.toLowerCase() === 'input' &&\n    typeof node.select === 'function'\n  );\n};\n\nconst isEscapeEvent = function (e) {\n  return e.key === 'Escape' || e.key === 'Esc' || e.keyCode === 27;\n};\n\nconst isTabEvent = function (e) {\n  return e.key === 'Tab' || e.keyCode === 9;\n};\n\nconst delay = function (fn) {\n  return setTimeout(fn, 0);\n};\n\n// Array.find/findIndex() are not supported on IE; this replicates enough\n//  of Array.findIndex() for our needs\nconst findIndex = function (arr, fn) {\n  let idx = -1;\n\n  arr.every(function (value, i) {\n    if (fn(value)) {\n      idx = i;\n      return false; // break\n    }\n\n    return true; // next\n  });\n\n  return idx;\n};\n\n/**\n * Get an option's value when it could be a plain value, or a handler that provides\n *  the value.\n * @param {*} value Option's value to check.\n * @param {...*} [params] Any parameters to pass to the handler, if `value` is a function.\n * @returns {*} The `value`, or the handler's returned value.\n */\nconst valueOrHandler = function (value, ...params) {\n  return typeof value === 'function' ? value(...params) : value;\n};\n\nconst getActualTarget = function (event) {\n  // NOTE: If the trap is _inside_ a shadow DOM, event.target will always be the\n  //  shadow host. However, event.target.composedPath() will be an array of\n  //  nodes \"clicked\" from inner-most (the actual element inside the shadow) to\n  //  outer-most (the host HTML document). If we have access to composedPath(),\n  //  then use its first element; otherwise, fall back to event.target (and\n  //  this only works for an _open_ shadow DOM; otherwise,\n  //  composedPath()[0] === event.target always).\n  return event.target.shadowRoot && typeof event.composedPath === 'function'\n    ? event.composedPath()[0]\n    : event.target;\n};\n\nconst createFocusTrap = function (elements, userOptions) {\n  // SSR: a live trap shouldn't be created in this type of environment so this\n  //  should be safe code to execute if the `document` option isn't specified\n  const doc = userOptions?.document || document;\n\n  const config = {\n    returnFocusOnDeactivate: true,\n    escapeDeactivates: true,\n    delayInitialFocus: true,\n    ...userOptions,\n  };\n\n  const state = {\n    // containers given to createFocusTrap()\n    // @type {Array<HTMLElement>}\n    containers: [],\n\n    // list of objects identifying tabbable nodes in `containers` in the trap\n    // NOTE: it's possible that a group has no tabbable nodes if nodes get removed while the trap\n    //  is active, but the trap should never get to a state where there isn't at least one group\n    //  with at least one tabbable node in it (that would lead to an error condition that would\n    //  result in an error being thrown)\n    // @type {Array<{\n    //   container: HTMLElement,\n    //   tabbableNodes: Array<HTMLElement>, // empty if none\n    //   focusableNodes: Array<HTMLElement>, // empty if none\n    //   firstTabbableNode: HTMLElement|null,\n    //   lastTabbableNode: HTMLElement|null,\n    //   nextTabbableNode: (node: HTMLElement, forward: boolean) => HTMLElement|undefined\n    // }>}\n    containerGroups: [], // same order/length as `containers` list\n\n    // references to objects in `containerGroups`, but only those that actually have\n    //  tabbable nodes in them\n    // NOTE: same order as `containers` and `containerGroups`, but __not necessarily__\n    //  the same length\n    tabbableGroups: [],\n\n    nodeFocusedBeforeActivation: null,\n    mostRecentlyFocusedNode: null,\n    active: false,\n    paused: false,\n\n    // timer ID for when delayInitialFocus is true and initial focus in this trap\n    //  has been delayed during activation\n    delayInitialFocusTimer: undefined,\n  };\n\n  let trap; // eslint-disable-line prefer-const -- some private functions reference it, and its methods reference private functions, so we must declare here and define later\n\n  /**\n   * Gets a configuration option value.\n   * @param {Object|undefined} configOverrideOptions If true, and option is defined in this set,\n   *  value will be taken from this object. Otherwise, value will be taken from base configuration.\n   * @param {string} optionName Name of the option whose value is sought.\n   * @param {string|undefined} [configOptionName] Name of option to use __instead of__ `optionName`\n   *  IIF `configOverrideOptions` is not defined. Otherwise, `optionName` is used.\n   */\n  const getOption = (configOverrideOptions, optionName, configOptionName) => {\n    return configOverrideOptions &&\n      configOverrideOptions[optionName] !== undefined\n      ? configOverrideOptions[optionName]\n      : config[configOptionName || optionName];\n  };\n\n  /**\n   * Finds the index of the container that contains the element.\n   * @param {HTMLElement} element\n   * @returns {number} Index of the container in either `state.containers` or\n   *  `state.containerGroups` (the order/length of these lists are the same); -1\n   *  if the element isn't found.\n   */\n  const findContainerIndex = function (element) {\n    // NOTE: search `containerGroups` because it's possible a group contains no tabbable\n    //  nodes, but still contains focusable nodes (e.g. if they all have `tabindex=-1`)\n    //  and we still need to find the element in there\n    return state.containerGroups.findIndex(\n      ({ container, tabbableNodes }) =>\n        container.contains(element) ||\n        // fall back to explicit tabbable search which will take into consideration any\n        //  web components if the `tabbableOptions.getShadowRoot` option was used for\n        //  the trap, enabling shadow DOM support in tabbable (`Node.contains()` doesn't\n        //  look inside web components even if open)\n        tabbableNodes.find((node) => node === element)\n    );\n  };\n\n  /**\n   * Gets the node for the given option, which is expected to be an option that\n   *  can be either a DOM node, a string that is a selector to get a node, `false`\n   *  (if a node is explicitly NOT given), or a function that returns any of these\n   *  values.\n   * @param {string} optionName\n   * @returns {undefined | false | HTMLElement | SVGElement} Returns\n   *  `undefined` if the option is not specified; `false` if the option\n   *  resolved to `false` (node explicitly not given); otherwise, the resolved\n   *  DOM node.\n   * @throws {Error} If the option is set, not `false`, and is not, or does not\n   *  resolve to a node.\n   */\n  const getNodeForOption = function (optionName, ...params) {\n    let optionValue = config[optionName];\n\n    if (typeof optionValue === 'function') {\n      optionValue = optionValue(...params);\n    }\n\n    if (optionValue === true) {\n      optionValue = undefined; // use default value\n    }\n\n    if (!optionValue) {\n      if (optionValue === undefined || optionValue === false) {\n        return optionValue;\n      }\n      // else, empty string (invalid), null (invalid), 0 (invalid)\n\n      throw new Error(\n        `\\`${optionName}\\` was specified but was not a node, or did not return a node`\n      );\n    }\n\n    let node = optionValue; // could be HTMLElement, SVGElement, or non-empty string at this point\n\n    if (typeof optionValue === 'string') {\n      node = doc.querySelector(optionValue); // resolve to node, or null if fails\n      if (!node) {\n        throw new Error(\n          `\\`${optionName}\\` as selector refers to no known node`\n        );\n      }\n    }\n\n    return node;\n  };\n\n  const getInitialFocusNode = function () {\n    let node = getNodeForOption('initialFocus');\n\n    // false explicitly indicates we want no initialFocus at all\n    if (node === false) {\n      return false;\n    }\n\n    if (node === undefined) {\n      // option not specified: use fallback options\n      if (findContainerIndex(doc.activeElement) >= 0) {\n        node = doc.activeElement;\n      } else {\n        const firstTabbableGroup = state.tabbableGroups[0];\n        const firstTabbableNode =\n          firstTabbableGroup && firstTabbableGroup.firstTabbableNode;\n\n        // NOTE: `fallbackFocus` option function cannot return `false` (not supported)\n        node = firstTabbableNode || getNodeForOption('fallbackFocus');\n      }\n    }\n\n    if (!node) {\n      throw new Error(\n        'Your focus-trap needs to have at least one focusable element'\n      );\n    }\n\n    return node;\n  };\n\n  const updateTabbableNodes = function () {\n    state.containerGroups = state.containers.map((container) => {\n      const tabbableNodes = tabbable(container, config.tabbableOptions);\n\n      // NOTE: if we have tabbable nodes, we must have focusable nodes; focusable nodes\n      //  are a superset of tabbable nodes\n      const focusableNodes = focusable(container, config.tabbableOptions);\n\n      return {\n        container,\n        tabbableNodes,\n        focusableNodes,\n        firstTabbableNode: tabbableNodes.length > 0 ? tabbableNodes[0] : null,\n        lastTabbableNode:\n          tabbableNodes.length > 0\n            ? tabbableNodes[tabbableNodes.length - 1]\n            : null,\n\n        /**\n         * Finds the __tabbable__ node that follows the given node in the specified direction,\n         *  in this container, if any.\n         * @param {HTMLElement} node\n         * @param {boolean} [forward] True if going in forward tab order; false if going\n         *  in reverse.\n         * @returns {HTMLElement|undefined} The next tabbable node, if any.\n         */\n        nextTabbableNode(node, forward = true) {\n          // NOTE: If tabindex is positive (in order to manipulate the tab order separate\n          //  from the DOM order), this __will not work__ because the list of focusableNodes,\n          //  while it contains tabbable nodes, does not sort its nodes in any order other\n          //  than DOM order, because it can't: Where would you place focusable (but not\n          //  tabbable) nodes in that order? They have no order, because they aren't tabbale...\n          // Support for positive tabindex is already broken and hard to manage (possibly\n          //  not supportable, TBD), so this isn't going to make things worse than they\n          //  already are, and at least makes things better for the majority of cases where\n          //  tabindex is either 0/unset or negative.\n          // FYI, positive tabindex issue: https://github.com/focus-trap/focus-trap/issues/375\n          const nodeIdx = focusableNodes.findIndex((n) => n === node);\n          if (nodeIdx < 0) {\n            return undefined;\n          }\n\n          if (forward) {\n            return focusableNodes\n              .slice(nodeIdx + 1)\n              .find((n) => isTabbable(n, config.tabbableOptions));\n          }\n\n          return focusableNodes\n            .slice(0, nodeIdx)\n            .reverse()\n            .find((n) => isTabbable(n, config.tabbableOptions));\n        },\n      };\n    });\n\n    state.tabbableGroups = state.containerGroups.filter(\n      (group) => group.tabbableNodes.length > 0\n    );\n\n    // throw if no groups have tabbable nodes and we don't have a fallback focus node either\n    if (\n      state.tabbableGroups.length <= 0 &&\n      !getNodeForOption('fallbackFocus') // returning false not supported for this option\n    ) {\n      throw new Error(\n        'Your focus-trap must have at least one container with at least one tabbable node in it at all times'\n      );\n    }\n  };\n\n  const tryFocus = function (node) {\n    if (node === false) {\n      return;\n    }\n\n    if (node === doc.activeElement) {\n      return;\n    }\n\n    if (!node || !node.focus) {\n      tryFocus(getInitialFocusNode());\n      return;\n    }\n\n    node.focus({ preventScroll: !!config.preventScroll });\n    state.mostRecentlyFocusedNode = node;\n\n    if (isSelectableInput(node)) {\n      node.select();\n    }\n  };\n\n  const getReturnFocusNode = function (previousActiveElement) {\n    const node = getNodeForOption('setReturnFocus', previousActiveElement);\n    return node ? node : node === false ? false : previousActiveElement;\n  };\n\n  // This needs to be done on mousedown and touchstart instead of click\n  // so that it precedes the focus event.\n  const checkPointerDown = function (e) {\n    const target = getActualTarget(e);\n\n    if (findContainerIndex(target) >= 0) {\n      // allow the click since it ocurred inside the trap\n      return;\n    }\n\n    if (valueOrHandler(config.clickOutsideDeactivates, e)) {\n      // immediately deactivate the trap\n      trap.deactivate({\n        // if, on deactivation, we should return focus to the node originally-focused\n        //  when the trap was activated (or the configured `setReturnFocus` node),\n        //  then assume it's also OK to return focus to the outside node that was\n        //  just clicked, causing deactivation, as long as that node is focusable;\n        //  if it isn't focusable, then return focus to the original node focused\n        //  on activation (or the configured `setReturnFocus` node)\n        // NOTE: by setting `returnFocus: false`, deactivate() will do nothing,\n        //  which will result in the outside click setting focus to the node\n        //  that was clicked, whether it's focusable or not; by setting\n        //  `returnFocus: true`, we'll attempt to re-focus the node originally-focused\n        //  on activation (or the configured `setReturnFocus` node)\n        returnFocus:\n          config.returnFocusOnDeactivate &&\n          !isFocusable(target, config.tabbableOptions),\n      });\n      return;\n    }\n\n    // This is needed for mobile devices.\n    // (If we'll only let `click` events through,\n    // then on mobile they will be blocked anyways if `touchstart` is blocked.)\n    if (valueOrHandler(config.allowOutsideClick, e)) {\n      // allow the click outside the trap to take place\n      return;\n    }\n\n    // otherwise, prevent the click\n    e.preventDefault();\n  };\n\n  // In case focus escapes the trap for some strange reason, pull it back in.\n  const checkFocusIn = function (e) {\n    const target = getActualTarget(e);\n    const targetContained = findContainerIndex(target) >= 0;\n\n    // In Firefox when you Tab out of an iframe the Document is briefly focused.\n    if (targetContained || target instanceof Document) {\n      if (targetContained) {\n        state.mostRecentlyFocusedNode = target;\n      }\n    } else {\n      // escaped! pull it back in to where it just left\n      e.stopImmediatePropagation();\n      tryFocus(state.mostRecentlyFocusedNode || getInitialFocusNode());\n    }\n  };\n\n  // Hijack Tab events on the first and last focusable nodes of the trap,\n  // in order to prevent focus from escaping. If it escapes for even a\n  // moment it can end up scrolling the page and causing confusion so we\n  // kind of need to capture the action at the keydown phase.\n  const checkTab = function (e) {\n    const target = getActualTarget(e);\n    updateTabbableNodes();\n\n    let destinationNode = null;\n\n    if (state.tabbableGroups.length > 0) {\n      // make sure the target is actually contained in a group\n      // NOTE: the target may also be the container itself if it's focusable\n      //  with tabIndex='-1' and was given initial focus\n      const containerIndex = findContainerIndex(target);\n      const containerGroup =\n        containerIndex >= 0 ? state.containerGroups[containerIndex] : undefined;\n\n      if (containerIndex < 0) {\n        // target not found in any group: quite possible focus has escaped the trap,\n        //  so bring it back in to...\n        if (e.shiftKey) {\n          // ...the last node in the last group\n          destinationNode =\n            state.tabbableGroups[state.tabbableGroups.length - 1]\n              .lastTabbableNode;\n        } else {\n          // ...the first node in the first group\n          destinationNode = state.tabbableGroups[0].firstTabbableNode;\n        }\n      } else if (e.shiftKey) {\n        // REVERSE\n\n        // is the target the first tabbable node in a group?\n        let startOfGroupIndex = findIndex(\n          state.tabbableGroups,\n          ({ firstTabbableNode }) => target === firstTabbableNode\n        );\n\n        if (\n          startOfGroupIndex < 0 &&\n          (containerGroup.container === target ||\n            (isFocusable(target, config.tabbableOptions) &&\n              !isTabbable(target, config.tabbableOptions) &&\n              !containerGroup.nextTabbableNode(target, false)))\n        ) {\n          // an exception case where the target is either the container itself, or\n          //  a non-tabbable node that was given focus (i.e. tabindex is negative\n          //  and user clicked on it or node was programmatically given focus)\n          //  and is not followed by any other tabbable node, in which\n          //  case, we should handle shift+tab as if focus were on the container's\n          //  first tabbable node, and go to the last tabbable node of the LAST group\n          startOfGroupIndex = containerIndex;\n        }\n\n        if (startOfGroupIndex >= 0) {\n          // YES: then shift+tab should go to the last tabbable node in the\n          //  previous group (and wrap around to the last tabbable node of\n          //  the LAST group if it's the first tabbable node of the FIRST group)\n          const destinationGroupIndex =\n            startOfGroupIndex === 0\n              ? state.tabbableGroups.length - 1\n              : startOfGroupIndex - 1;\n\n          const destinationGroup = state.tabbableGroups[destinationGroupIndex];\n          destinationNode = destinationGroup.lastTabbableNode;\n        }\n      } else {\n        // FORWARD\n\n        // is the target the last tabbable node in a group?\n        let lastOfGroupIndex = findIndex(\n          state.tabbableGroups,\n          ({ lastTabbableNode }) => target === lastTabbableNode\n        );\n\n        if (\n          lastOfGroupIndex < 0 &&\n          (containerGroup.container === target ||\n            (isFocusable(target, config.tabbableOptions) &&\n              !isTabbable(target, config.tabbableOptions) &&\n              !containerGroup.nextTabbableNode(target)))\n        ) {\n          // an exception case where the target is the container itself, or\n          //  a non-tabbable node that was given focus (i.e. tabindex is negative\n          //  and user clicked on it or node was programmatically given focus)\n          //  and is not followed by any other tabbable node, in which\n          //  case, we should handle tab as if focus were on the container's\n          //  last tabbable node, and go to the first tabbable node of the FIRST group\n          lastOfGroupIndex = containerIndex;\n        }\n\n        if (lastOfGroupIndex >= 0) {\n          // YES: then tab should go to the first tabbable node in the next\n          //  group (and wrap around to the first tabbable node of the FIRST\n          //  group if it's the last tabbable node of the LAST group)\n          const destinationGroupIndex =\n            lastOfGroupIndex === state.tabbableGroups.length - 1\n              ? 0\n              : lastOfGroupIndex + 1;\n\n          const destinationGroup = state.tabbableGroups[destinationGroupIndex];\n          destinationNode = destinationGroup.firstTabbableNode;\n        }\n      }\n    } else {\n      // NOTE: the fallbackFocus option does not support returning false to opt-out\n      destinationNode = getNodeForOption('fallbackFocus');\n    }\n\n    if (destinationNode) {\n      e.preventDefault();\n      tryFocus(destinationNode);\n    }\n    // else, let the browser take care of [shift+]tab and move the focus\n  };\n\n  const checkKey = function (e) {\n    if (\n      isEscapeEvent(e) &&\n      valueOrHandler(config.escapeDeactivates, e) !== false\n    ) {\n      e.preventDefault();\n      trap.deactivate();\n      return;\n    }\n\n    if (isTabEvent(e)) {\n      checkTab(e);\n      return;\n    }\n  };\n\n  const checkClick = function (e) {\n    const target = getActualTarget(e);\n\n    if (findContainerIndex(target) >= 0) {\n      return;\n    }\n\n    if (valueOrHandler(config.clickOutsideDeactivates, e)) {\n      return;\n    }\n\n    if (valueOrHandler(config.allowOutsideClick, e)) {\n      return;\n    }\n\n    e.preventDefault();\n    e.stopImmediatePropagation();\n  };\n\n  //\n  // EVENT LISTENERS\n  //\n\n  const addListeners = function () {\n    if (!state.active) {\n      return;\n    }\n\n    // There can be only one listening focus trap at a time\n    activeFocusTraps.activateTrap(trap);\n\n    // Delay ensures that the focused element doesn't capture the event\n    // that caused the focus trap activation.\n    state.delayInitialFocusTimer = config.delayInitialFocus\n      ? delay(function () {\n          tryFocus(getInitialFocusNode());\n        })\n      : tryFocus(getInitialFocusNode());\n\n    doc.addEventListener('focusin', checkFocusIn, true);\n    doc.addEventListener('mousedown', checkPointerDown, {\n      capture: true,\n      passive: false,\n    });\n    doc.addEventListener('touchstart', checkPointerDown, {\n      capture: true,\n      passive: false,\n    });\n    doc.addEventListener('click', checkClick, {\n      capture: true,\n      passive: false,\n    });\n    doc.addEventListener('keydown', checkKey, {\n      capture: true,\n      passive: false,\n    });\n\n    return trap;\n  };\n\n  const removeListeners = function () {\n    if (!state.active) {\n      return;\n    }\n\n    doc.removeEventListener('focusin', checkFocusIn, true);\n    doc.removeEventListener('mousedown', checkPointerDown, true);\n    doc.removeEventListener('touchstart', checkPointerDown, true);\n    doc.removeEventListener('click', checkClick, true);\n    doc.removeEventListener('keydown', checkKey, true);\n\n    return trap;\n  };\n\n  //\n  // TRAP DEFINITION\n  //\n\n  trap = {\n    get active() {\n      return state.active;\n    },\n\n    get paused() {\n      return state.paused;\n    },\n\n    activate(activateOptions) {\n      if (state.active) {\n        return this;\n      }\n\n      const onActivate = getOption(activateOptions, 'onActivate');\n      const onPostActivate = getOption(activateOptions, 'onPostActivate');\n      const checkCanFocusTrap = getOption(activateOptions, 'checkCanFocusTrap');\n\n      if (!checkCanFocusTrap) {\n        updateTabbableNodes();\n      }\n\n      state.active = true;\n      state.paused = false;\n      state.nodeFocusedBeforeActivation = doc.activeElement;\n\n      if (onActivate) {\n        onActivate();\n      }\n\n      const finishActivation = () => {\n        if (checkCanFocusTrap) {\n          updateTabbableNodes();\n        }\n        addListeners();\n        if (onPostActivate) {\n          onPostActivate();\n        }\n      };\n\n      if (checkCanFocusTrap) {\n        checkCanFocusTrap(state.containers.concat()).then(\n          finishActivation,\n          finishActivation\n        );\n        return this;\n      }\n\n      finishActivation();\n      return this;\n    },\n\n    deactivate(deactivateOptions) {\n      if (!state.active) {\n        return this;\n      }\n\n      const options = {\n        onDeactivate: config.onDeactivate,\n        onPostDeactivate: config.onPostDeactivate,\n        checkCanReturnFocus: config.checkCanReturnFocus,\n        ...deactivateOptions,\n      };\n\n      clearTimeout(state.delayInitialFocusTimer); // noop if undefined\n      state.delayInitialFocusTimer = undefined;\n\n      removeListeners();\n      state.active = false;\n      state.paused = false;\n\n      activeFocusTraps.deactivateTrap(trap);\n\n      const onDeactivate = getOption(options, 'onDeactivate');\n      const onPostDeactivate = getOption(options, 'onPostDeactivate');\n      const checkCanReturnFocus = getOption(options, 'checkCanReturnFocus');\n      const returnFocus = getOption(\n        options,\n        'returnFocus',\n        'returnFocusOnDeactivate'\n      );\n\n      if (onDeactivate) {\n        onDeactivate();\n      }\n\n      const finishDeactivation = () => {\n        delay(() => {\n          if (returnFocus) {\n            tryFocus(getReturnFocusNode(state.nodeFocusedBeforeActivation));\n          }\n          if (onPostDeactivate) {\n            onPostDeactivate();\n          }\n        });\n      };\n\n      if (returnFocus && checkCanReturnFocus) {\n        checkCanReturnFocus(\n          getReturnFocusNode(state.nodeFocusedBeforeActivation)\n        ).then(finishDeactivation, finishDeactivation);\n        return this;\n      }\n\n      finishDeactivation();\n      return this;\n    },\n\n    pause() {\n      if (state.paused || !state.active) {\n        return this;\n      }\n\n      state.paused = true;\n      removeListeners();\n\n      return this;\n    },\n\n    unpause() {\n      if (!state.paused || !state.active) {\n        return this;\n      }\n\n      state.paused = false;\n      updateTabbableNodes();\n      addListeners();\n\n      return this;\n    },\n\n    updateContainerElements(containerElements) {\n      const elementsAsArray = [].concat(containerElements).filter(Boolean);\n\n      state.containers = elementsAsArray.map((element) =>\n        typeof element === 'string' ? doc.querySelector(element) : element\n      );\n\n      if (state.active) {\n        updateTabbableNodes();\n      }\n\n      return this;\n    },\n  };\n\n  // initialize container elements\n  trap.updateContainerElements(elements);\n\n  return trap;\n};\n\nexport { createFocusTrap };\n"], "names": ["trapQueue", "activeFocusTraps", "activateTrap", "trap", "length", "activeTrap", "pause", "trapIndex", "indexOf", "splice", "push", "deactivateTrap", "unpause", "delay", "fn", "setTimeout", "findIndex", "arr", "idx", "every", "value", "i", "valueOrHandler", "_len", "arguments", "params", "Array", "_key", "apply", "getActualTarget", "event", "target", "shadowRoot", "<PERSON><PERSON><PERSON>", "createFocusTrap", "elements", "userOptions", "doc", "document", "config", "_objectSpread", "returnFocusOnDeactivate", "escapeDeactivates", "delayInitialFocus", "state", "containers", "containerGroups", "tabbableGroups", "nodeFocusedBeforeActivation", "mostRecentlyFocusedNode", "active", "paused", "delayInitialFocusTimer", "undefined", "getOption", "configOverrideOptions", "optionName", "configOptionName", "findContainerIndex", "element", "_ref", "container", "tabbableNodes", "contains", "find", "node", "getNodeForOption", "optionValue", "_len2", "_key2", "Error", "querySelector", "getInitialFocusNode", "activeElement", "firstTabbableGroup", "firstTabbableNode", "updateTabbableNodes", "map", "tabbable", "tabbableOptions", "focusableNodes", "focusable", "lastTabbableNode", "nextTabbableNode", "forward", "nodeIdx", "n", "slice", "isTabbable", "reverse", "filter", "group", "tryFocus", "focus", "preventScroll", "tagName", "toLowerCase", "select", "isSelectableInput", "getReturnFocusNode", "previousActiveElement", "checkPointerDown", "e", "clickOutsideDeactivates", "deactivate", "returnFocus", "isFocusable", "allowOutsideClick", "preventDefault", "checkFocusIn", "targetContained", "Document", "stopImmediatePropagation", "<PERSON><PERSON><PERSON>", "key", "keyCode", "isEscapeEvent", "isTabEvent", "destinationNode", "containerIndex", "containerGroup", "shift<PERSON>ey", "startOfGroupIndex", "_ref2", "destinationGroupIndex", "lastOfGroupIndex", "_ref3", "checkTab", "checkClick", "addListeners", "addEventListener", "capture", "passive", "removeListeners", "removeEventListener", "activate", "activateOptions", "this", "onActivate", "onPostActivate", "checkCanFocusTrap", "finishActivation", "concat", "then", "deactivateOptions", "options", "onDeactivate", "onPostDeactivate", "checkCanReturnFocus", "clearTimeout", "finishDeactivation", "updateContainerElements", "containerElements", "elementsAsArray", "Boolean"], "mappings": ";;;;2wBAEA,IACQA,EADFC,GACED,EAAY,GACX,CACLE,aADK,SACQC,GACX,GAAIH,EAAUI,OAAS,EAAG,CACxB,IAAMC,EAAaL,EAAUA,EAAUI,OAAS,GAC5CC,IAAeF,GACjBE,EAAWC,QAIf,IAAMC,EAAYP,EAAUQ,QAAQL,IACjB,IAAfI,GAIFP,EAAUS,OAAOF,EAAW,GAH5BP,EAAUU,KAAKP,IAQnBQ,eAnBK,SAmBUR,GACb,IAAMI,EAAYP,EAAUQ,QAAQL,IACjB,IAAfI,GACFP,EAAUS,OAAOF,EAAW,GAG1BP,EAAUI,OAAS,GACrBJ,EAAUA,EAAUI,OAAS,GAAGQ,aAsBlCC,EAAQ,SAAUC,GACtB,OAAOC,WAAWD,EAAI,IAKlBE,EAAY,SAAUC,EAAKH,GAC/B,IAAII,GAAO,EAWX,OATAD,EAAIE,OAAM,SAAUC,EAAOC,GACzB,OAAIP,EAAGM,KACLF,EAAMG,GACC,MAMJH,GAUHI,EAAiB,SAAUF,GAAkB,IAAA,IAAAG,EAAAC,UAAApB,OAARqB,EAAQ,IAAAC,MAAAH,EAAA,EAAAA,EAAA,EAAA,GAAAI,EAAA,EAAAA,EAAAJ,EAAAI,IAARF,EAAQE,EAAA,GAAAH,UAAAG,GACjD,MAAwB,mBAAVP,EAAuBA,EAAKQ,WAAIH,EAAAA,GAAUL,GAGpDS,EAAkB,SAAUC,GAQhC,OAAOA,EAAMC,OAAOC,YAA4C,mBAAvBF,EAAMG,aAC3CH,EAAMG,eAAe,GACrBH,EAAMC,QAGNG,EAAkB,SAAUC,EAAUC,GAG1C,IA6CIjC,EA7CEkC,GAAMD,MAAAA,OAAAA,EAAAA,EAAaE,WAAYA,SAE/BC,EAAMC,EAAA,CACVC,yBAAyB,EACzBC,mBAAmB,EACnBC,mBAAmB,GAChBP,GAGCQ,EAAQ,CAGZC,WAAY,GAeZC,gBAAiB,GAMjBC,eAAgB,GAEhBC,4BAA6B,KAC7BC,wBAAyB,KACzBC,QAAQ,EACRC,QAAQ,EAIRC,4BAAwBC,GAapBC,EAAY,SAACC,EAAuBC,EAAYC,GACpD,OAAOF,QACiCF,IAAtCE,EAAsBC,GACpBD,EAAsBC,GACtBjB,EAAOkB,GAAoBD,IAU3BE,EAAqB,SAAUC,GAInC,OAAOf,EAAME,gBAAgB9B,WAC3B,SAAA4C,GAAA,IAAGC,IAAAA,UAAWC,IAAAA,cAAd,OACED,EAAUE,SAASJ,IAKnBG,EAAcE,MAAK,SAACC,GAAD,OAAUA,IAASN,SAiBtCO,EAAmB,SAAUV,GACjC,IAAIW,EAAc5B,EAAOiB,GAEzB,GAA2B,mBAAhBW,EAA4B,CAAA,IAAA,IAAAC,EAAA5C,UAAApB,OAHSqB,EAGT,IAAAC,MAAA0C,EAAA,EAAAA,EAAA,EAAA,GAAAC,EAAA,EAAAA,EAAAD,EAAAC,IAHS5C,EAGT4C,EAAA,GAAA7C,UAAA6C,GACrCF,EAAcA,EAAAvC,WAAA,EAAeH,GAO/B,IAJoB,IAAhB0C,IACFA,OAAcd,IAGXc,EAAa,CAChB,QAAoBd,IAAhBc,IAA6C,IAAhBA,EAC/B,OAAOA,EAIT,MAAM,IAAIG,MACHd,IAAAA,OAAAA,EADP,iEAKF,IAAIS,EAAOE,EAEX,GAA2B,iBAAhBA,KACTF,EAAO5B,EAAIkC,cAAcJ,IAEvB,MAAM,IAAIG,MACHd,IAAAA,OAAAA,EADP,0CAMJ,OAAOS,GAGHO,EAAsB,WAC1B,IAAIP,EAAOC,EAAiB,gBAG5B,IAAa,IAATD,EACF,OAAO,EAGT,QAAaZ,IAATY,EAEF,GAAIP,EAAmBrB,EAAIoC,gBAAkB,EAC3CR,EAAO5B,EAAIoC,kBACN,CACL,IAAMC,EAAqB9B,EAAMG,eAAe,GAKhDkB,EAHES,GAAsBA,EAAmBC,mBAGfT,EAAiB,iBAIjD,IAAKD,EACH,MAAM,IAAIK,MACR,gEAIJ,OAAOL,GAGHW,EAAsB,WA6D1B,GA5DAhC,EAAME,gBAAkBF,EAAMC,WAAWgC,KAAI,SAAChB,GAC5C,IAAMC,EAAgBgB,EAASjB,EAAWtB,EAAOwC,iBAI3CC,EAAiBC,EAAUpB,EAAWtB,EAAOwC,iBAEnD,MAAO,CACLlB,UAAAA,EACAC,cAAAA,EACAkB,eAAAA,EACAL,kBAAmBb,EAAc1D,OAAS,EAAI0D,EAAc,GAAK,KACjEoB,iBACEpB,EAAc1D,OAAS,EACnB0D,EAAcA,EAAc1D,OAAS,GACrC,KAUN+E,iBAlBK,SAkBYlB,GAAsB,IAAhBmB,6DAWfC,EAAUL,EAAehE,WAAU,SAACsE,GAAD,OAAOA,IAAMrB,KACtD,KAAIoB,EAAU,GAId,OAAID,EACKJ,EACJO,MAAMF,EAAU,GAChBrB,MAAK,SAACsB,GAAD,OAAOE,EAAWF,EAAG/C,EAAOwC,oBAG/BC,EACJO,MAAM,EAAGF,GACTI,UACAzB,MAAK,SAACsB,GAAD,OAAOE,EAAWF,EAAG/C,EAAOwC,yBAK1CnC,EAAMG,eAAiBH,EAAME,gBAAgB4C,QAC3C,SAACC,GAAD,OAAWA,EAAM7B,cAAc1D,OAAS,KAKxCwC,EAAMG,eAAe3C,QAAU,IAC9B8D,EAAiB,iBAElB,MAAM,IAAII,MACR,wGAKAsB,EAAW,SAAXA,EAAqB3B,IACZ,IAATA,GAIAA,IAAS5B,EAAIoC,gBAIZR,GAASA,EAAK4B,OAKnB5B,EAAK4B,MAAM,CAAEC,gBAAiBvD,EAAOuD,gBACrClD,EAAMK,wBAA0BgB,EA1TV,SAAUA,GAClC,OACEA,EAAK8B,SAC0B,UAA/B9B,EAAK8B,QAAQC,eACU,mBAAhB/B,EAAKgC,OAwTRC,CAAkBjC,IACpBA,EAAKgC,UARLL,EAASpB,OAYP2B,EAAqB,SAAUC,GACnC,IAAMnC,EAAOC,EAAiB,iBAAkBkC,GAChD,OAAOnC,IAAuB,IAATA,GAAyBmC,GAK1CC,EAAmB,SAAUC,GACjC,IAAMvE,EAASF,EAAgByE,GAE3B5C,EAAmB3B,IAAW,IAK9BT,EAAeiB,EAAOgE,wBAAyBD,GAEjDnG,EAAKqG,WAAW,CAYdC,YACElE,EAAOE,0BACNiE,EAAY3E,EAAQQ,EAAOwC,mBAQ9BzD,EAAeiB,EAAOoE,kBAAmBL,IAM7CA,EAAEM,mBAIEC,EAAe,SAAUP,GAC7B,IAAMvE,EAASF,EAAgByE,GACzBQ,EAAkBpD,EAAmB3B,IAAW,EAGlD+E,GAAmB/E,aAAkBgF,SACnCD,IACFlE,EAAMK,wBAA0BlB,IAIlCuE,EAAEU,2BACFpB,EAAShD,EAAMK,yBAA2BuB,OAyHxCyC,EAAW,SAAUX,GACzB,GAhfkB,SAAUA,GAC9B,MAAiB,WAAVA,EAAEY,KAA8B,QAAVZ,EAAEY,KAA+B,KAAdZ,EAAEa,QAgf9CC,CAAcd,KACkC,IAAhDhF,EAAeiB,EAAOG,kBAAmB4D,GAIzC,OAFAA,EAAEM,sBACFzG,EAAKqG,cAjfQ,SAAUF,GAC3B,MAAiB,QAAVA,EAAEY,KAA+B,IAAdZ,EAAEa,SAoftBE,CAAWf,IA3HA,SAAUA,GACzB,IAAMvE,EAASF,EAAgByE,GAC/B1B,IAEA,IAAI0C,EAAkB,KAEtB,GAAI1E,EAAMG,eAAe3C,OAAS,EAAG,CAInC,IAAMmH,EAAiB7D,EAAmB3B,GACpCyF,EACJD,GAAkB,EAAI3E,EAAME,gBAAgByE,QAAkBlE,EAEhE,GAAIkE,EAAiB,EAKjBD,EAFEhB,EAAEmB,SAGF7E,EAAMG,eAAeH,EAAMG,eAAe3C,OAAS,GAChD8E,iBAGatC,EAAMG,eAAe,GAAG4B,uBAEvC,GAAI2B,EAAEmB,SAAU,CAIrB,IAAIC,EAAoB1G,EACtB4B,EAAMG,gBACN,SAAA4E,GAAA,IAAGhD,IAAAA,kBAAH,OAA2B5C,IAAW4C,KAmBxC,GAfE+C,EAAoB,IACnBF,EAAe3D,YAAc9B,GAC3B2E,EAAY3E,EAAQQ,EAAOwC,mBACzBS,EAAWzD,EAAQQ,EAAOwC,mBAC1ByC,EAAerC,iBAAiBpD,GAAQ,MAQ7C2F,EAAoBH,GAGlBG,GAAqB,EAAG,CAI1B,IAAME,EACkB,IAAtBF,EACI9E,EAAMG,eAAe3C,OAAS,EAC9BsH,EAAoB,EAG1BJ,EADyB1E,EAAMG,eAAe6E,GACX1C,sBAEhC,CAIL,IAAI2C,EAAmB7G,EACrB4B,EAAMG,gBACN,SAAA+E,GAAA,IAAG5C,IAAAA,iBAAH,OAA0BnD,IAAWmD,KAmBvC,GAfE2C,EAAmB,IAClBL,EAAe3D,YAAc9B,GAC3B2E,EAAY3E,EAAQQ,EAAOwC,mBACzBS,EAAWzD,EAAQQ,EAAOwC,mBAC1ByC,EAAerC,iBAAiBpD,MAQrC8F,EAAmBN,GAGjBM,GAAoB,EAAG,CAIzB,IAAMD,EACJC,IAAqBjF,EAAMG,eAAe3C,OAAS,EAC/C,EACAyH,EAAmB,EAGzBP,EADyB1E,EAAMG,eAAe6E,GACXjD,yBAKvC2C,EAAkBpD,EAAiB,iBAGjCoD,IACFhB,EAAEM,iBACFhB,EAAS0B,IAgBTS,CAASzB,IAKP0B,EAAa,SAAU1B,GAC3B,IAAMvE,EAASF,EAAgByE,GAE3B5C,EAAmB3B,IAAW,GAI9BT,EAAeiB,EAAOgE,wBAAyBD,IAI/ChF,EAAeiB,EAAOoE,kBAAmBL,KAI7CA,EAAEM,iBACFN,EAAEU,6BAOEiB,EAAe,WACnB,GAAKrF,EAAMM,OAiCX,OA5BAjD,EAAiBC,aAAaC,GAI9ByC,EAAMQ,uBAAyBb,EAAOI,kBAClC9B,GAAM,WACJ+E,EAASpB,QAEXoB,EAASpB,KAEbnC,EAAI6F,iBAAiB,UAAWrB,GAAc,GAC9CxE,EAAI6F,iBAAiB,YAAa7B,EAAkB,CAClD8B,SAAS,EACTC,SAAS,IAEX/F,EAAI6F,iBAAiB,aAAc7B,EAAkB,CACnD8B,SAAS,EACTC,SAAS,IAEX/F,EAAI6F,iBAAiB,QAASF,EAAY,CACxCG,SAAS,EACTC,SAAS,IAEX/F,EAAI6F,iBAAiB,UAAWjB,EAAU,CACxCkB,SAAS,EACTC,SAAS,IAGJjI,GAGHkI,EAAkB,WACtB,GAAKzF,EAAMM,OAUX,OANAb,EAAIiG,oBAAoB,UAAWzB,GAAc,GACjDxE,EAAIiG,oBAAoB,YAAajC,GAAkB,GACvDhE,EAAIiG,oBAAoB,aAAcjC,GAAkB,GACxDhE,EAAIiG,oBAAoB,QAASN,GAAY,GAC7C3F,EAAIiG,oBAAoB,UAAWrB,GAAU,GAEtC9G,GA4JT,OArJAA,EAAO,CACL+C,aACE,OAAON,EAAMM,QAGfC,aACE,OAAOP,EAAMO,QAGfoF,SATK,SASIC,GACP,GAAI5F,EAAMM,OACR,OAAOuF,KAGT,IAAMC,EAAapF,EAAUkF,EAAiB,cACxCG,EAAiBrF,EAAUkF,EAAiB,kBAC5CI,EAAoBtF,EAAUkF,EAAiB,qBAEhDI,GACHhE,IAGFhC,EAAMM,QAAS,EACfN,EAAMO,QAAS,EACfP,EAAMI,4BAA8BX,EAAIoC,cAEpCiE,GACFA,IAGF,IAAMG,EAAmB,WACnBD,GACFhE,IAEFqD,IACIU,GACFA,KAIJ,OAAIC,GACFA,EAAkBhG,EAAMC,WAAWiG,UAAUC,KAC3CF,EACAA,GAEKJ,OAGTI,IACOJ,OAGTjC,WApDK,SAoDMwC,GACT,IAAKpG,EAAMM,OACT,OAAOuF,KAGT,IAAMQ,EAAOzG,EAAA,CACX0G,aAAc3G,EAAO2G,aACrBC,iBAAkB5G,EAAO4G,iBACzBC,oBAAqB7G,EAAO6G,qBACzBJ,GAGLK,aAAazG,EAAMQ,wBACnBR,EAAMQ,4BAAyBC,EAE/BgF,IACAzF,EAAMM,QAAS,EACfN,EAAMO,QAAS,EAEflD,EAAiBU,eAAeR,GAEhC,IAAM+I,EAAe5F,EAAU2F,EAAS,gBAClCE,EAAmB7F,EAAU2F,EAAS,oBACtCG,EAAsB9F,EAAU2F,EAAS,uBACzCxC,EAAcnD,EAClB2F,EACA,cACA,2BAGEC,GACFA,IAGF,IAAMI,EAAqB,WACzBzI,GAAM,WACA4F,GACFb,EAASO,EAAmBvD,EAAMI,8BAEhCmG,GACFA,QAKN,OAAI1C,GAAe2C,GACjBA,EACEjD,EAAmBvD,EAAMI,8BACzB+F,KAAKO,EAAoBA,GACpBb,OAGTa,IACOb,OAGTnI,MAAQ,WACN,OAAIsC,EAAMO,SAAWP,EAAMM,SAI3BN,EAAMO,QAAS,EACfkF,KAJSI,MASX7H,QAAU,WACR,OAAKgC,EAAMO,QAAWP,EAAMM,QAI5BN,EAAMO,QAAS,EACfyB,IACAqD,IAEOQ,MAPEA,MAUXc,wBAnIK,SAmImBC,GACtB,IAAMC,EAAkB,GAAGX,OAAOU,GAAmB9D,OAAOgE,SAU5D,OARA9G,EAAMC,WAAa4G,EAAgB5E,KAAI,SAAClB,GAAD,MAClB,iBAAZA,EAAuBtB,EAAIkC,cAAcZ,GAAWA,KAGzDf,EAAMM,QACR0B,IAGK6D,QAKNc,wBAAwBpH,GAEtBhC"}