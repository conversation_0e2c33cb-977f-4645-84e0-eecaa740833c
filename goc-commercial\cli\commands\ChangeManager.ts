import inquirer from 'inquirer';
import chalk from 'chalk';
import Table from 'cli-table3';
import { ChangeTracker } from '../utils/ChangeTracker';
import { Logger } from '../utils/Logger';

export interface ChangesCommandOptions {
  summary?: boolean;
}

export interface RevertCommandOptions {
  all?: boolean;
  file?: string;
}

export class ChangeManager {
  private changeTracker: ChangeTracker;
  private logger: Logger;

  constructor(changeTracker: ChangeTracker, logger: Logger) {
    this.changeTracker = changeTracker;
    this.logger = logger;
  }

  public async handleChangesCommand(options: ChangesCommandOptions = {}): Promise<void> {
    const changes = this.changeTracker.getSessionChanges();
    
    if (changes.length === 0) {
      this.logger.info('No changes recorded in this session');
      return;
    }

    if (options.summary) {
      this.displayDetailedSummary();
    } else {
      this.displayChangesTable();
    }
  }

  private displayChangesTable(): void {
    const changes = this.changeTracker.getSessionChanges();
    
    const table = new Table({
      head: ['File', 'Operation', 'Lines +/-', 'Time'],
      colWidths: [40, 12, 15, 12]
    });

    changes.forEach(change => {
      const addedText = change.linesAdded > 0 ? chalk.green(`+${change.linesAdded}`) : '';
      const deletedText = change.linesDeleted > 0 ? chalk.red(`-${change.linesDeleted}`) : '';
      const changesText = [addedText, deletedText].filter(Boolean).join(' ') || '-';
      
      const operationColor = change.operation === 'created' ? chalk.green : 
                            change.operation === 'edited' ? chalk.yellow : chalk.red;
      
      table.push([
        change.filename,
        operationColor(change.operation),
        changesText,
        change.timestamp.toLocaleTimeString()
      ]);
    });

    console.log('\n📋 Session Changes:');
    console.log(table.toString());
    
    const summary = this.changeTracker.getChangesSummary();
    console.log(chalk.bold(`\nTotal: ${summary.totalFiles} files, ${chalk.green(`+${summary.totalLinesAdded}`)} ${chalk.red(`-${summary.totalLinesDeleted}`)} lines`));
  }

  private displayDetailedSummary(): void {
    this.changeTracker.displayChangesSummary();
  }

  public async handleRevertCommand(options: RevertCommandOptions = {}): Promise<void> {
    const changes = this.changeTracker.getSessionChanges();
    
    if (changes.length === 0) {
      this.logger.info('No changes to revert');
      return;
    }

    if (options.all) {
      await this.revertAllChanges();
    } else if (options.file) {
      await this.revertSpecificFile(options.file);
    } else {
      await this.showRevertMenu();
    }
  }

  private async revertAllChanges(): Promise<void> {
    const { confirm } = await inquirer.prompt([
      {
        type: 'confirm',
        name: 'confirm',
        message: chalk.red('Are you sure you want to revert ALL changes in this session?'),
        default: false
      }
    ]);

    if (!confirm) {
      this.logger.info('Revert cancelled');
      return;
    }

    this.logger.loading('Reverting all changes...');
    const result = await this.changeTracker.revertAllChanges();
    
    if (result.success > 0) {
      this.logger.success(`✅ Successfully reverted ${result.success} files`);
    }
    
    if (result.failed > 0) {
      this.logger.error(`❌ Failed to revert ${result.failed} files`);
    }
  }

  private async revertSpecificFile(filename: string): Promise<void> {
    const success = await this.changeTracker.revertFile(filename);
    
    if (!success) {
      this.logger.error(`Failed to revert ${filename}`);
    }
  }

  private async showRevertMenu(): Promise<void> {
    const changes = this.changeTracker.getSessionChanges();
    
    const choices = [
      ...changes.map(change => ({
        name: `${change.filename} (${change.operation})`,
        value: change.filename
      })),
      { name: chalk.red('Revert ALL changes'), value: '__ALL__' },
      { name: 'Cancel', value: '__CANCEL__' }
    ];

    const { selectedFile } = await inquirer.prompt([
      {
        type: 'list',
        name: 'selectedFile',
        message: 'Which file would you like to revert?',
        choices
      }
    ]);

    if (selectedFile === '__CANCEL__') {
      this.logger.info('Revert cancelled');
      return;
    }

    if (selectedFile === '__ALL__') {
      await this.revertAllChanges();
      return;
    }

    await this.revertSpecificFile(selectedFile);
  }

  public async clearSession(): Promise<void> {
    const changes = this.changeTracker.getSessionChanges();
    
    if (changes.length === 0) {
      this.logger.info('No changes to clear');
      return;
    }

    const { confirm } = await inquirer.prompt([
      {
        type: 'confirm',
        name: 'confirm',
        message: 'Clear change tracking session? (This will not revert files, just clear the tracking)',
        default: false
      }
    ]);

    if (confirm) {
      this.changeTracker.clearSession();
      this.logger.success('Change tracking session cleared');
    } else {
      this.logger.info('Clear cancelled');
    }
  }
}
