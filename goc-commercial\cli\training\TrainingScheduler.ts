import { Logger } from '../utils/Logger';
import { ContinuousTrainingConfig } from './ContinuousTrainingDaemon';

export interface TrainingSchedule {
  name: string;
  type: 'quick' | 'deep' | 'comprehensive' | 'adaptive';
  interval: number; // milliseconds
  lastExecution: Date | null;
  nextExecution: Date;
  priority: number;
  aspects: string[];
  conditions?: {
    minMemoryExperiences?: number;
    maxSystemLoad?: number;
    requiredProviders?: string[];
  };
}

export interface AdaptiveSchedulingConfig {
  enabled: boolean;
  performanceThreshold: number;
  adaptationRate: number;
  minInterval: number;
  maxInterval: number;
}

export class TrainingScheduler {
  private logger: Logger;
  private schedules: Map<string, TrainingSchedule> = new Map();
  private adaptiveConfig: AdaptiveSchedulingConfig;
  private performanceHistory: Array<{ timestamp: Date; success: boolean; duration: number }> = [];

  constructor(logger: Logger) {
    this.logger = logger;
    this.adaptiveConfig = {
      enabled: true,
      performanceThreshold: 0.8,
      adaptationRate: 0.1,
      minInterval: 5 * 60 * 1000, // 5 minutes
      maxInterval: 24 * 60 * 60 * 1000 // 24 hours
    };
  }

  public async initialize(config: ContinuousTrainingConfig): Promise<void> {
    this.logger.debug('Initializing training scheduler...');
    
    this.createDefaultSchedules(config);
    this.optimizeSchedulesBasedOnIntensity(config.intensity);
    
    this.logger.debug(`✅ Initialized ${this.schedules.size} training schedules`);
  }

  private createDefaultSchedules(config: ContinuousTrainingConfig): void {
    // Quick coding and performance training
    this.schedules.set('quick-coding', {
      name: 'Quick Coding Training',
      type: 'quick',
      interval: config.scheduling.quickTraining * 60 * 1000,
      lastExecution: null,
      nextExecution: new Date(Date.now() + config.scheduling.quickTraining * 60 * 1000),
      priority: 3,
      aspects: ['coding', 'performance'],
      conditions: {
        minMemoryExperiences: 5,
        maxSystemLoad: 70
      }
    });

    // Deep learning sessions
    this.schedules.set('deep-learning', {
      name: 'Deep Learning Session',
      type: 'deep',
      interval: config.scheduling.deepTraining * 60 * 60 * 1000,
      lastExecution: null,
      nextExecution: new Date(Date.now() + config.scheduling.deepTraining * 60 * 60 * 1000),
      priority: 2,
      aspects: ['coding', 'webResearch', 'knowledgeExpansion'],
      conditions: {
        minMemoryExperiences: 10
      }
    });

    // Comprehensive training
    this.schedules.set('comprehensive', {
      name: 'Comprehensive Training',
      type: 'comprehensive',
      interval: config.scheduling.comprehensiveTraining * 24 * 60 * 60 * 1000,
      lastExecution: null,
      nextExecution: new Date(Date.now() + config.scheduling.comprehensiveTraining * 24 * 60 * 60 * 1000),
      priority: 1,
      aspects: ['coding', 'webResearch', 'performance', 'adaptiveBehavior', 'knowledgeExpansion', 'curiosityDriven']
    });

    // Adaptive behavior training
    this.schedules.set('adaptive-behavior', {
      name: 'Adaptive Behavior Training',
      type: 'adaptive',
      interval: 2 * 60 * 60 * 1000, // Every 2 hours
      lastExecution: null,
      nextExecution: new Date(Date.now() + 2 * 60 * 60 * 1000),
      priority: 4,
      aspects: ['adaptiveBehavior', 'performance'],
      conditions: {
        minMemoryExperiences: 3
      }
    });

    // Curiosity-driven research
    if (config.aspects.curiosityDriven && config.aspects.webResearch) {
      this.schedules.set('curiosity-research', {
        name: 'Curiosity-Driven Research',
        type: 'adaptive',
        interval: 6 * 60 * 60 * 1000, // Every 6 hours
        lastExecution: null,
        nextExecution: new Date(Date.now() + 6 * 60 * 60 * 1000),
        priority: 5,
        aspects: ['curiosityDriven', 'webResearch', 'knowledgeExpansion'],
        conditions: {
          requiredProviders: ['web']
        }
      });
    }
  }

  private optimizeSchedulesBasedOnIntensity(intensity: string): void {
    const intensityMultipliers = {
      minimal: 2.0,
      low: 1.5,
      medium: 1.0,
      high: 0.7,
      maximum: 0.5
    };

    const multiplier = intensityMultipliers[intensity as keyof typeof intensityMultipliers] || 1.0;

    for (const [name, schedule] of this.schedules) {
      const newInterval = Math.max(
        schedule.interval * multiplier,
        this.adaptiveConfig.minInterval
      );
      
      schedule.interval = newInterval;
      schedule.nextExecution = new Date(Date.now() + newInterval);
      
      this.logger.debug(`Adjusted ${name} interval to ${(newInterval / 60000).toFixed(1)} minutes`);
    }
  }

  public getNextScheduledTraining(): TrainingSchedule | null {
    const now = new Date();
    let nextSchedule: TrainingSchedule | null = null;
    let earliestTime = new Date(Date.now() + 365 * 24 * 60 * 60 * 1000); // 1 year from now

    for (const schedule of this.schedules.values()) {
      if (schedule.nextExecution <= now) {
        // This schedule is ready to run
        if (!nextSchedule || schedule.priority < nextSchedule.priority) {
          nextSchedule = schedule;
        }
      } else if (schedule.nextExecution < earliestTime) {
        earliestTime = schedule.nextExecution;
        if (!nextSchedule) {
          nextSchedule = schedule;
        }
      }
    }

    return nextSchedule;
  }

  public markScheduleExecuted(scheduleName: string, success: boolean, duration: number): void {
    const schedule = this.schedules.get(scheduleName);
    if (!schedule) return;

    const now = new Date();
    schedule.lastExecution = now;
    schedule.nextExecution = new Date(now.getTime() + schedule.interval);

    // Record performance for adaptive scheduling
    this.performanceHistory.push({
      timestamp: now,
      success,
      duration
    });

    // Keep only recent history (last 100 entries)
    if (this.performanceHistory.length > 100) {
      this.performanceHistory = this.performanceHistory.slice(-100);
    }

    // Adapt schedule if enabled
    if (this.adaptiveConfig.enabled) {
      this.adaptScheduleBasedOnPerformance(scheduleName, success, duration);
    }

    this.logger.debug(`Marked ${scheduleName} as executed (success: ${success}, duration: ${duration}ms)`);
  }

  private adaptScheduleBasedOnPerformance(scheduleName: string, success: boolean, duration: number): void {
    const schedule = this.schedules.get(scheduleName);
    if (!schedule) return;

    // Calculate recent success rate
    const recentPerformance = this.performanceHistory.slice(-10);
    const successRate = recentPerformance.filter(p => p.success).length / recentPerformance.length;

    // Adapt interval based on performance
    if (successRate > this.adaptiveConfig.performanceThreshold) {
      // Good performance - can train more frequently
      const newInterval = Math.max(
        schedule.interval * (1 - this.adaptiveConfig.adaptationRate),
        this.adaptiveConfig.minInterval
      );
      schedule.interval = newInterval;
      this.logger.debug(`Increased training frequency for ${scheduleName} due to good performance`);
    } else if (successRate < 0.5) {
      // Poor performance - reduce frequency
      const newInterval = Math.min(
        schedule.interval * (1 + this.adaptiveConfig.adaptationRate),
        this.adaptiveConfig.maxInterval
      );
      schedule.interval = newInterval;
      this.logger.debug(`Decreased training frequency for ${scheduleName} due to poor performance`);
    }

    // Update next execution time
    schedule.nextExecution = new Date(Date.now() + schedule.interval);
  }

  public getScheduleStatus(): any {
    const schedules = Array.from(this.schedules.values()).map(schedule => ({
      name: schedule.name,
      type: schedule.type,
      nextExecution: schedule.nextExecution,
      lastExecution: schedule.lastExecution,
      intervalMinutes: Math.round(schedule.interval / 60000),
      priority: schedule.priority,
      aspects: schedule.aspects
    }));

    const recentPerformance = this.performanceHistory.slice(-20);
    const successRate = recentPerformance.length > 0 
      ? recentPerformance.filter(p => p.success).length / recentPerformance.length 
      : 0;

    return {
      schedules,
      adaptiveConfig: this.adaptiveConfig,
      recentSuccessRate: successRate,
      totalExecutions: this.performanceHistory.length
    };
  }

  public updateAdaptiveConfig(config: Partial<AdaptiveSchedulingConfig>): void {
    this.adaptiveConfig = { ...this.adaptiveConfig, ...config };
    this.logger.info('🔧 Updated adaptive scheduling configuration');
  }

  public canExecuteSchedule(scheduleName: string, systemMetrics?: any): boolean {
    const schedule = this.schedules.get(scheduleName);
    if (!schedule || !schedule.conditions) return true;

    // Check conditions
    if (schedule.conditions.maxSystemLoad && systemMetrics?.cpuUsage > schedule.conditions.maxSystemLoad) {
      return false;
    }

    if (schedule.conditions.minMemoryExperiences && systemMetrics?.memoryExperiences < schedule.conditions.minMemoryExperiences) {
      return false;
    }

    return true;
  }
}
