<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\SubscriptionPlan;
use App\Models\UserSubscription;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create admin user
        $admin = User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'GOC Agent Admin',
                'email' => '<EMAIL>',
                'password' => Hash::make('admin123'),
                'role' => 'admin',
                'email_verified_at' => now(),
                'is_active' => true,
            ]
        );

        // Assign Enterprise plan to admin
        $enterprisePlan = SubscriptionPlan::where('slug', 'enterprise')->first();

        if ($enterprisePlan) {
            UserSubscription::updateOrCreate(
                ['user_id' => $admin->id],
                [
                    'user_id' => $admin->id,
                    'subscription_plan_id' => $enterprisePlan->id,
                    'status' => 'active',
                    'billing_cycle' => 'yearly',
                    'current_period_start' => now(),
                    'current_period_end' => now()->addYear(),
                    'usage_reset_date' => now()->toDateString(),
                ]
            );
        }

        $this->command->info('Admin user created: <EMAIL> / admin123');
    }
}
