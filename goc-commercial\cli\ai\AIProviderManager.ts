import { Config<PERSON>anager, AIProviderConfig } from '../config/ConfigManager';
import { Logger } from '../utils/Logger';
import { OllamaProvider } from './providers/OllamaProvider';
import { OpenAIProvider } from './providers/OpenAIProvider';
import { GroqProvider } from './providers/GroqProvider';
import { GeminiProvider } from './providers/GeminiProvider';

export interface ChatMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

export interface ChatResponse {
  content: string;
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
}

export interface AIProvider {
  name: string;
  isAvailable(): Promise<boolean>;
  chat(messages: ChatMessage[], model?: string): Promise<ChatResponse>;
  listModels(): Promise<string[]>;
}

export class AIProviderManager {
  private providers: Map<string, AIProvider> = new Map();
  private config: ConfigManager;
  private logger: Logger;
  private currentProvider?: string;
  private currentModel?: string;

  constructor(config: ConfigManager, logger: Logger) {
    this.config = config;
    this.logger = logger;
    this.initializeProviders();
  }

  public getCurrentProvider(): string | undefined {
    return this.currentProvider;
  }

  public getCurrentModel(): string | undefined {
    return this.currentModel;
  }

  public getSessionInfo(): { provider?: string; model?: string } {
    return {
      provider: this.currentProvider,
      model: this.currentModel
    };
  }

  private initializeProviders(): void {
    const config = this.config.getConfig();

    // Initialize Ollama
    if (config.providers.ollama.enabled) {
      this.providers.set('ollama', new OllamaProvider(config.providers.ollama, this.logger));
    }

    // Initialize OpenAI
    if (config.providers.openai.enabled && config.providers.openai.apiKey) {
      this.providers.set('openai', new OpenAIProvider(config.providers.openai, this.logger));
    }

    // Initialize Groq
    if (config.providers.groq.enabled && config.providers.groq.apiKey) {
      this.providers.set('groq', new GroqProvider(config.providers.groq, this.logger));
    }

    // Initialize Gemini
    if (config.providers.gemini.enabled && config.providers.gemini.apiKey) {
      this.providers.set('gemini', new GeminiProvider(config.providers.gemini, this.logger));
    }
  }

  public async getAvailableProviders(): Promise<string[]> {
    const available: string[] = [];
    
    for (const [name, provider] of this.providers) {
      try {
        if (await provider.isAvailable()) {
          available.push(name);
        }
      } catch (error) {
        this.logger.debug(`Provider ${name} is not available: ${error}`);
      }
    }

    return available;
  }

  public getProvider(name: string): AIProvider | undefined {
    return this.providers.get(name);
  }

  public async getDefaultProvider(): Promise<AIProvider | undefined> {
    const config = this.config.getConfig();
    const defaultProvider = this.providers.get(config.defaultProvider);
    
    if (defaultProvider && await defaultProvider.isAvailable()) {
      return defaultProvider;
    }

    // Fallback to first available provider
    const availableProviders = await this.getAvailableProviders();
    if (availableProviders.length > 0) {
      return this.providers.get(availableProviders[0]);
    }

    return undefined;
  }

  public async chat(
    messages: ChatMessage[],
    providerName?: string,
    model?: string
  ): Promise<ChatResponse> {
    let provider: AIProvider | undefined;
    let attemptedProviders: string[] = [];
    let actualModel: string | undefined;

    if (providerName) {
      provider = this.getProvider(providerName);
      if (!provider) {
        throw new Error(`Provider '${providerName}' not found or not configured. Available providers: ${Array.from(this.providers.keys()).join(', ')}`);
      }
      attemptedProviders.push(providerName);
    } else {
      provider = await this.getDefaultProvider();
      if (!provider) {
        throw new Error('No AI providers available. Please configure at least one provider using: goc config');
      }
      attemptedProviders.push(provider.name);
    }

    // Determine the actual model being used
    if (model) {
      actualModel = model;
    } else {
      const config = this.config.getConfig();
      const providerConfig = config.providers[provider.name as keyof typeof config.providers];
      actualModel = providerConfig?.defaultModel;
    }

    // Update session tracking
    this.currentProvider = provider.name;
    this.currentModel = actualModel;

    // Try the primary provider
    try {
      if (!await provider.isAvailable()) {
        throw new Error(`Provider '${provider.name}' is not available`);
      }
      return await provider.chat(messages, model);
    } catch (error) {
      this.logger.warn(`Primary provider '${provider.name}' failed: ${error}`);

      // Try fallback providers if primary fails
      if (!providerName) { // Only use fallbacks if no specific provider was requested
        const availableProviders = await this.getAvailableProviders();
        const fallbackProviders = availableProviders.filter(p => !attemptedProviders.includes(p));

        for (const fallbackName of fallbackProviders) {
          try {
            const fallbackProvider = this.getProvider(fallbackName);
            if (fallbackProvider && await fallbackProvider.isAvailable()) {
              this.logger.info(`Trying fallback provider: ${fallbackName}`);
              const response = await fallbackProvider.chat(messages, model);
              this.logger.success(`Successfully used fallback provider: ${fallbackName}`);
              return response;
            }
          } catch (fallbackError) {
            this.logger.debug(`Fallback provider '${fallbackName}' also failed: ${fallbackError}`);
            attemptedProviders.push(fallbackName);
          }
        }
      }

      // If all providers failed, throw the original error with helpful context
      throw new Error(`All providers failed. Last error from '${provider.name}': ${error}. Attempted providers: ${attemptedProviders.join(', ')}`);
    }
  }

  public async listModels(providerName?: string): Promise<{ provider: string; models: string[] }[]> {
    const results: { provider: string; models: string[] }[] = [];

    if (providerName) {
      const provider = this.getProvider(providerName);
      if (provider && await provider.isAvailable()) {
        const models = await provider.listModels();
        results.push({ provider: providerName, models });
      }
    } else {
      for (const [name, provider] of this.providers) {
        try {
          if (await provider.isAvailable()) {
            const models = await provider.listModels();
            results.push({ provider: name, models });
          }
        } catch (error) {
          this.logger.debug(`Failed to list models for ${name}: ${error}`);
        }
      }
    }

    return results;
  }

  public refreshProviders(): void {
    this.providers.clear();
    this.initializeProviders();
  }
}
