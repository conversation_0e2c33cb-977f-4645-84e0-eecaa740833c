<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Production Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains configuration settings specifically for production
    | deployment of the GOC Agent platform.
    |
    */

    'cache' => [
        'default' => 'redis',
        'stores' => [
            'redis' => [
                'driver' => 'redis',
                'connection' => 'cache',
                'lock_connection' => 'default',
            ],
        ],
        'prefix' => 'goc_agent_cache',
    ],

    'session' => [
        'driver' => 'redis',
        'connection' => 'session',
        'lifetime' => 120,
        'expire_on_close' => false,
        'encrypt' => true,
        'files' => storage_path('framework/sessions'),
        'store' => null,
        'lottery' => [2, 100],
        'cookie' => 'goc_agent_session',
        'path' => '/',
        'domain' => null,
        'secure' => true,
        'http_only' => true,
        'same_site' => 'lax',
    ],

    'queue' => [
        'default' => 'redis',
        'connections' => [
            'redis' => [
                'driver' => 'redis',
                'connection' => 'default',
                'queue' => 'default',
                'retry_after' => 90,
                'block_for' => null,
                'after_commit' => false,
            ],
        ],
        'batching' => [
            'database' => 'mysql',
            'table' => 'job_batches',
        ],
        'failed' => [
            'driver' => 'database-uuids',
            'database' => 'mysql',
            'table' => 'failed_jobs',
        ],
    ],

    'logging' => [
        'default' => 'stack',
        'deprecations' => 'null',
        'channels' => [
            'stack' => [
                'driver' => 'stack',
                'channels' => ['single', 'slack'],
                'ignore_exceptions' => false,
            ],
            'single' => [
                'driver' => 'single',
                'path' => storage_path('logs/laravel.log'),
                'level' => 'error',
                'replace_placeholders' => true,
            ],
            'slack' => [
                'driver' => 'slack',
                'url' => env('LOG_SLACK_WEBHOOK_URL'),
                'username' => 'GOC Agent',
                'emoji' => ':boom:',
                'level' => 'critical',
            ],
        ],
    ],

    'security' => [
        'headers' => [
            'X-Frame-Options' => 'DENY',
            'X-Content-Type-Options' => 'nosniff',
            'X-XSS-Protection' => '1; mode=block',
            'Referrer-Policy' => 'strict-origin-when-cross-origin',
            'Content-Security-Policy' => "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' https:; connect-src 'self' https:;",
        ],
        'rate_limiting' => [
            'api' => [
                'max_attempts' => 1000,
                'decay_minutes' => 1,
            ],
            'auth' => [
                'max_attempts' => 5,
                'decay_minutes' => 15,
            ],
        ],
    ],

    'optimization' => [
        'opcache' => [
            'enable' => true,
            'memory_consumption' => 256,
            'max_accelerated_files' => 20000,
            'validate_timestamps' => false,
        ],
        'compression' => [
            'gzip' => true,
            'level' => 6,
        ],
        'cdn' => [
            'enabled' => env('CDN_ENABLED', false),
            'url' => env('CDN_URL'),
        ],
    ],

    'monitoring' => [
        'health_checks' => [
            'database' => true,
            'redis' => true,
            'storage' => true,
            'queue' => true,
        ],
        'metrics' => [
            'enabled' => true,
            'retention_days' => 30,
        ],
        'alerts' => [
            'error_threshold' => 10,
            'response_time_threshold' => 2000, // milliseconds
            'disk_usage_threshold' => 85, // percentage
        ],
    ],

    'backup' => [
        'enabled' => env('BACKUP_ENABLED', true),
        'schedule' => '0 2 * * *', // Daily at 2 AM
        'retention_days' => 30,
        'destinations' => [
            's3' => [
                'driver' => 's3',
                'bucket' => env('BACKUP_S3_BUCKET'),
                'region' => env('BACKUP_S3_REGION'),
            ],
        ],
    ],
];
