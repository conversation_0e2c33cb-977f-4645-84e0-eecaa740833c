// Context Engine Management Commands

import chalk from 'chalk';
import inquirer from 'inquirer';
import { Logger } from '../utils/Logger';
import { ConfigManager } from '../config/ConfigManager';
import { ContextEngineFactory, ProviderTestResult } from '../context/ContextEngineFactory';
import { HybridContextBuilder } from '../context/HybridContextBuilder';
import { CodebaseAnalyzer } from '../codebase/CodebaseAnalyzer';

export class ContextEngineCommand {
  private logger: Logger;
  private config: ConfigManager;
  private factory: ContextEngineFactory;
  private codebaseAnalyzer: CodebaseAnalyzer;

  constructor(logger: Logger, config: ConfigManager, codebaseAnalyzer: CodebaseAnalyzer) {
    this.logger = logger;
    this.config = config;
    this.factory = new ContextEngineFactory(logger, codebaseAnalyzer);
    this.codebaseAnalyzer = codebaseAnalyzer;
  }

  public async handleContextCommand(action?: string): Promise<void> {
    if (!action) {
      await this.showContextMenu();
      return;
    }

    switch (action.toLowerCase()) {
      case 'status':
        await this.showStatus();
        break;
      case 'test':
        await this.testProviders();
        break;
      case 'configure':
        await this.configureEngine();
        break;
      case 'benchmark':
        await this.benchmarkEngines();
        break;
      default:
        this.logger.error(`Unknown context command: ${action}`);
        await this.showContextMenu();
    }
  }

  private async showContextMenu(): Promise<void> {
    const { action } = await inquirer.prompt([
      {
        type: 'list',
        name: 'action',
        message: 'Context Engine Management:',
        choices: [
          { name: '📊 Show current status', value: 'status' },
          { name: '🧪 Test providers', value: 'test' },
          { name: '⚙️ Configure engine', value: 'configure' },
          { name: '🏃 Benchmark engines', value: 'benchmark' },
          { name: '❌ Back to main menu', value: 'back' }
        ]
      }
    ]);

    if (action !== 'back') {
      await this.handleContextCommand(action);
    }
  }

  private async showStatus(): Promise<void> {
    const currentConfig = this.config.getConfig().contextEngine;
    
    console.log(chalk.blue('\n🧠 Context Engine Status\n'));
    console.log(`Provider: ${chalk.green(currentConfig.provider)}`);
    console.log(`Enabled: ${currentConfig.enabled ? chalk.green('Yes') : chalk.red('No')}`);
    console.log(`Fallback to Basic: ${currentConfig.fallbackToBasic ? chalk.green('Yes') : chalk.red('No')}`);
    console.log(`Cache Enabled: ${currentConfig.cacheEnabled ? chalk.green('Yes') : chalk.red('No')}`);
    console.log(`Cache TTL: ${chalk.yellow(currentConfig.cacheTTL + 's')}`);
    console.log(`Max Semantic Depth: ${chalk.yellow(currentConfig.maxSemanticDepth)}`);
    console.log(`Max Context Nodes: ${chalk.yellow(currentConfig.maxContextNodes)}`);

    if (currentConfig.apiConfig?.baseUrl) {
      console.log(`API URL: ${chalk.cyan(currentConfig.apiConfig.baseUrl)}`);
    }

    // Test current provider
    if (currentConfig.enabled) {
      console.log(chalk.blue('\n🧪 Testing current provider...\n'));
      try {
        const testResult = await this.factory.testProvider(currentConfig.provider, currentConfig);
        this.displayTestResult(testResult);
      } catch (error) {
        console.log(chalk.red(`❌ Test failed: ${error}`));
      }
    }
  }

  private async testProviders(): Promise<void> {
    console.log(chalk.blue('\n🧪 Testing Context Engine Providers\n'));
    
    const providers = this.factory.getSupportedProviders();
    const currentConfig = this.config.getConfig().contextEngine;
    
    for (const provider of providers) {
      console.log(chalk.yellow(`Testing ${provider}...`));
      
      try {
        const testConfig = { ...currentConfig, provider: provider as any };
        const result = await this.factory.testProvider(provider, testConfig);
        this.displayTestResult(result);
      } catch (error) {
        console.log(chalk.red(`❌ ${provider}: ${error}\n`));
      }
    }
  }

  private displayTestResult(result: ProviderTestResult): void {
    if (result.success) {
      console.log(chalk.green(`✅ ${result.provider}: Available`));
      console.log(`   Test time: ${result.testTime}ms`);
      
      if (result.capabilities) {
        console.log(`   Capabilities:`);
        console.log(`     Semantic Search: ${result.capabilities.semanticSearch ? '✅' : '❌'}`);
        console.log(`     Cross Language: ${result.capabilities.crossLanguage ? '✅' : '❌'}`);
        console.log(`     Real-time Indexing: ${result.capabilities.realTimeIndexing ? '✅' : '❌'}`);
        console.log(`     Architectural Patterns: ${result.capabilities.architecturalPatterns ? '✅' : '❌'}`);
        console.log(`     Performance: ${chalk.cyan(result.capabilities.performanceLevel)}`);
        console.log(`     Requires API: ${result.capabilities.requiresApi ? '🌐' : '💻'}`);
      }
    } else {
      console.log(chalk.red(`❌ ${result.provider}: ${result.error}`));
    }
    console.log('');
  }

  private async configureEngine(): Promise<void> {
    const currentConfig = this.config.getConfig().contextEngine;
    
    const answers = await inquirer.prompt([
      {
        type: 'list',
        name: 'provider',
        message: 'Select context engine provider:',
        choices: [
          { name: '🚀 Augment (Best quality, requires API)', value: 'augment' },
          { name: '🧠 Intelligent (Advanced indexing, local)', value: 'intelligent' },
          { name: '🌳 Tree-sitter (Good quality, local)', value: 'tree-sitter' },
          { name: '🤖 CodeBERT (ML-powered, local)', value: 'codebert' },
          { name: '⚡ Basic (Fast, simple)', value: 'basic' }
        ],
        default: currentConfig.provider
      },
      {
        type: 'confirm',
        name: 'enabled',
        message: 'Enable semantic context engine?',
        default: currentConfig.enabled
      },
      {
        type: 'confirm',
        name: 'fallbackToBasic',
        message: 'Fallback to basic engine if semantic fails?',
        default: currentConfig.fallbackToBasic
      },
      {
        type: 'confirm',
        name: 'cacheEnabled',
        message: 'Enable context caching?',
        default: currentConfig.cacheEnabled
      },
      {
        type: 'number',
        name: 'maxSemanticDepth',
        message: 'Maximum semantic analysis depth (1-5):',
        default: currentConfig.maxSemanticDepth,
        validate: (input) => input >= 1 && input <= 5
      }
    ]);

    // Provider-specific configuration
    if (answers.provider === 'augment') {
      const augmentConfig = await inquirer.prompt([
        {
          type: 'input',
          name: 'baseUrl',
          message: 'Augment API Base URL:',
          default: currentConfig.apiConfig?.baseUrl || 'https://api.augmentcode.com/v1'
        },
        {
          type: 'password',
          name: 'apiKey',
          message: 'Augment API Key:',
          default: currentConfig.apiConfig?.apiKey || ''
        }
      ]);

      answers.apiConfig = {
        baseUrl: augmentConfig.baseUrl,
        apiKey: augmentConfig.apiKey,
        timeout: 30000
      };
    }

    // Update configuration
    const updatedConfig = {
      ...this.config.getConfig(),
      contextEngine: {
        ...currentConfig,
        ...answers
      }
    };

    this.config.updateConfig(updatedConfig);
    this.logger.success('✅ Context engine configuration updated!');

    // Test the new configuration
    const testNew = await inquirer.prompt([
      {
        type: 'confirm',
        name: 'test',
        message: 'Test the new configuration?',
        default: true
      }
    ]);

    if (testNew.test) {
      console.log(chalk.blue('\n🧪 Testing new configuration...\n'));
      const result = await this.factory.testProvider(answers.provider, updatedConfig.contextEngine);
      this.displayTestResult(result);
    }
  }

  private async benchmarkEngines(): Promise<void> {
    console.log(chalk.blue('\n🏃 Benchmarking Context Engines\n'));
    
    const hybridBuilder = new HybridContextBuilder(
      this.codebaseAnalyzer,
      this.logger,
      {
        useSemanticEngine: true,
        fallbackToBasic: true,
        semanticThreshold: 0.5,
        maxSemanticProcessingTime: 30000,
        cacheEnabled: false // Disable cache for fair benchmark
      }
    );

    const testQueries = [
      'simple file query',
      'complex architectural analysis of the entire codebase structure',
      'find all functions related to user authentication',
      'analyze dependency relationships'
    ];

    console.log('Running benchmark with different query complexities...\n');

    for (const query of testQueries) {
      console.log(chalk.yellow(`Query: "${query}"`));
      
      // Test basic context
      const basicStart = Date.now();
      await hybridBuilder.buildContext();
      const basicTime = Date.now() - basicStart;
      
      // Test enhanced context
      const enhancedStart = Date.now();
      try {
        await hybridBuilder.buildEnhancedContext(query, 'benchmark');
        const enhancedTime = Date.now() - enhancedStart;
        
        console.log(`  Basic: ${chalk.green(basicTime + 'ms')}`);
        console.log(`  Enhanced: ${chalk.cyan(enhancedTime + 'ms')}`);
        console.log(`  Overhead: ${chalk.yellow((enhancedTime - basicTime) + 'ms')}\n`);
      } catch (error) {
        console.log(`  Basic: ${chalk.green(basicTime + 'ms')}`);
        console.log(`  Enhanced: ${chalk.red('Failed - ' + error)}\n`);
      }
    }

    // Show stats
    const stats = await hybridBuilder.getSemanticStats();
    console.log(chalk.blue('📊 Engine Statistics:'));
    console.log(JSON.stringify(stats, null, 2));
  }
}
