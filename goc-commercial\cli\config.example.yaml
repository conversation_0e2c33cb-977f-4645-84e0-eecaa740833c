# GOC Agent Configuration Example
# Copy this file to ~/.goc-agent/config.yaml and customize

defaultProvider: ollama

providers:
  # Ollama - Local AI (no API key needed)
  # Install from: https://ollama.ai
  # Pull models with: ollama pull llama3.2
  ollama:
    name: Ollama
    baseUrl: http://localhost:11434
    defaultModel: llama3.2
    enabled: true

  # OpenAI - Requires API key
  # Get key from: https://platform.openai.com
  openai:
    name: OpenAI
    baseUrl: https://api.openai.com/v1
    apiKey: ${OPENAI_API_KEY}
    defaultModel: gpt-4
    enabled: false

  # Groq - Fast inference, requires API key
  # Get key from: https://console.groq.com
  groq:
    name: Groq
    baseUrl: https://api.groq.com/openai/v1
    apiKey: ${GROQ_API_KEY}
    defaultModel: llama-3.1-70b-versatile
    enabled: false

  # Gemini - Google's AI, requires API key
  # Get key from: https://aistudio.google.com
  gemini:
    name: Gemini
    baseUrl: https://generativelanguage.googleapis.com/v1beta
    apiKey: ${GEMINI_API_KEY}
    defaultModel: gemini-pro
    enabled: false

# Codebase scanning settings
codebase:
  # Files to exclude from scanning
  excludePatterns:
    - node_modules/**
    - .git/**
    - dist/**
    - build/**
    - "*.log"
    - .env*
    - "*.min.js"
    - "*.map"

  # File types to include
  includePatterns:
    - "**/*.ts"
    - "**/*.js"
    - "**/*.tsx"
    - "**/*.jsx"
    - "**/*.py"
    - "**/*.java"
    - "**/*.cpp"
    - "**/*.c"
    - "**/*.h"
    - "**/*.cs"
    - "**/*.php"
    - "**/*.rb"
    - "**/*.go"
    - "**/*.rs"
    - "**/*.md"
    - "**/*.json"
    - "**/*.yaml"
    - "**/*.yml"

  # Maximum file size to process (in bytes)
  maxFileSize: 1048576  # 1MB

  # Maximum number of files to scan
  maxFiles: 1000

# UI settings
ui:
  theme: dark
  verbose: false
