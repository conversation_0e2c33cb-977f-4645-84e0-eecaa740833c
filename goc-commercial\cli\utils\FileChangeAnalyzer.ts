import fs from 'fs';
import { createTwoFilesPatch } from 'diff';

export interface FileChange {
  filename: string;
  operation: 'created' | 'edited' | 'deleted';
  linesAdded: number;
  linesDeleted: number;
  timestamp: Date;
  backupPath?: string;
  originalContent?: string;
  newContent?: string;
}

export interface LineDifference {
  linesAdded: number;
  linesDeleted: number;
}

export class FileChangeAnalyzer {
  
  public static analyzeFileChange(
    filePath: string,
    originalContent: string | null,
    newContent: string | null,
    operation: 'created' | 'edited' | 'deleted'
  ): FileChange {
    const timestamp = new Date();
    
    if (operation === 'created') {
      return {
        filename: filePath,
        operation: 'created',
        linesAdded: newContent ? newContent.split('\n').length : 0,
        linesDeleted: 0,
        timestamp,
        newContent: newContent || undefined
      };
    }
    
    if (operation === 'deleted') {
      return {
        filename: filePath,
        operation: 'deleted',
        linesAdded: 0,
        linesDeleted: originalContent ? originalContent.split('\n').length : 0,
        timestamp,
        originalContent: originalContent || undefined
      };
    }
    
    // For edited files, calculate line differences
    if (originalContent && newContent) {
      const { linesAdded, linesDeleted } = this.calculateLineDifferences(originalContent, newContent);
      
      return {
        filename: filePath,
        operation: 'edited',
        linesAdded,
        linesDeleted,
        timestamp,
        originalContent,
        newContent
      };
    }
    
    // Fallback for edge cases
    return {
      filename: filePath,
      operation,
      linesAdded: 0,
      linesDeleted: 0,
      timestamp
    };
  }
  
  private static calculateLineDifferences(oldContent: string, newContent: string): LineDifference {
    const patch = createTwoFilesPatch(
      'old',
      'new',
      oldContent,
      newContent,
      'original',
      'modified'
    );
    
    let linesAdded = 0;
    let linesDeleted = 0;
    
    const lines = patch.split('\n');
    
    for (const line of lines) {
      if (line.startsWith('+') && !line.startsWith('+++')) {
        linesAdded++;
      } else if (line.startsWith('-') && !line.startsWith('---')) {
        linesDeleted++;
      }
    }
    
    return { linesAdded, linesDeleted };
  }
  
  public static createBackup(filePath: string, content: string): string {
    const timestamp = Date.now();
    const backupPath = `${filePath}.backup.${timestamp}`;
    
    try {
      fs.writeFileSync(backupPath, content);
      return backupPath;
    } catch (error) {
      throw new Error(`Failed to create backup: ${error instanceof Error ? error.message : String(error)}`);
    }
  }
  
  public static restoreFromBackup(filePath: string, backupPath: string): void {
    try {
      if (!fs.existsSync(backupPath)) {
        throw new Error(`Backup file not found: ${backupPath}`);
      }
      
      const backupContent = fs.readFileSync(backupPath, 'utf8');
      fs.writeFileSync(filePath, backupContent);
      
      // Clean up backup file
      fs.unlinkSync(backupPath);
    } catch (error) {
      throw new Error(`Failed to restore from backup: ${error instanceof Error ? error.message : String(error)}`);
    }
  }
  
  public static formatChangeForDisplay(change: FileChange): string {
    const icon = change.operation === 'created' ? '📝' : change.operation === 'edited' ? '✏️' : '🗑️';
    const addedText = change.linesAdded > 0 ? `+${change.linesAdded}` : '';
    const deletedText = change.linesDeleted > 0 ? `-${change.linesDeleted}` : '';
    const changesText = [addedText, deletedText].filter(Boolean).join(' ');
    
    return `${icon} ${change.filename} ${changesText ? `(${changesText})` : ''} - ${change.timestamp.toLocaleTimeString()}`;
  }
}
