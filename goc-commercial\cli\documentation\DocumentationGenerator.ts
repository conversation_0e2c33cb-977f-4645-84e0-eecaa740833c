import { Logger } from '../utils/Logger';
import { CodebaseAnalyzer } from '../codebase/CodebaseAnalyzer';
import { AIProviderManager } from '../ai/AIProviderManager';
import fs from 'fs';
import path from 'path';

export interface DocumentationConfig {
  outputDir: string;
  format: 'markdown' | 'html' | 'json';
  includePrivate: boolean;
  includeTests: boolean;
  generateIndex: boolean;
  autoUpdate: boolean;
  templates?: {
    readme?: string;
    api?: string;
    changelog?: string;
  };
}

export interface DocumentationSection {
  title: string;
  content: string;
  subsections?: DocumentationSection[];
  metadata?: Record<string, any>;
}

export interface GeneratedDocumentation {
  readme: string;
  api: string;
  changelog: string;
  architecture: string;
  index: string;
  files: Map<string, string>;
}

export class DocumentationGenerator {
  private logger: Logger;
  private codebaseAnalyzer: CodebaseAnalyzer;
  private aiManager: AIProviderManager;
  private config: DocumentationConfig;

  constructor(
    logger: Logger,
    codebaseAnalyzer: CodebaseAnalyzer,
    aiManager: AIProviderManager,
    config: DocumentationConfig
  ) {
    this.logger = logger;
    this.codebaseAnalyzer = codebaseAnalyzer;
    this.aiManager = aiManager;
    this.config = config;
  }

  public async generateDocumentation(): Promise<GeneratedDocumentation> {
    this.logger.info('📚 Generating comprehensive documentation...');

    const context = await this.codebaseAnalyzer.scanCodebase();
    const documentation: GeneratedDocumentation = {
      readme: '',
      api: '',
      changelog: '',
      architecture: '',
      index: '',
      files: new Map()
    };

    try {
      // Generate different types of documentation
      documentation.readme = await this.generateReadme(context);
      documentation.api = await this.generateApiDocumentation(context);
      documentation.architecture = await this.generateArchitectureDoc(context);
      documentation.changelog = await this.generateChangelog();
      documentation.index = await this.generateIndex(documentation);

      // Generate file-specific documentation
      await this.generateFileDocumentation(context, documentation);

      // Save documentation
      await this.saveDocumentation(documentation);

      this.logger.success('📚 Documentation generation completed');
      return documentation;
    } catch (error) {
      this.logger.error(`Documentation generation failed: ${error}`);
      throw error;
    }
  }

  private async generateReadme(context: any): Promise<string> {
    const prompt = `Generate a comprehensive README.md for this codebase:

Project Structure:
${context.structure}

Languages: ${context.languages.join(', ')}
Total Files: ${context.totalFiles}

Please include:
1. Project title and description
2. Features and capabilities
3. Installation instructions
4. Usage examples
5. Configuration guide
6. Contributing guidelines
7. License information

Make it professional and user-friendly.`;

    try {
      const response = await this.aiManager.chat([
        { role: 'user', content: prompt }
      ]);

      return this.formatMarkdown(response.content);
    } catch (error) {
      return this.generateFallbackReadme(context);
    }
  }

  private async generateApiDocumentation(context: any): Promise<string> {
    const prompt = `Generate API documentation for this codebase:

Project Structure:
${context.structure}

Please analyze the code and create documentation that includes:
1. API endpoints (if any)
2. Class and method documentation
3. Function signatures and parameters
4. Return types and examples
5. Error handling
6. Authentication requirements (if applicable)

Format as clear, structured markdown.`;

    try {
      const response = await this.aiManager.chat([
        { role: 'user', content: prompt }
      ]);

      return this.formatMarkdown(response.content);
    } catch (error) {
      return this.generateFallbackApiDoc(context);
    }
  }

  private async generateArchitectureDoc(context: any): Promise<string> {
    const prompt = `Analyze the architecture of this codebase and create documentation:

Project Structure:
${context.structure}

Languages: ${context.languages.join(', ')}

Please provide:
1. High-level architecture overview
2. Design patterns used
3. Module dependencies
4. Data flow
5. Key components and their responsibilities
6. Technology stack
7. Architectural decisions and rationale

Format as detailed markdown with diagrams if possible.`;

    try {
      const response = await this.aiManager.chat([
        { role: 'user', content: prompt }
      ]);

      return this.formatMarkdown(response.content);
    } catch (error) {
      return this.generateFallbackArchitectureDoc(context);
    }
  }

  private async generateChangelog(): Promise<string> {
    // This would integrate with git history in a real implementation
    const changelog = `# Changelog

All notable changes to this project will be documented in this file.

## [Unreleased]

### Added
- Comprehensive documentation generation
- Enhanced error recovery system
- Advanced model selection features
- Configuration validation and migration

### Changed
- Improved CLI experience
- Enhanced performance monitoring

### Fixed
- Agent identity issues
- JSON parsing improvements

## [1.0.0] - ${new Date().toISOString().split('T')[0]}

### Added
- Initial release
- Core functionality
- Basic documentation
`;

    return changelog;
  }

  private async generateIndex(documentation: GeneratedDocumentation): Promise<string> {
    const index = `# Documentation Index

## Core Documentation

- [README](./README.md) - Project overview and getting started
- [API Documentation](./API.md) - Detailed API reference
- [Architecture](./ARCHITECTURE.md) - System design and architecture
- [Changelog](./CHANGELOG.md) - Version history and changes

## File Documentation

${Array.from(documentation.files.keys())
  .map(file => `- [${file}](./files/${file.replace(/\//g, '_')}.md)`)
  .join('\n')}

## Quick Links

- [Installation Guide](./README.md#installation)
- [Configuration](./README.md#configuration)
- [Contributing](./README.md#contributing)
- [API Reference](./API.md)

---

*Documentation generated automatically by GOC Agent*
`;

    return index;
  }

  private async generateFileDocumentation(context: any, documentation: GeneratedDocumentation): Promise<void> {
    // Generate documentation for key files
    const importantFiles = context.files?.filter((file: any) => 
      file.relativePath.includes('src/') && 
      (file.relativePath.endsWith('.ts') || file.relativePath.endsWith('.js'))
    ).slice(0, 10); // Limit to 10 most important files

    for (const file of importantFiles || []) {
      try {
        const content = await this.codebaseAnalyzer.getFileContent(file.relativePath);
        const fileDoc = await this.generateSingleFileDoc(file.relativePath, content);
        documentation.files.set(file.relativePath, fileDoc);
      } catch (error) {
        this.logger.debug(`Failed to generate docs for ${file.relativePath}: ${error}`);
      }
    }
  }

  private async generateSingleFileDoc(filePath: string, content: string): Promise<string> {
    const prompt = `Generate documentation for this file:

File: ${filePath}
Content:
${content.substring(0, 2000)}...

Please provide:
1. File purpose and overview
2. Key classes/functions/exports
3. Dependencies
4. Usage examples
5. Important notes

Keep it concise but informative.`;

    try {
      const response = await this.aiManager.chat([
        { role: 'user', content: prompt }
      ]);

      return `# ${filePath}

${response.content}

---
*Generated automatically by GOC Agent*
`;
    } catch (error) {
      return this.generateFallbackFileDoc(filePath, content);
    }
  }

  private async saveDocumentation(documentation: GeneratedDocumentation): Promise<void> {
    const outputDir = this.config.outputDir;
    
    // Ensure output directory exists
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    // Save main documentation files
    fs.writeFileSync(path.join(outputDir, 'README.md'), documentation.readme);
    fs.writeFileSync(path.join(outputDir, 'API.md'), documentation.api);
    fs.writeFileSync(path.join(outputDir, 'ARCHITECTURE.md'), documentation.architecture);
    fs.writeFileSync(path.join(outputDir, 'CHANGELOG.md'), documentation.changelog);
    fs.writeFileSync(path.join(outputDir, 'INDEX.md'), documentation.index);

    // Save file documentation
    const filesDir = path.join(outputDir, 'files');
    if (!fs.existsSync(filesDir)) {
      fs.mkdirSync(filesDir, { recursive: true });
    }

    for (const [filePath, content] of documentation.files) {
      const fileName = filePath.replace(/\//g, '_') + '.md';
      fs.writeFileSync(path.join(filesDir, fileName), content);
    }

    this.logger.success(`📚 Documentation saved to: ${outputDir}`);
  }

  private formatMarkdown(content: string): string {
    // Clean up and format markdown content
    return content
      .replace(/```(\w+)?\n/g, '```$1\n')
      .replace(/\n{3,}/g, '\n\n')
      .trim();
  }

  private generateFallbackReadme(context: any): string {
    return `# ${path.basename(process.cwd())}

## Overview

This project contains ${context.totalFiles} files across ${context.languages.length} programming languages.

## Languages Used

${context.languages.map((lang: string) => `- ${lang}`).join('\n')}

## Project Structure

\`\`\`
${context.structure}
\`\`\`

## Getting Started

1. Clone the repository
2. Install dependencies
3. Configure the application
4. Run the application

## Documentation

This documentation was generated automatically. For more detailed information, please refer to the source code and comments.

---
*Generated by GOC Agent Documentation Generator*
`;
  }

  private generateFallbackApiDoc(context: any): string {
    return `# API Documentation

## Overview

This document provides API documentation for the project.

## Project Information

- Total Files: ${context.totalFiles}
- Languages: ${context.languages.join(', ')}

## API Reference

*API documentation is being generated. Please refer to the source code for detailed API information.*

---
*Generated by GOC Agent Documentation Generator*
`;
  }

  private generateFallbackArchitectureDoc(context: any): string {
    return `# Architecture Documentation

## System Overview

This document describes the architecture of the project.

## Project Statistics

- Total Files: ${context.totalFiles}
- Languages: ${context.languages.join(', ')}

## Architecture

\`\`\`
${context.structure}
\`\`\`

## Components

*Architecture analysis is being generated. Please refer to the source code for detailed architectural information.*

---
*Generated by GOC Agent Documentation Generator*
`;
  }

  private generateFallbackFileDoc(filePath: string, content: string): string {
    const lines = content.split('\n');
    const imports = lines.filter(line => line.includes('import') || line.includes('require')).slice(0, 5);
    const exports = lines.filter(line => line.includes('export') || line.includes('module.exports')).slice(0, 5);

    return `# ${filePath}

## File Overview

This file is part of the project structure.

## Imports
${imports.length > 0 ? imports.map(imp => `- \`${imp.trim()}\``).join('\n') : '*No imports detected*'}

## Exports
${exports.length > 0 ? exports.map(exp => `- \`${exp.trim()}\``).join('\n') : '*No exports detected*'}

## File Size
- Lines: ${lines.length}
- Characters: ${content.length}

---
*Generated by GOC Agent Documentation Generator*
`;
  }
}
