// Intelligent Semantic Engine with Advanced Indexing

import { Logger } from '../../utils/Logger';
import { CodebaseAnalyzer } from '../../codebase/CodebaseAnalyzer';
import { CodeIndexer, SearchResult } from '../../indexing/CodeIndexer';
import { 
  IContextEngine, 
  ContextEngineConfig, 
  SemanticGraph, 
  ContextQuery, 
  EnhancedContext,
  RelevanceScore,
  SemanticMatch,
  ContextEngineStats,
  SemanticNode,
  SemanticRelationship
} from '../IContextEngine';

export class IntelligentSemanticEngine implements IContextEngine {
  public readonly name = 'Intelligent Semantic Engine';
  public readonly version = '1.0.0';
  
  private logger: Logger;
  private config?: ContextEngineConfig;
  private codebaseAnalyzer: CodebaseAnalyzer;
  private indexer: CodeIndexer;
  private isInitialized = false;
  private semanticGraph?: SemanticGraph;

  constructor(logger: Logger, codebaseAnalyzer: CodebaseAnalyzer) {
    this.logger = logger;
    this.codebaseAnalyzer = codebaseAnalyzer;
    this.indexer = new CodeIndexer(logger, codebaseAnalyzer);
  }

  public async initialize(config: ContextEngineConfig): Promise<void> {
    this.config = config;
    
    try {
      this.logger.info('🧠 Initializing Intelligent Semantic Engine...');
      
      // Build the code index
      await this.indexer.buildIndex();
      
      // Build semantic graph from index
      await this.buildSemanticGraphFromIndex();
      
      this.isInitialized = true;
      this.logger.success('✅ Intelligent Semantic Engine ready');
      
      const stats = this.indexer.getStats();
      this.logger.info(`📊 Indexed ${stats.totalSymbols} symbols with ${stats.totalRelationships} relationships`);
      
    } catch (error) {
      throw new Error(`Failed to initialize Intelligent Semantic Engine: ${error}`);
    }
  }

  public async isAvailable(): Promise<boolean> {
    return this.isInitialized;
  }

  public async analyzeCodebase(rootPath: string): Promise<SemanticGraph> {
    if (!this.isInitialized) {
      throw new Error('Engine not initialized');
    }

    this.logger.info('🔍 Analyzing codebase with intelligent semantic analysis...');
    
    // Rebuild index for the new path
    await this.indexer.buildIndex(rootPath);
    await this.buildSemanticGraphFromIndex();
    
    return this.semanticGraph!;
  }

  private async buildSemanticGraphFromIndex(): Promise<void> {
    const nodes = new Map<string, SemanticNode>();
    const relationships: SemanticRelationship[] = [];
    
    // Convert indexed symbols to semantic nodes
    const stats = this.indexer.getStats();
    const allSymbols = this.indexer.getSymbolsByType('function')
      .concat(this.indexer.getSymbolsByType('class'))
      .concat(this.indexer.getSymbolsByType('interface'))
      .concat(this.indexer.getSymbolsByType('method'));
    
    for (const symbol of allSymbols) {
      const node: SemanticNode = {
        id: symbol.id,
        type: symbol.type as any,
        name: symbol.name,
        path: symbol.filePath,
        startLine: symbol.startLine,
        endLine: symbol.endLine,
        content: symbol.signature,
        metadata: {
          ...symbol.metadata,
          parameters: symbol.parameters,
          returnType: symbol.returnType,
          visibility: symbol.visibility,
          isStatic: symbol.isStatic,
          isAsync: symbol.isAsync,
          parentClass: symbol.parentClass
        }
      };
      
      nodes.set(node.id, node);
    }
    
    this.semanticGraph = {
      nodes,
      relationships,
      metadata: {
        generatedAt: new Date(),
        version: this.version,
        rootPath: process.cwd(),
        totalNodes: nodes.size,
        totalRelationships: relationships.length
      }
    };
  }

  public async updateSemanticGraph(changedFiles: string[]): Promise<void> {
    this.logger.debug(`Intelligent engine: Updating graph for ${changedFiles.length} files`);
    
    // Rebuild index for changed files
    await this.indexer.buildIndex();
    await this.buildSemanticGraphFromIndex();
  }

  public async buildEnhancedContext(query: ContextQuery): Promise<EnhancedContext> {
    const startTime = Date.now();
    
    if (!this.isInitialized) {
      throw new Error('Engine not initialized');
    }

    this.logger.debug(`🔍 Building enhanced context for: "${query.query}"`);
    
    // Use intelligent search to find relevant symbols
    const searchResults = await this.indexer.search(query.query, {
      maxResults: query.maxResults || 20,
      includeRelated: query.includeRelated !== false
    });
    
    // Convert search results to relevance scores
    const relevanceScores: RelevanceScore[] = searchResults.map(result => ({
      fileId: `file_${result.symbol.filePath}`,
      filePath: result.symbol.filePath,
      score: result.relevanceScore,
      reasons: result.matchReasons,
      semanticMatches: [{
        nodeId: result.symbol.id,
        matchType: 'semantic',
        confidence: result.relevanceScore,
        snippet: result.symbol.signature
      }]
    }));
    
    // Get related symbols for semantic context
    const relatedFunctions = searchResults
      .filter(r => r.symbol.type === 'function')
      .map(r => this.convertSymbolToSemanticNode(r.symbol))
      .slice(0, 10);
    
    const relatedClasses = searchResults
      .filter(r => r.symbol.type === 'class')
      .map(r => this.convertSymbolToSemanticNode(r.symbol))
      .slice(0, 10);
    
    const dependencies = searchResults
      .filter(r => r.symbol.imports && r.symbol.imports.length > 0)
      .map(r => this.convertSymbolToSemanticNode(r.symbol))
      .slice(0, 10);
    
    // Detect architectural patterns
    const architecturalPatterns = await this.detectIntelligentArchitecturalPatterns(searchResults);
    const codePatterns = await this.detectIntelligentCodePatterns(searchResults);
    
    // Build primary content
    const primaryContent = this.buildIntelligentPrimaryContent(searchResults, query);
    
    return {
      primaryContent,
      semanticContext: {
        relatedFunctions,
        relatedClasses,
        dependencies,
        dependents: [], // TODO: Implement dependents analysis
        architecturalPatterns,
        codePatterns
      },
      relevanceScores,
      metadata: {
        generatedAt: new Date(),
        queryType: query.type,
        processingTime: Date.now() - startTime,
        contextLength: primaryContent.length,
        semanticNodesIncluded: relatedFunctions.length + relatedClasses.length
      }
    };
  }

  private convertSymbolToSemanticNode(symbol: any): SemanticNode {
    return {
      id: symbol.id,
      type: symbol.type,
      name: symbol.name,
      path: symbol.filePath,
      startLine: symbol.startLine,
      endLine: symbol.endLine,
      content: symbol.signature,
      metadata: symbol.metadata || {}
    };
  }

  private buildIntelligentPrimaryContent(searchResults: SearchResult[], query: ContextQuery): string {
    const stats = this.indexer.getStats();
    
    let content = `Intelligent semantic analysis of codebase:

## Index Statistics
- Total symbols indexed: ${stats.totalSymbols}
- Total relationships: ${stats.totalRelationships}
- Files covered: ${stats.filesCovered}
- Last indexed: ${stats.lastIndexed.toISOString()}

## Query Analysis: "${query.query}"
- Query type: ${query.type}
- Results found: ${searchResults.length}
- Top matches:
`;

    // Add top search results
    for (const result of searchResults.slice(0, 5)) {
      content += `
### ${result.symbol.name} (${result.symbol.type})
- **File**: ${result.symbol.filePath}:${result.symbol.startLine}
- **Relevance**: ${(result.relevanceScore * 100).toFixed(1)}%
- **Match reasons**: ${result.matchReasons.join(', ')}
- **Signature**: \`${result.symbol.signature || 'N/A'}\`
`;
      
      if (result.relatedSymbols.length > 0) {
        content += `- **Related**: ${result.relatedSymbols.map(s => s.name).join(', ')}\n`;
      }
    }

    return content;
  }

  private async detectIntelligentArchitecturalPatterns(searchResults: SearchResult[]): Promise<string[]> {
    const patterns: string[] = [];
    
    // Analyze the symbols to detect patterns
    const classes = searchResults.filter(r => r.symbol.type === 'class');
    const interfaces = searchResults.filter(r => r.symbol.type === 'interface');
    const functions = searchResults.filter(r => r.symbol.type === 'function');
    
    // MVC Pattern detection
    const hasControllers = classes.some(r => r.symbol.name.toLowerCase().includes('controller'));
    const hasModels = classes.some(r => r.symbol.name.toLowerCase().includes('model'));
    const hasViews = classes.some(r => r.symbol.name.toLowerCase().includes('view'));
    if (hasControllers && hasModels) patterns.push('MVC Pattern');
    
    // Repository Pattern
    const hasRepositories = classes.some(r => r.symbol.name.toLowerCase().includes('repository'));
    if (hasRepositories) patterns.push('Repository Pattern');
    
    // Factory Pattern
    const hasFactories = classes.some(r => r.symbol.name.toLowerCase().includes('factory'));
    if (hasFactories) patterns.push('Factory Pattern');
    
    // Service Layer
    const hasServices = classes.some(r => r.symbol.name.toLowerCase().includes('service'));
    if (hasServices) patterns.push('Service Layer Pattern');
    
    // Observer Pattern
    const hasObservers = classes.some(r => r.symbol.name.toLowerCase().includes('observer'));
    if (hasObservers) patterns.push('Observer Pattern');
    
    // Dependency Injection
    const hasInjection = searchResults.some(r => 
      r.symbol.signature?.includes('inject') || 
      r.symbol.parameters?.some(p => p.includes('inject'))
    );
    if (hasInjection) patterns.push('Dependency Injection');
    
    return patterns;
  }

  private async detectIntelligentCodePatterns(searchResults: SearchResult[]): Promise<string[]> {
    const patterns: string[] = [];
    
    // Async/Await pattern
    const hasAsync = searchResults.some(r => r.symbol.isAsync);
    if (hasAsync) patterns.push('Async/Await Pattern');
    
    // Static methods
    const hasStatic = searchResults.some(r => r.symbol.isStatic);
    if (hasStatic) patterns.push('Static Methods');
    
    // Interface implementation
    const hasInterfaces = searchResults.some(r => r.symbol.type === 'interface');
    if (hasInterfaces) patterns.push('Interface-based Design');
    
    // Generic/Template usage
    const hasGenerics = searchResults.some(r => 
      r.symbol.signature?.includes('<') && r.symbol.signature?.includes('>')
    );
    if (hasGenerics) patterns.push('Generic Programming');
    
    return patterns;
  }

  public async scoreRelevance(query: string, files: string[]): Promise<RelevanceScore[]> {
    const searchResults = await this.indexer.search(query, { maxResults: 50 });
    
    return searchResults
      .filter(result => files.includes(result.symbol.filePath))
      .map(result => ({
        fileId: `file_${result.symbol.filePath}`,
        filePath: result.symbol.filePath,
        score: result.relevanceScore,
        reasons: result.matchReasons,
        semanticMatches: [{
          nodeId: result.symbol.id,
          matchType: 'semantic',
          confidence: result.relevanceScore,
          snippet: result.symbol.signature
        }]
      }));
  }

  public async findSimilarCode(codeSnippet: string, language?: string): Promise<SemanticMatch[]> {
    // Extract keywords from the code snippet
    const keywords = codeSnippet.match(/\w+/g) || [];
    const searchQuery = keywords.join(' ');
    
    const searchResults = await this.indexer.search(searchQuery, { maxResults: 10 });
    
    return searchResults.map(result => ({
      nodeId: result.symbol.id,
      matchType: 'semantic',
      confidence: result.relevanceScore,
      snippet: result.symbol.signature
    }));
  }

  public async findRelatedNodes(nodeId: string, relationshipTypes?: string[]): Promise<SemanticNode[]> {
    const symbol = this.indexer.getSymbolById(nodeId);
    if (!symbol) return [];
    
    // Find related symbols through search
    const searchResults = await this.indexer.search(symbol.name, { 
      maxResults: 10,
      includeRelated: true 
    });
    
    return searchResults
      .filter(result => result.symbol.id !== nodeId)
      .map(result => this.convertSymbolToSemanticNode(result.symbol));
  }

  public async detectArchitecturalPatterns(graph: SemanticGraph): Promise<string[]> {
    const allSymbols = Array.from(graph.nodes.values());
    const searchResults = allSymbols.map(node => ({
      symbol: {
        id: node.id,
        name: node.name,
        type: node.type,
        filePath: node.path,
        signature: node.content
      },
      relevanceScore: 1.0,
      matchReasons: [],
      relatedSymbols: []
    }));
    
    return this.detectIntelligentArchitecturalPatterns(searchResults as any);
  }

  public async detectCodePatterns(nodes: SemanticNode[]): Promise<string[]> {
    const searchResults = nodes.map(node => ({
      symbol: {
        id: node.id,
        name: node.name,
        type: node.type,
        filePath: node.path,
        signature: node.content,
        isAsync: node.metadata.isAsync,
        isStatic: node.metadata.isStatic
      },
      relevanceScore: 1.0,
      matchReasons: [],
      relatedSymbols: []
    }));
    
    return this.detectIntelligentCodePatterns(searchResults as any);
  }

  public async clearCache(): Promise<void> {
    // Clear indexer cache if needed
    this.logger.debug('Clearing intelligent semantic engine cache');
  }

  public async getStats(): Promise<ContextEngineStats> {
    const indexStats = this.indexer.getStats();
    
    return {
      totalNodes: indexStats.totalSymbols,
      totalRelationships: indexStats.totalRelationships,
      cacheHitRate: 0.85, // Simulated cache hit rate
      averageQueryTime: indexStats.averageQueryTime,
      lastUpdated: indexStats.lastIndexed,
      memoryUsage: indexStats.indexSize
    };
  }
}
