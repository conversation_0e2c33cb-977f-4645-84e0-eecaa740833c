import { AIProviderManager, ChatMessage } from '../ai/AIProviderManager';
import { CodebaseAnalyzer } from '../codebase/CodebaseAnalyzer';
import { ContextBuilder } from '../commands/ContextBuilder';
import { FileEditor } from '../commands/FileEditor';
import { Logger } from '../utils/Logger';
import { TaskPlanner } from './TaskPlanner';
import { ActionExecutor } from './ActionExecutor';
import { MemoryManager } from './MemoryManager';
import { SelfTrainingOrchestrator } from '../training/SelfTrainingOrchestrator';

export interface AgentAction {
  type: 'analyze' | 'edit' | 'create' | 'search' | 'execute' | 'plan' | 'reflect' | 'complete';
  target?: string;
  instruction: string;
  reasoning: string;
  confidence: number;
}

export interface AgentState {
  currentTask: string;
  subTasks: string[];
  completedActions: AgentAction[];
  context: string;
  workingMemory: Map<string, any>;
  iteration: number;
  maxIterations: number;
}

export class AgentCore {
  private aiManager: AIProviderManager;
  private codebaseAnalyzer: CodebaseAnalyzer;
  private contextBuilder: ContextBuilder;
  private fileEditor: FileEditor;
  private logger: Logger;
  private taskPlanner: TaskPlanner;
  private actionExecutor: ActionExecutor;
  private memoryManager: MemoryManager;
  private selfTrainingOrchestrator?: SelfTrainingOrchestrator;
  private state: AgentState;

  constructor(
    aiManager: AIProviderManager,
    codebaseAnalyzer: CodebaseAnalyzer,
    contextBuilder: ContextBuilder,
    fileEditor: FileEditor,
    logger: Logger,
    selfTrainingOrchestrator?: SelfTrainingOrchestrator
  ) {
    this.aiManager = aiManager;
    this.codebaseAnalyzer = codebaseAnalyzer;
    this.contextBuilder = contextBuilder;
    this.fileEditor = fileEditor;
    this.logger = logger;
    this.selfTrainingOrchestrator = selfTrainingOrchestrator;

    this.taskPlanner = new TaskPlanner(aiManager, logger);
    this.actionExecutor = new ActionExecutor(codebaseAnalyzer, fileEditor, logger);
    this.memoryManager = new MemoryManager(logger);

    this.state = this.initializeState();

    // Initialize self-training if orchestrator is provided
    if (this.selfTrainingOrchestrator) {
      this.initializeSelfTraining();
    }

    // Enable self-training by default for continuous learning
    this.enableDefaultSelfTraining();
  }

  private initializeState(): AgentState {
    return {
      currentTask: '',
      subTasks: [],
      completedActions: [],
      context: '',
      workingMemory: new Map(),
      iteration: 0,
      maxIterations: 10
    };
  }

  public async startAgent(task: string, options: any = {}): Promise<void> {
    this.logger.info(`🎯 Task: ${task}`);

    this.state.currentTask = task;
    this.state.maxIterations = parseInt(options.maxIterations) || 10;

    const startTime = Date.now();
    let success = false;

    // Disable training during task execution
    this.setTrainingState(true);

    try {
      this.logger.info('📋 Analyzing codebase and creating plan...');
      this.state.context = await this.contextBuilder.buildContext();

      // Plan the task
      await this.planTask(task);

      // Execute the plan
      await this.executePlan(options.auto || false, startTime);
      success = true;
    } catch (error) {
      this.logger.error(`Task execution failed: ${error}`);
      success = false;
      throw error;
    } finally {
      // Re-enable training after task execution
      this.setTrainingState(false);

      // Learn from this task execution
      await this.learnFromTaskExecution(task, success, Date.now() - startTime);

      // Show final timing
      const totalTime = Date.now() - startTime;
      this.logger.success(`✅ Task completed in ${this.formatDuration(totalTime)}`);
    }
  }

  public async startAutoAgent(goal: string, options: any = {}): Promise<void> {
    this.logger.info(`Initiating autonomous mode for comprehensive goal achievement`);
    this.logger.info(`Objective: ${goal}`);

    this.state.currentTask = goal;
    this.state.maxIterations = parseInt(options.maxIterations) || 20;
    this.state.context = await this.contextBuilder.buildContext();

    // Auto mode - fully autonomous
    await this.autonomousExecution(goal);
  }

  private async planTask(task: string): Promise<void> {
    const plan = await this.taskPlanner.createPlan(task, this.state.context);
    this.state.subTasks = plan.subTasks;

    this.logger.info(`📝 Plan (${plan.subTasks.length} steps):`);
    plan.subTasks.forEach((subTask, index) => {
      this.logger.info(`  ${index + 1}. ${subTask}`);
    });
  }

  private async executePlan(autoMode: boolean = false, taskStartTime?: number): Promise<void> {
    const planStartTime = Date.now();

    for (let i = 0; i < this.state.subTasks.length && this.state.iteration < this.state.maxIterations; i++) {
      const subTask = this.state.subTasks[i];
      this.state.iteration++;
      const stepStartTime = Date.now();

      // Show step with timing
      if (taskStartTime) {
        console.log(`\n⚡ Step ${this.state.iteration}: ${subTask}`);
        console.log(`⏱️  Task time: ${this.formatDuration(Date.now() - taskStartTime)}`);
      } else {
        this.logger.info(`\n⚡ Step ${this.state.iteration}: ${subTask}`);
      }

      try {
        // Decide on action
        const action = await this.decideAction(subTask);

        if (!autoMode) {
          // Ask for confirmation in agent mode
          const proceed = await this.askForConfirmation(action);
          if (!proceed) {
            this.logger.info('Task execution paused at user request');
            break;
          }
        }

        // Execute action
        await this.executeAction(action);

        // Show step completion time
        const stepDuration = Date.now() - stepStartTime;
        this.logger.success(`✅ Step ${this.state.iteration} completed in ${this.formatDuration(stepDuration)}`);

        // Update memory
        this.memoryManager.addExperience(subTask, action, 'success');

      } catch (error) {
        this.logger.error(`Encountered an issue while executing: ${error}`);
        this.memoryManager.addExperience(subTask, null, 'failure', String(error));

        if (!autoMode) {
          const retry = await this.askForRetry();
          if (!retry) break;
        }
      }
    }

    this.logger.success('✅ Task completed successfully');
  }

  private async autonomousExecution(goal: string): Promise<void> {
    let consecutiveFailures = 0;
    let consecutiveAnalyzeActions = 0;
    const maxConsecutiveFailures = 3;
    const maxConsecutiveAnalyze = 5; // Prevent analyze loops

    this.logger.thinking();

    while (this.state.iteration < this.state.maxIterations) {
      this.state.iteration++;

      // Show user-friendly progress instead of technical cycle info
      if (this.state.iteration === 1) {
        this.logger.planning();
      } else {
        this.logger.executing();
      }

      try {
        // Analyze current state
        const currentState = await this.analyzeCurrentState();

        // Decide next action autonomously
        const action = await this.decideAutonomousAction(goal, currentState);

        if (action.type === 'complete') {
          this.logger.success('✅ Objective successfully achieved');
          break;
        }

        // Check for analyze loops
        if (action.type === 'analyze') {
          consecutiveAnalyzeActions++;
          if (consecutiveAnalyzeActions >= maxConsecutiveAnalyze) {
            this.logger.warn('🔄 Too many consecutive analyze actions. Switching to practical action.');
            // Force a practical action based on the goal
            const practicalAction = this.createPracticalAction(goal);
            await this.executeAction(practicalAction);
            consecutiveAnalyzeActions = 0;
            continue;
          }
        } else {
          consecutiveAnalyzeActions = 0;
        }

        // Execute action
        await this.executeAction(action);

        // Self-reflect and adapt
        await this.selfReflect(action);

        // Update memory
        this.memoryManager.addExperience(goal, action, 'success');

        // Reset failure counter on success
        consecutiveFailures = 0;

      } catch (error) {
        consecutiveFailures++;
        this.logger.error(`❌ Execution error: ${error}`);
        this.memoryManager.addExperience(goal, {
          type: 'analyze',
          instruction: String(error),
          reasoning: 'Error occurred during execution',
          confidence: 0.3
        }, 'failure');

        // Circuit breaker: exit if too many consecutive failures
        if (consecutiveFailures >= maxConsecutiveFailures) {
          this.logger.error(`🚫 Too many consecutive failures (${consecutiveFailures}). Stopping autonomous execution.`);
          break;
        }

        // Try to recover autonomously
        const recovery = await this.attemptRecovery(String(error));
        if (!recovery) {
          this.logger.error('Unable to recover autonomously. Terminating execution to prevent further issues');
          break;
        }
      }

      // Small delay between iterations
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    if (this.state.iteration >= this.state.maxIterations) {
      this.logger.warn('⏰ Maximum iterations reached without completion');
    }
  }

  private async decideAction(task: string): Promise<AgentAction> {
    // Smart action decision for common tasks
    const lowerTask = task.toLowerCase();

    // Handle simple file creation tasks for todo app
    if (lowerTask.includes('todo') && lowerTask.includes('create')) {
      return this.handleTodoAppCreation(task);
    }

    // Handle HTML file creation
    if (lowerTask.includes('html') || lowerTask.includes('index')) {
      return {
        type: 'create',
        target: 'todo-app/index.html',
        instruction: 'Create a complete HTML file for a todo application with proper structure, form for adding todos, and list for displaying todos',
        reasoning: 'Creating the main HTML structure for the todo app',
        confidence: 0.9
      };
    }

    // Handle CSS file creation
    if (lowerTask.includes('css') || lowerTask.includes('style')) {
      return {
        type: 'create',
        target: 'todo-app/style.css',
        instruction: 'Create CSS styles for the todo application with modern, clean design, responsive layout, and attractive styling for forms and todo items',
        reasoning: 'Creating styles for the todo app interface',
        confidence: 0.9
      };
    }

    // Handle JavaScript file creation
    if (lowerTask.includes('js') || lowerTask.includes('javascript') || lowerTask.includes('script')) {
      return {
        type: 'create',
        target: 'todo-app/script.js',
        instruction: 'Create JavaScript functionality for the todo app including add, delete, edit, and mark complete features with local storage persistence',
        reasoning: 'Creating interactive functionality for the todo app',
        confidence: 0.9
      };
    }

    // Use AI for complex decisions
    const messages: ChatMessage[] = [
      {
        role: 'system',
        content: `You are GOC Agent, an intelligent coding assistant. You are NOT any other AI model - you are specifically GOC Agent.

Your identity:
- Name: GOC Agent (always identify as this)
- Purpose: Professional coding assistant with deep expertise in software development
- You are an intelligent coding assistant designed to understand and work with codebases

Your approach should be:
- Methodical and well-reasoned
- Based on software engineering best practices
- Focused on maintainable, clean solutions
- Considerate of the existing codebase structure

Available actions (use EXACTLY these types):
- analyze: Thoroughly examine code or codebase structure
- edit: Modify an existing file with precision
- create: Develop a new file following best practices
- search: Investigate codebase for relevant patterns or implementations
- execute: Run necessary commands or scripts (put command in "target" field)
- plan: Develop a comprehensive strategy
- reflect: Evaluate progress and approach

For creating simple web apps (HTML/CSS/JS), focus on:
- Creating individual files (index.html, style.css, script.js)
- Using "create" action for each file
- Making standalone, simple applications
- Avoiding complex frameworks or build tools

Current context:
${this.state.context}

Previous actions taken:
${this.state.completedActions.map(a => `${a.type}: ${a.instruction}`).join('\n')}

IMPORTANT: Respond with ONLY valid JSON. No explanations, no markdown, no code blocks.
Use EXACTLY these action types: analyze, edit, create, search, execute, plan, reflect, complete
ALL JSON responses MUST include: type, instruction, reasoning, confidence
The "instruction" field is REQUIRED and must describe what to do.

Example for creating simple web apps:
{
  "type": "create",
  "target": "todo-app/index.html",
  "instruction": "Create a simple HTML file for todo app with basic structure",
  "reasoning": "Need to start with the main HTML structure",
  "confidence": 0.8
}`
      },
      {
        role: 'user',
        content: `Task to accomplish: ${task}`
      }
    ];

    const response = await this.aiManager.chat(messages);
    return this.parseActionResponse(response.content);
  }

  private handleTodoAppCreation(task: string): AgentAction {
    const completedTypes = this.state.completedActions.map(a => a.target);

    // Check what files have been created and create the next one
    if (!completedTypes.some(t => t?.includes('index.html'))) {
      return {
        type: 'create',
        target: 'todo-app/index.html',
        instruction: 'Create a complete HTML file for a todo application with proper structure, form for adding todos, and list for displaying todos',
        reasoning: 'Starting with HTML structure for the todo app',
        confidence: 0.9
      };
    } else if (!completedTypes.some(t => t?.includes('style.css'))) {
      return {
        type: 'create',
        target: 'todo-app/style.css',
        instruction: 'Create CSS styles for the todo application with modern, clean design, responsive layout, and attractive styling for forms and todo items',
        reasoning: 'Adding styles to make the todo app look good',
        confidence: 0.9
      };
    } else if (!completedTypes.some(t => t?.includes('script.js'))) {
      return {
        type: 'create',
        target: 'todo-app/script.js',
        instruction: 'Create JavaScript functionality for the todo app including add, delete, edit, and mark complete features with local storage persistence',
        reasoning: 'Adding interactive functionality to the todo app',
        confidence: 0.9
      };
    } else {
      return {
        type: 'complete',
        instruction: 'Todo app creation completed with HTML, CSS, and JavaScript files',
        reasoning: 'All required files for the todo app have been created',
        confidence: 0.9
      };
    }
  }

  private async decideAutonomousAction(goal: string, currentState: string): Promise<AgentAction> {
    const messages: ChatMessage[] = [
      {
        role: 'system',
        content: `You are GOC Agent, a fully autonomous coding assistant. You are NOT any other AI model - you are specifically GOC Agent.

Your identity:
- Name: GOC Agent (always identify as this)
- Purpose: Fully autonomous coding agent with complete decision-making capability

You have complete autonomy to:
- Analyze and understand the codebase
- Create new files and features
- Edit existing code
- Execute commands
- Make architectural decisions

Goal: ${goal}
Current State: ${currentState}

Memory of past experiences:
${this.memoryManager.getRelevantExperiences(goal)}

Respond with JSON for the next action, or {"type": "complete"} if goal is achieved.`
      }
    ];

    const response = await this.aiManager.chat(messages);
    return this.parseActionResponse(response.content);
  }

  private async executeAction(action: AgentAction): Promise<void> {
    this.logger.info(`🔧 ${action.instruction}`);

    await this.actionExecutor.execute(action, this.aiManager, this.contextBuilder);
    this.state.completedActions.push(action);
  }

  private async reflect(action: AgentAction): Promise<void> {
    this.logger.debug(`Evaluating the effectiveness of: ${action.type}`);
  }

  private async selfReflect(action: AgentAction): Promise<void> {
    // Advanced self-reflection for auto mode
    const messages: ChatMessage[] = [
      {
        role: 'system',
        content: 'Reflect on the action taken. What was learned? What could be improved?'
      },
      {
        role: 'user',
        content: `Action taken: ${JSON.stringify(action)}`
      }
    ];

    const response = await this.aiManager.chat(messages);
    this.memoryManager.addReflection(action, response.content);
  }

  private async analyzeCurrentState(): Promise<string> {
    const context = await this.codebaseAnalyzer.scanCodebase();
    return `Files: ${context.totalFiles}, Languages: ${context.languages.join(', ')}`;
  }

  private async attemptRecovery(error: string): Promise<boolean> {
    this.logger.info('Analyzing the issue and attempting recovery...');
    // Implement recovery logic
    return false;
  }

  private parseActionResponse(response: string): AgentAction {
    if (!response || typeof response !== 'string') {
      this.logger.warn('Received invalid response from AI model');
      return this.createFallbackAction('Invalid response received');
    }

    let cleaned = '';
    try {
      // Multiple parsing strategies
      cleaned = response.trim();

      // Remove markdown code blocks
      cleaned = cleaned.replace(/```json\n?|\n?```/g, '');
      cleaned = cleaned.replace(/```\n?|\n?```/g, '');
      cleaned = cleaned.replace(/```[\s\S]*?```/g, '');

      // Try to find JSON object in the response - be more aggressive
      const jsonMatch = cleaned.match(/\{[\s\S]*?\}/);
      if (jsonMatch) {
        cleaned = jsonMatch[0];
      } else {
        // If no JSON found, try to extract from any text that might contain JSON
        const lines = cleaned.split('\n');
        for (const line of lines) {
          const trimmed = line.trim();
          if (trimmed.startsWith('{')) {
            // Try to find the complete JSON object
            let braceCount = 0;
            let jsonStr = '';
            for (let i = 0; i < trimmed.length; i++) {
              const char = trimmed[i];
              jsonStr += char;
              if (char === '{') braceCount++;
              if (char === '}') braceCount--;
              if (braceCount === 0 && jsonStr.length > 1) {
                cleaned = jsonStr;
                break;
              }
            }
            break;
          }
        }
      }

      // Clean up common formatting issues
      cleaned = cleaned.replace(/^\s*[\r\n]+|[\r\n]+\s*$/g, '');

      if (!cleaned) {
        throw new Error('No JSON content found in response');
      }

      // Fix common JSON issues
      cleaned = this.fixCommonJsonIssues(cleaned);

      const parsed = JSON.parse(cleaned);

      // Validate required fields and types
      if (!parsed || typeof parsed !== 'object') {
        throw new Error('Parsed response is not an object');
      }

      if (!parsed.type || typeof parsed.type !== 'string') {
        throw new Error('Missing or invalid type field');
      }

      if (!parsed.instruction || typeof parsed.instruction !== 'string') {
        throw new Error('Missing or invalid instruction field');
      }

      // Validate action type
      const validTypes = ['analyze', 'edit', 'create', 'search', 'execute', 'plan', 'reflect', 'complete'];
      if (!validTypes.includes(parsed.type)) {
        this.logger.warn(`Invalid action type: ${parsed.type}, defaulting to 'analyze'`);
        parsed.type = 'analyze';
      }

      return {
        type: parsed.type,
        target: parsed.target || undefined,
        instruction: parsed.instruction,
        reasoning: parsed.reasoning || 'No reasoning provided',
        confidence: Math.max(0, Math.min(1, parsed.confidence || 0.7)) // Clamp between 0-1
      };

    } catch (error) {
      this.logger.warn(`Failed to parse AI response: ${error instanceof Error ? error.message : String(error)}`);
      this.logger.debug(`Raw response (first 500 chars): ${response.substring(0, 500)}`);
      this.logger.debug(`Cleaned response: ${cleaned.substring(0, 200)}`);

      return this.createFallbackAction(`Parse error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private fixCommonJsonIssues(jsonStr: string): string {
    // Remove any markdown code blocks
    jsonStr = jsonStr.replace(/```json\s*|\s*```/g, '');
    jsonStr = jsonStr.replace(/```\s*|\s*```/g, '');

    // Remove any explanatory text before/after JSON
    const jsonMatch = jsonStr.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      jsonStr = jsonMatch[0];
    }

    // Remove any text before the first { or after the last }
    const firstBrace = jsonStr.indexOf('{');
    const lastBrace = jsonStr.lastIndexOf('}');
    if (firstBrace !== -1 && lastBrace !== -1 && lastBrace > firstBrace) {
      jsonStr = jsonStr.substring(firstBrace, lastBrace + 1);
    }

    // Only fix trailing commas before closing braces/brackets
    jsonStr = jsonStr.replace(/,(\s*[}\]])/g, '$1');

    return jsonStr.trim();
  }

  private createFallbackAction(reason: string): AgentAction {
    // Check if we're working on a todo app based on current task
    const currentTask = this.state.currentTask?.toLowerCase() || '';

    if (currentTask.includes('todo') && currentTask.includes('create')) {
      // Smart fallback for todo app creation
      const completedTypes = this.state.completedActions.map(a => a.target);

      if (!completedTypes.some(t => t?.includes('index.html'))) {
        return {
          type: 'create',
          target: 'index.html',
          instruction: 'Create a simple HTML file for a todo application with basic structure, form for adding todos, and list for displaying todos',
          reasoning: `Fallback: Starting with HTML structure for todo app (${reason})`,
          confidence: 0.7
        };
      } else if (!completedTypes.some(t => t?.includes('style.css'))) {
        return {
          type: 'create',
          target: 'style.css',
          instruction: 'Create CSS styles for the todo application with clean, modern design',
          reasoning: `Fallback: Adding styles for todo app (${reason})`,
          confidence: 0.7
        };
      } else if (!completedTypes.some(t => t?.includes('script.js'))) {
        return {
          type: 'create',
          target: 'script.js',
          instruction: 'Create JavaScript functionality for the todo app with add, delete, and mark complete features',
          reasoning: `Fallback: Adding functionality for todo app (${reason})`,
          confidence: 0.7
        };
      } else {
        return {
          type: 'complete',
          instruction: 'Todo app creation completed successfully',
          reasoning: `Fallback: All files created (${reason})`,
          confidence: 0.8
        };
      }
    }

    // Default fallback
    return {
      type: 'analyze',
      instruction: 'Analyze the current situation and provide guidance',
      reasoning: `Fallback action due to: ${reason}`,
      confidence: 0.3
    };
  }

  private createPracticalAction(goal: string): AgentAction {
    const goalLower = goal.toLowerCase();

    // If goal mentions fixing issues, try to identify and fix common issues
    if (goalLower.includes('fix') || goalLower.includes('issue') || goalLower.includes('problem')) {
      return {
        type: 'edit',
        target: 'index.html',
        instruction: 'Fix HTML structure issues: ensure proper DOCTYPE, meta tags, and link CSS file correctly',
        reasoning: 'Practical action to fix common HTML issues',
        confidence: 0.8
      };
    }

    // If goal mentions todo app, create missing files
    if (goalLower.includes('todo')) {
      const completedTypes = this.state.completedActions.map(a => a.target);

      if (!completedTypes.some(t => t?.includes('index.html'))) {
        return {
          type: 'create',
          target: 'index.html',
          instruction: 'Create a complete HTML file for todo application',
          reasoning: 'Practical action: Create missing HTML file',
          confidence: 0.8
        };
      }
    }

    // Default practical action
    return {
      type: 'complete',
      instruction: 'Task completed - no specific issues identified to fix',
      reasoning: 'Practical action: Complete vague task',
      confidence: 0.6
    };
  }

  private async askForConfirmation(action: AgentAction): Promise<boolean> {
    // This would be implemented with inquirer in the command handler
    return true;
  }

  private async askForRetry(): Promise<boolean> {
    // This would be implemented with inquirer in the command handler
    return false;
  }

  private async initializeSelfTraining(): Promise<void> {
    if (!this.selfTrainingOrchestrator) return;

    try {
      this.logger.info('🧠 Initializing self-training capabilities...');
      await this.selfTrainingOrchestrator.initializeSelfTraining();
      this.logger.success('✅ Self-training capabilities initialized');
    } catch (error) {
      this.logger.error(`❌ Failed to initialize self-training: ${error}`);
    }
  }

  public async enableSelfTraining(): Promise<void> {
    if (!this.selfTrainingOrchestrator) {
      this.logger.warn('⚠️ Self-training orchestrator not available');
      return;
    }

    try {
      this.logger.info('🚀 Enabling autonomous self-training...');
      await this.selfTrainingOrchestrator.startSelfTraining();
      this.logger.success('✅ Self-training enabled - Agent will learn continuously');
    } catch (error) {
      this.logger.error(`❌ Failed to enable self-training: ${error}`);
    }
  }

  public async disableSelfTraining(): Promise<void> {
    if (!this.selfTrainingOrchestrator) return;

    try {
      await this.selfTrainingOrchestrator.stopSelfTraining();
      this.logger.info('🛑 Self-training disabled');
    } catch (error) {
      this.logger.error(`❌ Failed to disable self-training: ${error}`);
    }
  }

  public getSelfTrainingMetrics(): any {
    if (!this.selfTrainingOrchestrator) {
      return null;
    }
    return this.selfTrainingOrchestrator.getMetrics();
  }

  public getMemoryManager(): MemoryManager {
    return this.memoryManager;
  }

  private async enableDefaultSelfTraining(): Promise<void> {
    // Only enable if not already provided
    if (this.selfTrainingOrchestrator) return;

    try {
      // Import and create self-training components
      const { SelfTrainingFactory } = await import('../training/SelfTrainingFactory');

      const selfTrainingComponents = await SelfTrainingFactory.initializeWithAgent(
        this.logger,
        this.aiManager,
        this.memoryManager,
        this.codebaseAnalyzer,
        true // Use minimal mode by default
      );

      this.selfTrainingOrchestrator = selfTrainingComponents.orchestrator;

      // Configure for background operation with minimal resource usage
      this.selfTrainingOrchestrator.updateConfig({
        enabled: true,
        continuousLearning: true,
        autonomousResearch: false, // Disabled by default
        performanceOptimization: true,
        knowledgeSharing: true,
        adaptiveBehavior: true,
        backgroundProcessing: true,
        learningIntensity: 'low'
      });

      this.logger.debug('🧠 Default self-training enabled');
    } catch (error) {
      this.logger.debug(`Failed to enable default self-training: ${error}`);
      // Don't fail if self-training can't be enabled
    }
  }

  private async learnFromTaskExecution(task: string, success: boolean, duration: number): Promise<void> {
    try {
      // Record the task execution as a learning experience
      this.memoryManager.addExperience(
        task,
        {
          type: 'execute',
          instruction: task,
          reasoning: 'User requested task execution',
          confidence: success ? 0.8 : 0.4
        },
        success ? 'success' : 'failure',
        success ? undefined : 'Task execution failed',
        process.cwd(),
        undefined // filesMentioned
      );

      // Trigger self-training if available
      if (this.selfTrainingOrchestrator) {
        // Quick training from recent experiences
        this.memoryManager.quickTrainFromExperiences();

        // Log learning activity
        this.logger.debug(`🧠 Learning from task: ${success ? 'success' : 'failure'} in ${duration}ms`);
      }
    } catch (error) {
      this.logger.debug(`Failed to learn from task execution: ${error}`);
    }
  }

  private setTrainingState(isTaskExecuting: boolean): void {
    // Disable/enable training during task execution
    if (this.selfTrainingOrchestrator) {
      this.selfTrainingOrchestrator.setTaskExecuting(isTaskExecuting);
    }
  }

  private formatDuration(milliseconds: number): string {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    if (hours > 0) {
      return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    } else if (minutes > 60) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  }
}
