import fs from 'fs';
import path from 'path';
import glob from 'fast-glob';
import ignore from 'ignore';
import chokidar from 'chokidar';
import { ConfigManager } from '../config/ConfigManager';
import { Logger } from '../utils/Logger';

export interface FileInfo {
  path: string;
  relativePath: string;
  size: number;
  extension: string;
  lastModified: Date;
  content?: string;
}

export interface CodebaseContext {
  files: FileInfo[];
  totalFiles: number;
  totalSize: number;
  languages: string[];
  structure: string;
}

export class CodebaseAnalyzer {
  private config: ConfigManager;
  private logger: Logger;
  private watcher?: chokidar.FSWatcher;
  private fileCache: Map<string, FileInfo> = new Map();
  private ignoreFilter: any;

  constructor(config: ConfigManager, logger: Logger) {
    this.config = config;
    this.logger = logger;
    this.initializeIgnoreFilter();
  }

  private initializeIgnoreFilter(): void {
    const config = this.config.getConfig();
    this.ignoreFilter = ignore().add(config.codebase.excludePatterns);
    
    // Also read .gitignore if it exists
    const gitignorePath = path.join(process.cwd(), '.gitignore');
    if (fs.existsSync(gitignorePath)) {
      const gitignoreContent = fs.readFileSync(gitignorePath, 'utf8');
      this.ignoreFilter.add(gitignoreContent);
    }
  }

  public async scanCodebase(rootPath: string = process.cwd()): Promise<CodebaseContext> {
    this.logger.info('Scanning codebase...');
    
    const config = this.config.getConfig();
    const files: FileInfo[] = [];
    let totalSize = 0;
    const languages = new Set<string>();

    try {
      // Get all files matching include patterns
      const allFiles = await glob(config.codebase.includePatterns, {
        cwd: rootPath,
        absolute: true,
        ignore: config.codebase.excludePatterns,
        dot: false
      });

      // Filter files and collect info
      for (const filePath of allFiles) {
        const relativePath = path.relative(rootPath, filePath);
        
        // Skip if ignored
        if (this.ignoreFilter.ignores(relativePath)) {
          continue;
        }

        try {
          const stats = fs.statSync(filePath);
          
          // Skip if file is too large
          if (stats.size > config.codebase.maxFileSize) {
            this.logger.debug(`Skipping large file: ${relativePath} (${stats.size} bytes)`);
            continue;
          }

          const extension = path.extname(filePath).toLowerCase();
          const fileInfo: FileInfo = {
            path: filePath,
            relativePath,
            size: stats.size,
            extension,
            lastModified: stats.mtime
          };

          files.push(fileInfo);
          totalSize += stats.size;
          
          // Track language
          const language = this.getLanguageFromExtension(extension);
          if (language) {
            languages.add(language);
          }

          // Stop if we've reached max files
          if (files.length >= config.codebase.maxFiles) {
            this.logger.warn(`Reached maximum file limit (${config.codebase.maxFiles})`);
            break;
          }
        } catch (error) {
          this.logger.debug(`Error processing file ${relativePath}: ${error instanceof Error ? error.message : String(error)}`);
          continue;
        }
      }

      // Cache the files
      this.fileCache.clear();
      files.forEach(file => this.fileCache.set(file.relativePath, file));

      const structure = await this.generateStructure(rootPath, files);

      this.logger.info(`Scanned ${files.length} files (${this.formatBytes(totalSize)})`);

      return {
        files,
        totalFiles: files.length,
        totalSize,
        languages: Array.from(languages),
        structure
      };
    } catch (error) {
      this.logger.error(`Error scanning codebase: ${error}`);
      throw error;
    }
  }

  public async getFileContent(filePath: string): Promise<string> {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      
      // Update cache
      const relativePath = path.relative(process.cwd(), filePath);
      const fileInfo = this.fileCache.get(relativePath);
      if (fileInfo) {
        fileInfo.content = content;
      }
      
      return content;
    } catch (error) {
      throw new Error(`Failed to read file ${filePath}: ${error}`);
    }
  }

  public async searchFiles(query: string, type: 'text' | 'regex' = 'text'): Promise<FileInfo[]> {
    const results: FileInfo[] = [];
    const searchRegex = type === 'regex' ? new RegExp(query, 'gi') : new RegExp(query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'gi');

    for (const fileInfo of this.fileCache.values()) {
      try {
        if (!fileInfo.content) {
          fileInfo.content = await this.getFileContent(fileInfo.path);
        }

        if (searchRegex.test(fileInfo.content)) {
          results.push(fileInfo);
        }
      } catch (error) {
        this.logger.debug(`Error searching file ${fileInfo.relativePath}: ${error}`);
      }
    }

    return results;
  }

  public getFilesByExtension(extension: string): FileInfo[] {
    return Array.from(this.fileCache.values()).filter(
      file => file.extension === extension
    );
  }

  public startWatching(rootPath: string = process.cwd()): void {
    if (this.watcher) {
      this.watcher.close();
    }

    const config = this.config.getConfig();
    
    this.watcher = chokidar.watch(config.codebase.includePatterns, {
      cwd: rootPath,
      ignored: config.codebase.excludePatterns,
      ignoreInitial: true
    });

    this.watcher.on('add', (filePath) => {
      this.logger.debug(`File added: ${filePath}`);
      this.invalidateCache(filePath);
    });

    this.watcher.on('change', (filePath) => {
      this.logger.debug(`File changed: ${filePath}`);
      this.invalidateCache(filePath);
    });

    this.watcher.on('unlink', (filePath) => {
      this.logger.debug(`File removed: ${filePath}`);
      this.fileCache.delete(filePath);
    });
  }

  public stopWatching(): void {
    if (this.watcher) {
      this.watcher.close();
      this.watcher = undefined;
    }
  }

  private invalidateCache(filePath: string): void {
    const fileInfo = this.fileCache.get(filePath);
    if (fileInfo) {
      fileInfo.content = undefined;
    }
  }

  private getLanguageFromExtension(extension: string): string | null {
    const languageMap: { [key: string]: string } = {
      '.js': 'JavaScript',
      '.ts': 'TypeScript',
      '.jsx': 'React',
      '.tsx': 'React TypeScript',
      '.py': 'Python',
      '.java': 'Java',
      '.cpp': 'C++',
      '.c': 'C',
      '.h': 'C/C++ Header',
      '.cs': 'C#',
      '.php': 'PHP',
      '.rb': 'Ruby',
      '.go': 'Go',
      '.rs': 'Rust',
      '.md': 'Markdown',
      '.json': 'JSON',
      '.yaml': 'YAML',
      '.yml': 'YAML'
    };

    return languageMap[extension] || null;
  }

  private async generateStructure(rootPath: string, files: FileInfo[]): Promise<string> {
    const tree: { [key: string]: any } = {};
    
    files.forEach(file => {
      const parts = file.relativePath.split(path.sep);
      let current = tree;
      
      parts.forEach((part, index) => {
        if (index === parts.length - 1) {
          current[part] = null; // File
        } else {
          if (!current[part]) {
            current[part] = {}; // Directory
          }
          current = current[part];
        }
      });
    });

    return this.treeToString(tree);
  }

  private treeToString(tree: any, indent: string = ''): string {
    let result = '';
    const entries = Object.entries(tree).sort(([a], [b]) => a.localeCompare(b));
    
    entries.forEach(([name, value], index) => {
      const isLast = index === entries.length - 1;
      const prefix = isLast ? '└── ' : '├── ';
      result += `${indent}${prefix}${name}\n`;
      
      if (value !== null) {
        const nextIndent = indent + (isLast ? '    ' : '│   ');
        result += this.treeToString(value, nextIndent);
      }
    });
    
    return result;
  }

  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}
