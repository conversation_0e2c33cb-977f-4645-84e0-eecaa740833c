#!/usr/bin/env node

import { Command } from 'commander';
import chalk from 'chalk';
import { ConfigManager } from './config/ConfigManager';
import { CodebaseAnalyzer } from './codebase/CodebaseAnalyzer';
import { AIProviderManager } from './ai/AIProviderManager';
import { CommandHandler } from './commands/CommandHandler';
import { ContextEngineCommand } from './commands/ContextEngineCommand';
import { SelfTrainCommand } from './commands/SelfTrainCommand';
import { EnhancedChatCommand } from './commands/EnhancedChatCommand';
import { Logger } from './utils/Logger';

const program = new Command();

async function main() {
  try {
    // Validate Node.js version
    const nodeVersion = process.version;
    const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
    if (majorVersion < 16) {
      console.error('Error: Node.js 16 or higher is required. Current version:', nodeVersion);
      process.exit(1);
    }

    // Initialize core components with error handling
    const config = new ConfigManager();
    const logger = new Logger();

    // Validate configuration
    try {
      config.getConfig();
    } catch (configError) {
      logger.error(`Configuration error: ${configError instanceof Error ? configError.message : String(configError)}`);
      logger.info('Run "goc config" to set up your configuration');
      process.exit(1);
    }

    const codebaseAnalyzer = new CodebaseAnalyzer(config, logger);
    const aiManager = new AIProviderManager(config, logger);
    const commandHandler = new CommandHandler(config, codebaseAnalyzer, aiManager, logger);
    const contextEngineCommand = new ContextEngineCommand(logger, config, codebaseAnalyzer);
    const selfTrainCommand = new SelfTrainCommand();
    const enhancedChatCommand = new EnhancedChatCommand();

    // Setup CLI
    program
      .name('goc')
      .description('GOC Agent - A coding agent with multiple AI provider support')
      .version('1.0.0');

    // Main chat command - the primary interface
    program
      .command('chat')
      .description('Start interactive chat session')
      .option('-p, --provider <provider>', 'AI provider (ollama, openai, groq, gemini)')
      .option('-m, --model <model>', 'Model to use')
      .action(async (options) => {
        await commandHandler.handleChat(options);
        process.exit(0);
      });

    // Agent mode - autonomous coding
    program
      .command('agent')
      .description('Start autonomous agent mode')
      .option('-p, --provider <provider>', 'AI provider to use')
      .option('-m, --model <model>', 'Model to use')
      .option('-t, --task <task>', 'Task description')
      .option('--auto', 'Enable fully autonomous mode')
      .action(async (options) => {
        await commandHandler.handleAgent(options);
      });

    // Configuration - simplified
    program
      .command('config')
      .description('Configure providers and settings')
      .action(async () => {
        await commandHandler.handleConfig();
      });

    // Model selection
    program
      .command('models')
      .description('Browse and select AI models')
      .action(async () => {
        await commandHandler.handleModelSelection();
      });

    // Guidelines command
    program
      .command('guidelines')
      .description('Manage user guidelines and project settings')
      .option('-s, --set', 'Set new guidelines')
      .option('-v, --view', 'View current guidelines')
      .option('-p, --project <path>', 'Set project-specific guidelines')
      .action(async (options) => {
        await commandHandler.handleGuidelines(options);
        process.exit(0);
      });

    // Project command
    program
      .command('project')
      .description('Manage projects')
      .option('-a, --add <path>', 'Add a new project')
      .option('-s, --set <path>', 'Set current project')
      .option('-l, --list', 'List recent projects')
      .option('-r, --remove <path>', 'Remove a project')
      .action(async (options) => {
        await commandHandler.handleProject(options);
        process.exit(0);
      });

    // NEW: Changes command - View current session changes
    program
      .command('changes')
      .description('View current session changes')
      .option('-s, --summary', 'Show detailed summary')
      .action(async (options) => {
        await commandHandler.handleChanges(options);
        process.exit(0);
      });

    // NEW: Revert command - Revert file changes
    program
      .command('revert [file]')
      .description('Revert changes to files')
      .option('-a, --all', 'Revert all changes in session')
      .action(async (file, options) => {
        if (file) {
          options.file = file;
        }
        await commandHandler.handleRevert(options);
        process.exit(0);
      });

    // NEW: Clear changes command - Clear change tracking session
    program
      .command('clear-changes')
      .description('Clear change tracking session (does not revert files)')
      .action(async () => {
        await commandHandler.handleClearChanges();
        process.exit(0);
      });

    // Intelligence status - Simple command to check how smart the agent is
    program
      .command('status')
      .description('Check agent intelligence and learning progress')
      .action(async () => {
        await commandHandler.handleIntelligence({ status: true });
        process.exit(0);
      });

    // Testing commands
    program
      .command('test')
      .description('Run comprehensive test suite')
      .option('-s, --suite <suite>', 'Run specific test suite')
      .option('-g, --generate', 'Generate test report')
      .option('-o, --output <path>', 'Output path for test report')
      .action(async (options) => {
        await commandHandler.handleTest(options);
        process.exit(0);
      });

    // Performance monitoring
    program
      .command('performance')
      .alias('perf')
      .description('Show performance metrics and cache statistics')
      .option('--stats', 'Show detailed performance statistics')
      .option('--clear', 'Clear performance cache')
      .action(async (options) => {
        await commandHandler.handlePerformance(options);
        process.exit(0);
      });

    // Documentation generation
    program
      .command('docs')
      .description('Generate and manage documentation')
      .option('-g, --generate', 'Generate full documentation')
      .option('-u, --update', 'Update existing documentation')
      .action(async (options) => {
        await commandHandler.handleDocumentation(options);
        process.exit(0);
      });

    // Health monitoring
    program
      .command('health')
      .description('Monitor codebase health and quality')
      .option('-r, --report', 'Generate health report')
      .option('-h, --history', 'Show health history')
      .option('-c, --clear', 'Clear health history')
      .action(async (options) => {
        await commandHandler.handleHealth(options);
        process.exit(0);
      });

    // Configuration management
    program
      .command('config-mgmt')
      .description('Advanced configuration management')
      .option('-v, --validate', 'Validate configuration')
      .option('-b, --backup', 'Backup configuration')
      .option('-r, --restore', 'Restore configuration')
      .option('-e, --export', 'Export configuration')
      .option('-i, --import', 'Import configuration')
      .action(async (options) => {
        await commandHandler.handleConfigManagement(options);
        process.exit(0);
      });

    // Context engine management
    program
      .command('context [action]')
      .description('Manage semantic context engine (status|test|configure|benchmark)')
      .action(async (action) => {
        await contextEngineCommand.handleContextCommand(action);
        process.exit(0);
      });

    // Web search command - Search the web and learn from results
    program
      .command('web <query>')
      .description('Search the web and learn from the results')
      .option('-n, --num <number>', 'Number of results to fetch', '3')
      .action(async (query, options) => {
        await commandHandler.handleWebSearch(query, options);
        process.exit(0);
      });

    // Self-training command - Enable autonomous learning
    program.addCommand(selfTrainCommand.createCommand());

    // Enhanced chat command with backend integration
    enhancedChatCommand.register(program);

    // Technology training command
    program
      .command('train-tech')
      .description('Train the agent on specific technologies')
      .option('-t, --technologies <techs>', 'Comma-separated list of technologies (php,html,css,javascript,laravel,flutter,python,vue)', 'php,html,css,javascript,laravel,flutter,python,vue')
      .option('-d, --depth <level>', 'Training depth (basic, intermediate, advanced, comprehensive)', 'intermediate')
      .option('-f, --focus <type>', 'Training focus (fundamentals, best-practices, latest-trends, all)', 'all')
      .option('--duration <minutes>', 'Training duration in minutes', '60')
      .option('--concurrent', 'Train on multiple technologies concurrently')
      .action(async (options) => {
        await commandHandler.handleTechnologyTraining(options);
      });

    // Parse arguments
    await program.parseAsync(process.argv);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.error(chalk.red('Fatal error:'), errorMessage);

    // Provide helpful context for common errors
    if (errorMessage.includes('ENOENT')) {
      console.error(chalk.yellow('Hint:'), 'Make sure you are in a valid project directory');
    } else if (errorMessage.includes('permission')) {
      console.error(chalk.yellow('Hint:'), 'Check file permissions or run with appropriate privileges');
    } else if (errorMessage.includes('network') || errorMessage.includes('timeout')) {
      console.error(chalk.yellow('Hint:'), 'Check your internet connection and AI provider settings');
    }

    process.exit(1);
  }
}

// Handle unhandled rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error(chalk.red('Unhandled Rejection at:'), promise, chalk.red('reason:'), reason);
  process.exit(1);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error(chalk.red('Uncaught Exception:'), error);
  process.exit(1);
});

if (require.main === module) {
  main();
}

export { main };
