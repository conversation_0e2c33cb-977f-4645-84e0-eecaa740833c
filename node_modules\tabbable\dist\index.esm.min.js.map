{"version": 3, "file": "index.esm.min.js", "sources": ["../src/index.js"], "sourcesContent": ["const candidateSelectors = [\n  'input',\n  'select',\n  'textarea',\n  'a[href]',\n  'button',\n  '[tabindex]:not(slot)',\n  'audio[controls]',\n  'video[controls]',\n  '[contenteditable]:not([contenteditable=\"false\"])',\n  'details>summary:first-of-type',\n  'details',\n];\nconst candidateSelector = /* #__PURE__ */ candidateSelectors.join(',');\n\nconst NoElement = typeof Element === 'undefined';\n\nconst matches = NoElement\n  ? function () {}\n  : Element.prototype.matches ||\n    Element.prototype.msMatchesSelector ||\n    Element.prototype.webkitMatchesSelector;\n\nconst getRootNode =\n  !NoElement && Element.prototype.getRootNode\n    ? (element) => element.getRootNode()\n    : (element) => element.ownerDocument;\n\n/**\n * @param {Element} el container to check in\n * @param {boolean} includeContainer add container to check\n * @param {(node: Element) => boolean} filter filter candidates\n * @returns {Element[]}\n */\nconst getCandidates = function (el, includeContainer, filter) {\n  let candidates = Array.prototype.slice.apply(\n    el.querySelectorAll(candidateSelector)\n  );\n  if (includeContainer && matches.call(el, candidateSelector)) {\n    candidates.unshift(el);\n  }\n  candidates = candidates.filter(filter);\n  return candidates;\n};\n\n/**\n * @callback GetShadowRoot\n * @param {Element} element to check for shadow root\n * @returns {ShadowRoot|boolean} ShadowRoot if available or boolean indicating if a shadowRoot is attached but not available.\n */\n\n/**\n * @callback ShadowRootFilter\n * @param {Element} shadowHostNode the element which contains shadow content\n * @returns {boolean} true if a shadow root could potentially contain valid candidates.\n */\n\n/**\n * @typedef {Object} CandidatesScope\n * @property {Element} scope contains inner candidates\n * @property {Element[]} candidates\n */\n\n/**\n * @typedef {Object} IterativeOptions\n * @property {GetShadowRoot|boolean} getShadowRoot true if shadow support is enabled; falsy if not;\n *  if a function, implies shadow support is enabled and either returns the shadow root of an element\n *  or a boolean stating if it has an undisclosed shadow root\n * @property {(node: Element) => boolean} filter filter candidates\n * @property {boolean} flatten if true then result will flatten any CandidatesScope into the returned list\n * @property {ShadowRootFilter} shadowRootFilter filter shadow roots;\n */\n\n/**\n * @param {Element[]} elements list of element containers to match candidates from\n * @param {boolean} includeContainer add container list to check\n * @param {IterativeOptions} options\n * @returns {Array.<Element|CandidatesScope>}\n */\nconst getCandidatesIteratively = function (\n  elements,\n  includeContainer,\n  options\n) {\n  const candidates = [];\n  const elementsToCheck = Array.from(elements);\n  while (elementsToCheck.length) {\n    const element = elementsToCheck.shift();\n    if (element.tagName === 'SLOT') {\n      // add shadow dom slot scope (slot itself cannot be focusable)\n      const assigned = element.assignedElements();\n      const content = assigned.length ? assigned : element.children;\n      const nestedCandidates = getCandidatesIteratively(content, true, options);\n      if (options.flatten) {\n        candidates.push(...nestedCandidates);\n      } else {\n        candidates.push({\n          scope: element,\n          candidates: nestedCandidates,\n        });\n      }\n    } else {\n      // check candidate element\n      const validCandidate = matches.call(element, candidateSelector);\n      if (\n        validCandidate &&\n        options.filter(element) &&\n        (includeContainer || !elements.includes(element))\n      ) {\n        candidates.push(element);\n      }\n\n      // iterate over shadow content if possible\n      const shadowRoot =\n        element.shadowRoot ||\n        // check for an undisclosed shadow\n        (typeof options.getShadowRoot === 'function' &&\n          options.getShadowRoot(element));\n\n      const validShadowRoot =\n        !options.shadowRootFilter || options.shadowRootFilter(element);\n\n      if (shadowRoot && validShadowRoot) {\n        // add shadow dom scope IIF a shadow root node was given; otherwise, an undisclosed\n        //  shadow exists, so look at light dom children as fallback BUT create a scope for any\n        //  child candidates found because they're likely slotted elements (elements that are\n        //  children of the web component element (which has the shadow), in the light dom, but\n        //  slotted somewhere _inside_ the undisclosed shadow) -- the scope is created below,\n        //  _after_ we return from this recursive call\n        const nestedCandidates = getCandidatesIteratively(\n          shadowRoot === true ? element.children : shadowRoot.children,\n          true,\n          options\n        );\n\n        if (options.flatten) {\n          candidates.push(...nestedCandidates);\n        } else {\n          candidates.push({\n            scope: element,\n            candidates: nestedCandidates,\n          });\n        }\n      } else {\n        // there's not shadow so just dig into the element's (light dom) children\n        //  __without__ giving the element special scope treatment\n        elementsToCheck.unshift(...element.children);\n      }\n    }\n  }\n  return candidates;\n};\n\nconst getTabindex = function (node, isScope) {\n  if (node.tabIndex < 0) {\n    // in Chrome, <details/>, <audio controls/> and <video controls/> elements get a default\n    // `tabIndex` of -1 when the 'tabindex' attribute isn't specified in the DOM,\n    // yet they are still part of the regular tab order; in FF, they get a default\n    // `tabIndex` of 0; since Chrome still puts those elements in the regular tab\n    // order, consider their tab index to be 0.\n    // Also browsers do not return `tabIndex` correctly for contentEditable nodes;\n    // so if they don't have a tabindex attribute specifically set, assume it's 0.\n    //\n    // isScope is positive for custom element with shadow root or slot that by default\n    // have tabIndex -1, but need to be sorted by document order in order for their\n    // content to be inserted in the correct position\n    if (\n      (isScope ||\n        /^(AUDIO|VIDEO|DETAILS)$/.test(node.tagName) ||\n        node.isContentEditable) &&\n      isNaN(parseInt(node.getAttribute('tabindex'), 10))\n    ) {\n      return 0;\n    }\n  }\n\n  return node.tabIndex;\n};\n\nconst sortOrderedTabbables = function (a, b) {\n  return a.tabIndex === b.tabIndex\n    ? a.documentOrder - b.documentOrder\n    : a.tabIndex - b.tabIndex;\n};\n\nconst isInput = function (node) {\n  return node.tagName === 'INPUT';\n};\n\nconst isHiddenInput = function (node) {\n  return isInput(node) && node.type === 'hidden';\n};\n\nconst isDetailsWithSummary = function (node) {\n  const r =\n    node.tagName === 'DETAILS' &&\n    Array.prototype.slice\n      .apply(node.children)\n      .some((child) => child.tagName === 'SUMMARY');\n  return r;\n};\n\nconst getCheckedRadio = function (nodes, form) {\n  for (let i = 0; i < nodes.length; i++) {\n    if (nodes[i].checked && nodes[i].form === form) {\n      return nodes[i];\n    }\n  }\n};\n\nconst isTabbableRadio = function (node) {\n  if (!node.name) {\n    return true;\n  }\n  const radioScope = node.form || getRootNode(node);\n  const queryRadios = function (name) {\n    return radioScope.querySelectorAll(\n      'input[type=\"radio\"][name=\"' + name + '\"]'\n    );\n  };\n\n  let radioSet;\n  if (\n    typeof window !== 'undefined' &&\n    typeof window.CSS !== 'undefined' &&\n    typeof window.CSS.escape === 'function'\n  ) {\n    radioSet = queryRadios(window.CSS.escape(node.name));\n  } else {\n    try {\n      radioSet = queryRadios(node.name);\n    } catch (err) {\n      // eslint-disable-next-line no-console\n      console.error(\n        'Looks like you have a radio button with a name attribute containing invalid CSS selector characters and need the CSS.escape polyfill: %s',\n        err.message\n      );\n      return false;\n    }\n  }\n\n  const checked = getCheckedRadio(radioSet, node.form);\n  return !checked || checked === node;\n};\n\nconst isRadio = function (node) {\n  return isInput(node) && node.type === 'radio';\n};\n\nconst isNonTabbableRadio = function (node) {\n  return isRadio(node) && !isTabbableRadio(node);\n};\n\nconst isZeroArea = function (node) {\n  const { width, height } = node.getBoundingClientRect();\n  return width === 0 && height === 0;\n};\nconst isHidden = function (node, { displayCheck, getShadowRoot }) {\n  // NOTE: visibility will be `undefined` if node is detached from the document\n  //  (see notes about this further down), which means we will consider it visible\n  //  (this is legacy behavior from a very long way back)\n  // NOTE: we check this regardless of `displayCheck=\"none\"` because this is a\n  //  _visibility_ check, not a _display_ check\n  if (getComputedStyle(node).visibility === 'hidden') {\n    return true;\n  }\n\n  const isDirectSummary = matches.call(node, 'details>summary:first-of-type');\n  const nodeUnderDetails = isDirectSummary ? node.parentElement : node;\n  if (matches.call(nodeUnderDetails, 'details:not([open]) *')) {\n    return true;\n  }\n\n  // The root node is the shadow root if the node is in a shadow DOM; some document otherwise\n  //  (but NOT _the_ document; see second 'If' comment below for more).\n  // If rootNode is shadow root, it'll have a host, which is the element to which the shadow\n  //  is attached, and the one we need to check if it's in the document or not (because the\n  //  shadow, and all nodes it contains, is never considered in the document since shadows\n  //  behave like self-contained DOMs; but if the shadow's HOST, which is part of the document,\n  //  is hidden, or is not in the document itself but is detached, it will affect the shadow's\n  //  visibility, including all the nodes it contains). The host could be any normal node,\n  //  or a custom element (i.e. web component). Either way, that's the one that is considered\n  //  part of the document, not the shadow root, nor any of its children (i.e. the node being\n  //  tested).\n  // If rootNode is not a shadow root, it won't have a host, and so rootNode should be the\n  //  document (per the docs) and while it's a Document-type object, that document does not\n  //  appear to be the same as the node's `ownerDocument` for some reason, so it's safer\n  //  to ignore the rootNode at this point, and use `node.ownerDocument`. Otherwise,\n  //  using `rootNode.contains(node)` will _always_ be true we'll get false-positives when\n  //  node is actually detached.\n  const nodeRootHost = getRootNode(node).host;\n  const nodeIsAttached =\n    nodeRootHost?.ownerDocument.contains(nodeRootHost) ||\n    node.ownerDocument.contains(node);\n\n  if (!displayCheck || displayCheck === 'full') {\n    if (typeof getShadowRoot === 'function') {\n      // figure out if we should consider the node to be in an undisclosed shadow and use the\n      //  'non-zero-area' fallback\n      const originalNode = node;\n      while (node) {\n        const parentElement = node.parentElement;\n        const rootNode = getRootNode(node);\n        if (\n          parentElement &&\n          !parentElement.shadowRoot &&\n          getShadowRoot(parentElement) === true // check if there's an undisclosed shadow\n        ) {\n          // node has an undisclosed shadow which means we can only treat it as a black box, so we\n          //  fall back to a non-zero-area test\n          return isZeroArea(node);\n        } else if (node.assignedSlot) {\n          // iterate up slot\n          node = node.assignedSlot;\n        } else if (!parentElement && rootNode !== node.ownerDocument) {\n          // cross shadow boundary\n          node = rootNode.host;\n        } else {\n          // iterate up normal dom\n          node = parentElement;\n        }\n      }\n\n      node = originalNode;\n    }\n    // else, `getShadowRoot` might be true, but all that does is enable shadow DOM support\n    //  (i.e. it does not also presume that all nodes might have undisclosed shadows); or\n    //  it might be a falsy value, which means shadow DOM support is disabled\n\n    // Since we didn't find it sitting in an undisclosed shadow (or shadows are disabled)\n    //  now we can just test to see if it would normally be visible or not, provided it's\n    //  attached to the main document.\n    // NOTE: We must consider case where node is inside a shadow DOM and given directly to\n    //  `isTabbable()` or `isFocusable()` -- regardless of `getShadowRoot` option setting.\n\n    if (nodeIsAttached) {\n      // this works wherever the node is: if there's at least one client rect, it's\n      //  somehow displayed; it also covers the CSS 'display: contents' case where the\n      //  node itself is hidden in place of its contents; and there's no need to search\n      //  up the hierarchy either\n      return !node.getClientRects().length;\n    }\n\n    // Else, the node isn't attached to the document, which means the `getClientRects()`\n    //  API will __always__ return zero rects (this can happen, for example, if React\n    //  is used to render nodes onto a detached tree, as confirmed in this thread:\n    //  https://github.com/facebook/react/issues/9117#issuecomment-284228870)\n    //\n    // It also means that even window.getComputedStyle(node).display will return `undefined`\n    //  because styles are only computed for nodes that are in the document.\n    //\n    // NOTE: THIS HAS BEEN THE CASE FOR YEARS. It is not new, nor is it caused by tabbable\n    //  somehow. Though it was never stated officially, anyone who has ever used tabbable\n    //  APIs on nodes in detached containers has actually implicitly used tabbable in what\n    //  was later (as of v5.2.0 on Apr 9, 2021) called `displayCheck=\"none\"` mode -- essentially\n    //  considering __everything__ to be visible because of the innability to determine styles.\n  } else if (displayCheck === 'non-zero-area') {\n    // NOTE: Even though this tests that the node's client rect is non-zero to determine\n    //  whether it's displayed, and that a detached node will __always__ have a zero-area\n    //  client rect, we don't special-case for whether the node is attached or not. In\n    //  this mode, we do want to consider nodes that have a zero area to be hidden at all\n    //  times, and that includes attached or not.\n    return isZeroArea(node);\n  }\n\n  // visible, as far as we can tell, or per current `displayCheck` mode\n  return false;\n};\n\n// form fields (nested) inside a disabled fieldset are not focusable/tabbable\n//  unless they are in the _first_ <legend> element of the top-most disabled\n//  fieldset\nconst isDisabledFromFieldset = function (node) {\n  if (/^(INPUT|BUTTON|SELECT|TEXTAREA)$/.test(node.tagName)) {\n    let parentNode = node.parentElement;\n    // check if `node` is contained in a disabled <fieldset>\n    while (parentNode) {\n      if (parentNode.tagName === 'FIELDSET' && parentNode.disabled) {\n        // look for the first <legend> among the children of the disabled <fieldset>\n        for (let i = 0; i < parentNode.children.length; i++) {\n          const child = parentNode.children.item(i);\n          // when the first <legend> (in document order) is found\n          if (child.tagName === 'LEGEND') {\n            // if its parent <fieldset> is not nested in another disabled <fieldset>,\n            // return whether `node` is a descendant of its first <legend>\n            return matches.call(parentNode, 'fieldset[disabled] *')\n              ? true\n              : !child.contains(node);\n          }\n        }\n        // the disabled <fieldset> containing `node` has no <legend>\n        return true;\n      }\n      parentNode = parentNode.parentElement;\n    }\n  }\n\n  // else, node's tabbable/focusable state should not be affected by a fieldset's\n  //  enabled/disabled state\n  return false;\n};\n\nconst isNodeMatchingSelectorFocusable = function (options, node) {\n  if (\n    node.disabled ||\n    isHiddenInput(node) ||\n    isHidden(node, options) ||\n    // For a details element with a summary, the summary element gets the focus\n    isDetailsWithSummary(node) ||\n    isDisabledFromFieldset(node)\n  ) {\n    return false;\n  }\n  return true;\n};\n\nconst isNodeMatchingSelectorTabbable = function (options, node) {\n  if (\n    isNonTabbableRadio(node) ||\n    getTabindex(node) < 0 ||\n    !isNodeMatchingSelectorFocusable(options, node)\n  ) {\n    return false;\n  }\n  return true;\n};\n\nconst isValidShadowRootTabbable = function (shadowHostNode) {\n  const tabIndex = parseInt(shadowHostNode.getAttribute('tabindex'), 10);\n  if (isNaN(tabIndex) || tabIndex >= 0) {\n    return true;\n  }\n  // If a custom element has an explicit negative tabindex,\n  // browsers will not allow tab targeting said element's children.\n  return false;\n};\n\n/**\n * @param {Array.<Element|CandidatesScope>} candidates\n * @returns Element[]\n */\nconst sortByOrder = function (candidates) {\n  const regularTabbables = [];\n  const orderedTabbables = [];\n  candidates.forEach(function (item, i) {\n    const isScope = !!item.scope;\n    const element = isScope ? item.scope : item;\n    const candidateTabindex = getTabindex(element, isScope);\n    const elements = isScope ? sortByOrder(item.candidates) : element;\n    if (candidateTabindex === 0) {\n      isScope\n        ? regularTabbables.push(...elements)\n        : regularTabbables.push(element);\n    } else {\n      orderedTabbables.push({\n        documentOrder: i,\n        tabIndex: candidateTabindex,\n        item: item,\n        isScope: isScope,\n        content: elements,\n      });\n    }\n  });\n\n  return orderedTabbables\n    .sort(sortOrderedTabbables)\n    .reduce((acc, sortable) => {\n      sortable.isScope\n        ? acc.push(...sortable.content)\n        : acc.push(sortable.content);\n      return acc;\n    }, [])\n    .concat(regularTabbables);\n};\n\nconst tabbable = function (el, options) {\n  options = options || {};\n\n  let candidates;\n  if (options.getShadowRoot) {\n    candidates = getCandidatesIteratively([el], options.includeContainer, {\n      filter: isNodeMatchingSelectorTabbable.bind(null, options),\n      flatten: false,\n      getShadowRoot: options.getShadowRoot,\n      shadowRootFilter: isValidShadowRootTabbable,\n    });\n  } else {\n    candidates = getCandidates(\n      el,\n      options.includeContainer,\n      isNodeMatchingSelectorTabbable.bind(null, options)\n    );\n  }\n  return sortByOrder(candidates);\n};\n\nconst focusable = function (el, options) {\n  options = options || {};\n\n  let candidates;\n  if (options.getShadowRoot) {\n    candidates = getCandidatesIteratively([el], options.includeContainer, {\n      filter: isNodeMatchingSelectorFocusable.bind(null, options),\n      flatten: true,\n      getShadowRoot: options.getShadowRoot,\n    });\n  } else {\n    candidates = getCandidates(\n      el,\n      options.includeContainer,\n      isNodeMatchingSelectorFocusable.bind(null, options)\n    );\n  }\n\n  return candidates;\n};\n\nconst isTabbable = function (node, options) {\n  options = options || {};\n  if (!node) {\n    throw new Error('No node provided');\n  }\n  if (matches.call(node, candidateSelector) === false) {\n    return false;\n  }\n  return isNodeMatchingSelectorTabbable(options, node);\n};\n\nconst focusableCandidateSelector = /* #__PURE__ */ candidateSelectors\n  .concat('iframe')\n  .join(',');\n\nconst isFocusable = function (node, options) {\n  options = options || {};\n  if (!node) {\n    throw new Error('No node provided');\n  }\n  if (matches.call(node, focusableCandidateSelector) === false) {\n    return false;\n  }\n  return isNodeMatchingSelectorFocusable(options, node);\n};\n\nexport { tabbable, focusable, isTabbable, isFocusable };\n"], "names": ["candidateSelectors", "candidate<PERSON><PERSON><PERSON>", "join", "NoElement", "Element", "matches", "prototype", "msMatchesSelector", "webkitMatchesSelector", "getRootNode", "element", "ownerDocument", "getCandidates", "el", "<PERSON><PERSON><PERSON><PERSON>", "filter", "candidates", "Array", "slice", "apply", "querySelectorAll", "call", "unshift", "getCandidatesIteratively", "elements", "options", "elementsToCheck", "from", "length", "shift", "tagName", "assigned", "assignedElements", "nestedCandidates", "children", "flatten", "push", "scope", "includes", "shadowRoot", "getShadowRoot", "validShadowRoot", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getTabindex", "node", "isScope", "tabIndex", "test", "isContentEditable", "isNaN", "parseInt", "getAttribute", "sortOrderedTabbables", "a", "b", "documentOrder", "isInput", "isNonTabbableRadio", "type", "isRadio", "name", "radioSet", "radioScope", "form", "queryRadios", "window", "CSS", "escape", "err", "console", "error", "message", "checked", "nodes", "i", "getCheckedRadio", "isTabbableRadio", "isZeroArea", "getBoundingClientRect", "width", "height", "isNodeMatchingSelectorFocusable", "disabled", "isHiddenInput", "_ref", "displayCheck", "getComputedStyle", "visibility", "nodeUnderDetails", "parentElement", "nodeRootHost", "host", "nodeIsAttached", "contains", "originalNode", "rootNode", "assignedSlot", "getClientRects", "isHidden", "some", "child", "isDetailsWithSummary", "parentNode", "item", "isDisabledFromFieldset", "isNodeMatchingSelectorTabbable", "isValidShadowRootTabbable", "shadowHostNode", "tabbable", "sortByOrder", "regularTabbables", "orderedTabbables", "for<PERSON>ach", "candidateTabindex", "content", "sort", "reduce", "acc", "sortable", "concat", "bind", "focusable", "isTabbable", "Error", "focusableCandidateSelector", "isFocusable"], "mappings": ";;;;AAAA,IAAMA,EAAqB,CACzB,QACA,SACA,WACA,UACA,SACA,uBACA,kBACA,kBACA,mDACA,gCACA,WAEIC,EAAoCD,EAAmBE,KAAK,KAE5DC,EAA+B,oBAAZC,QAEnBC,EAAUF,EACZ,aACAC,QAAQE,UAAUD,SAClBD,QAAQE,UAAUC,mBAClBH,QAAQE,UAAUE,sBAEhBC,GACHN,GAAaC,QAAQE,UAAUG,YAC5B,SAACC,GAAD,OAAaA,EAAQD,eACrB,SAACC,GAAD,OAAaA,EAAQC,eAQrBC,EAAgB,SAAUC,EAAIC,EAAkBC,GACpD,IAAIC,EAAaC,MAAMX,UAAUY,MAAMC,MACrCN,EAAGO,iBAAiBnB,IAMtB,OAJIa,GAAoBT,EAAQgB,KAAKR,EAAIZ,IACvCe,EAAWM,QAAQT,GAErBG,EAAaA,EAAWD,OAAOA,IAsC3BQ,EAA2B,SAA3BA,EACJC,EACAV,EACAW,GAIA,IAFA,IAAMT,EAAa,GACbU,EAAkBT,MAAMU,KAAKH,GAC5BE,EAAgBE,QAAQ,CAC7B,IAAMlB,EAAUgB,EAAgBG,QAChC,GAAwB,SAApBnB,EAAQoB,QAAoB,CAE9B,IAAMC,EAAWrB,EAAQsB,mBAEnBC,EAAmBV,EADTQ,EAASH,OAASG,EAAWrB,EAAQwB,UACM,EAAMT,GAC7DA,EAAQU,QACVnB,EAAWoB,KAAXjB,MAAAH,EAAmBiB,GAEnBjB,EAAWoB,KAAK,CACdC,MAAO3B,EACPM,WAAYiB,QAGX,CAEkB5B,EAAQgB,KAAKX,EAAST,IAG3CwB,EAAQV,OAAOL,KACdI,IAAqBU,EAASc,SAAS5B,KAExCM,EAAWoB,KAAK1B,GAIlB,IAAM6B,EACJ7B,EAAQ6B,YAE0B,mBAA1Bd,EAAQe,eACdf,EAAQe,cAAc9B,GAEpB+B,GACHhB,EAAQiB,kBAAoBjB,EAAQiB,iBAAiBhC,GAExD,GAAI6B,GAAcE,EAAiB,CAOjC,IAAMR,EAAmBV,GACR,IAAfgB,EAAsB7B,EAAQwB,SAAWK,EAAWL,UACpD,EACAT,GAGEA,EAAQU,QACVnB,EAAWoB,KAAXjB,MAAAH,EAAmBiB,GAEnBjB,EAAWoB,KAAK,CACdC,MAAO3B,EACPM,WAAYiB,SAMhBP,EAAgBJ,QAAhBI,MAAAA,EAA2BhB,EAAQwB,WAIzC,OAAOlB,GAGH2B,EAAc,SAAUC,EAAMC,GAClC,OAAID,EAAKE,SAAW,IAafD,GACC,0BAA0BE,KAAKH,EAAKd,UACpCc,EAAKI,oBACPC,MAAMC,SAASN,EAAKO,aAAa,YAAa,KAEvC,EAIJP,EAAKE,UAGRM,EAAuB,SAAUC,EAAGC,GACxC,OAAOD,EAAEP,WAAaQ,EAAER,SACpBO,EAAEE,cAAgBD,EAAEC,cACpBF,EAAEP,SAAWQ,EAAER,UAGfU,EAAU,SAAUZ,GACxB,MAAwB,UAAjBA,EAAKd,SA+DR2B,EAAqB,SAAUb,GACnC,OALc,SAAUA,GACxB,OAAOY,EAAQZ,IAAuB,UAAdA,EAAKc,KAItBC,CAAQf,KAxCO,SAAUA,GAChC,IAAKA,EAAKgB,KACR,OAAO,EAET,IAOIC,EAPEC,EAAalB,EAAKmB,MAAQtD,EAAYmC,GACtCoB,EAAc,SAAUJ,GAC5B,OAAOE,EAAW1C,iBAChB,6BAA+BwC,EAAO,OAK1C,GACoB,oBAAXK,aACe,IAAfA,OAAOC,KACe,mBAAtBD,OAAOC,IAAIC,OAElBN,EAAWG,EAAYC,OAAOC,IAAIC,OAAOvB,EAAKgB,YAE9C,IACEC,EAAWG,EAAYpB,EAAKgB,MAC5B,MAAOQ,GAMP,OAJAC,QAAQC,MACN,2IACAF,EAAIG,UAEC,EAIX,IAAMC,EAvCgB,SAAUC,EAAOV,GACvC,IAAK,IAAIW,EAAI,EAAGA,EAAID,EAAM7C,OAAQ8C,IAChC,GAAID,EAAMC,GAAGF,SAAWC,EAAMC,GAAGX,OAASA,EACxC,OAAOU,EAAMC,GAoCDC,CAAgBd,EAAUjB,EAAKmB,MAC/C,OAAQS,GAAWA,IAAY5B,EAQNgC,CAAgBhC,IAGrCiC,EAAa,SAAUjC,GAC3B,IAA0BA,EAAAA,EAAKkC,wBAAvBC,IAAAA,MAAOC,IAAAA,OACf,OAAiB,IAAVD,GAA0B,IAAXC,GAmJlBC,EAAkC,SAAUxD,EAASmB,GACzD,QACEA,EAAKsC,UAvNa,SAAUtC,GAC9B,OAAOY,EAAQZ,IAAuB,WAAdA,EAAKc,KAuN3ByB,CAAcvC,IApJD,SAAUA,EAAuCwC,GAAA,IAA/BC,IAAAA,aAAc7C,IAAAA,cAM/C,GAA0C,WAAtC8C,iBAAiB1C,GAAM2C,WACzB,OAAO,EAGT,IACMC,EADkBnF,EAAQgB,KAAKuB,EAAM,iCACAA,EAAK6C,cAAgB7C,EAChE,GAAIvC,EAAQgB,KAAKmE,EAAkB,yBACjC,OAAO,EAoBT,IAAME,EAAejF,EAAYmC,GAAM+C,KACjCC,GACJF,MAAAA,OAAAA,EAAAA,EAAc/E,cAAckF,SAASH,KACrC9C,EAAKjC,cAAckF,SAASjD,GAE9B,GAAKyC,GAAiC,SAAjBA,GA6Dd,GAAqB,kBAAjBA,EAMT,OAAOR,EAAWjC,OAnE0B,CAC5C,GAA6B,mBAAlBJ,EAA8B,CAIvC,IADA,IAAMsD,EAAelD,EACdA,GAAM,CACX,IAAM6C,EAAgB7C,EAAK6C,cACrBM,EAAWtF,EAAYmC,GAC7B,GACE6C,IACCA,EAAclD,aACkB,IAAjCC,EAAciD,GAId,OAAOZ,EAAWjC,GAGlBA,EAFSA,EAAKoD,aAEPpD,EAAKoD,aACFP,GAAiBM,IAAanD,EAAKjC,cAKtC8E,EAHAM,EAASJ,KAOpB/C,EAAOkD,EAYT,GAAIF,EAKF,OAAQhD,EAAKqD,iBAAiBrE,OA0BlC,OAAO,EAwCLsE,CAAStD,EAAMnB,IArNU,SAAUmB,GAMrC,MAJmB,YAAjBA,EAAKd,SACLb,MAAMX,UAAUY,MACbC,MAAMyB,EAAKV,UACXiE,MAAK,SAACC,GAAD,MAA6B,YAAlBA,EAAMtE,WAkNzBuE,CAAqBzD,IApCM,SAAUA,GACvC,GAAI,mCAAmCG,KAAKH,EAAKd,SAG/C,IAFA,IAAIwE,EAAa1D,EAAK6C,cAEfa,GAAY,CACjB,GAA2B,aAAvBA,EAAWxE,SAA0BwE,EAAWpB,SAAU,CAE5D,IAAK,IAAIR,EAAI,EAAGA,EAAI4B,EAAWpE,SAASN,OAAQ8C,IAAK,CACnD,IAAM0B,EAAQE,EAAWpE,SAASqE,KAAK7B,GAEvC,GAAsB,WAAlB0B,EAAMtE,QAGR,QAAOzB,EAAQgB,KAAKiF,EAAY,0BAE3BF,EAAMP,SAASjD,GAIxB,OAAO,EAET0D,EAAaA,EAAWb,cAM5B,OAAO,EAULe,CAAuB5D,KAOrB6D,EAAiC,SAAUhF,EAASmB,GACxD,QACEa,EAAmBb,IACnBD,EAAYC,GAAQ,IACnBqC,EAAgCxD,EAASmB,KAOxC8D,EAA4B,SAAUC,GAC1C,IAAM7D,EAAWI,SAASyD,EAAexD,aAAa,YAAa,IACnE,SAAIF,MAAMH,IAAaA,GAAY,IA8C/B8D,EAAW,SAAU/F,EAAIY,GAkB7B,OApDkB,SAAdoF,EAAwB7F,GAC5B,IAAM8F,EAAmB,GACnBC,EAAmB,GAqBzB,OApBA/F,EAAWgG,SAAQ,SAAUT,EAAM7B,GACjC,IAAM7B,IAAY0D,EAAKlE,MACjB3B,EAAUmC,EAAU0D,EAAKlE,MAAQkE,EACjCU,EAAoBtE,EAAYjC,EAASmC,GACzCrB,EAAWqB,EAAUgE,EAAYN,EAAKvF,YAAcN,EAChC,IAAtBuG,EACFpE,EACIiE,EAAiB1E,WAAjB0E,EAAyBtF,GACzBsF,EAAiB1E,KAAK1B,GAE1BqG,EAAiB3E,KAAK,CACpBmB,cAAemB,EACf5B,SAAUmE,EACVV,KAAMA,EACN1D,QAASA,EACTqE,QAAS1F,OAKRuF,EACJI,KAAK/D,GACLgE,QAAO,SAACC,EAAKC,GAIZ,OAHAA,EAASzE,QACLwE,EAAIjF,KAAJjB,MAAAkG,EAAYC,EAASJ,SACrBG,EAAIjF,KAAKkF,EAASJ,SACfG,IACN,IACFE,OAAOT,GAqBHD,EAjBPpF,EAAUA,GAAW,IAGTe,cACGjB,EAAyB,CAACV,GAAKY,EAAQX,iBAAkB,CACpEC,OAAQ0F,EAA+Be,KAAK,KAAM/F,GAClDU,SAAS,EACTK,cAAef,EAAQe,cACvBE,iBAAkBgE,IAGP9F,EACXC,EACAY,EAAQX,iBACR2F,EAA+Be,KAAK,KAAM/F,MAM1CgG,EAAY,SAAU5G,EAAIY,GAkB9B,OAjBAA,EAAUA,GAAW,IAGTe,cACGjB,EAAyB,CAACV,GAAKY,EAAQX,iBAAkB,CACpEC,OAAQkE,EAAgCuC,KAAK,KAAM/F,GACnDU,SAAS,EACTK,cAAef,EAAQe,gBAGZ5B,EACXC,EACAY,EAAQX,iBACRmE,EAAgCuC,KAAK,KAAM/F,KAO3CiG,EAAa,SAAU9E,EAAMnB,GAEjC,GADAA,EAAUA,GAAW,IAChBmB,EACH,MAAM,IAAI+E,MAAM,oBAElB,OAA8C,IAA1CtH,EAAQgB,KAAKuB,EAAM3C,IAGhBwG,EAA+BhF,EAASmB,IAG3CgF,EAA6C5H,EAChDuH,OAAO,UACPrH,KAAK,KAEF2H,EAAc,SAAUjF,EAAMnB,GAElC,GADAA,EAAUA,GAAW,IAChBmB,EACH,MAAM,IAAI+E,MAAM,oBAElB,OAAuD,IAAnDtH,EAAQgB,KAAKuB,EAAMgF,IAGhB3C,EAAgCxD,EAASmB"}