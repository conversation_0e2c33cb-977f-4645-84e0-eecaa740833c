{"name": "tabbable", "version": "5.3.3", "description": "Returns an array of all tabbable DOM nodes within a containing node.", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "index.d.ts", "sideEffects": false, "files": ["package.json", "dist", "src", "index.d.ts", "README.md", "CHANGELOG.md", "SECURITY.md", "LICENSE"], "scripts": {"build": "yarn clean && yarn compile", "clean": "rm -rf ./dist", "compile:esm": "cross-env BUILD_ENV=esm BABEL_ENV=esm rollup -c", "compile:cjs": "cross-env BUILD_ENV=cjs BABEL_ENV=es5 rollup -c", "compile:umd": "cross-env BUILD_ENV=umd BABEL_ENV=es5 rollup -c", "compile": "yarn compile:esm && yarn compile:cjs && yarn compile:umd", "format": "prettier --write \"{*,src/**/*,test/**/*,.github/workflows/*}.+(js|yml)\"", "format:check": "prettier --check \"{*,src/**/*,test/**/*,.github/workflows/*}.+(js|yml)\"", "format:watch": "onchange \"{*,src/**/*,test/**/*,.github/workflows/*}.+(js|yml)\" -- prettier --write {{changed}}", "lint": "eslint \"*.js\" \"src/**/*.js\" \"test/**/*.js\"", "start": "yarn compile:cjs && budo -l -d -o test/debug.js -- -t brfs", "test": "yarn format:check && yarn lint && yarn test:types && yarn test:unit && yarn test:e2e", "test:types": "tsc index.d.ts", "test:unit": "jest", "test:e2e": "ELECTRON_ENABLE_LOGGING=1 cypress run", "test:e2e:dev": "cypress open", "test:coverage": "cypress run --env coverage=true", "prepare": "yarn build", "prepublishOnly": "yarn test && yarn build", "release": "yarn build && changeset publish"}, "repository": {"type": "git", "url": "git+https://github.com/focus-trap/tabbable.git"}, "author": {"name": "<PERSON>", "url": "http://davidtheclark.com/"}, "license": "MIT", "bugs": {"url": "https://github.com/focus-trap/tabbable/issues"}, "homepage": "https://github.com/focus-trap/tabbable#readme", "devDependencies": {"@babel/core": "^7.18.2", "@babel/eslint-parser": "^7.17.0", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.17.12", "@babel/plugin-proposal-optional-chaining": "^7.16.0", "@babel/preset-env": "^7.18.0", "@changesets/cli": "^2.22.0", "@cypress/code-coverage": "^3.9.12", "@rollup/plugin-babel": "^5.3.1", "@rollup/plugin-commonjs": "^22.0.0", "@rollup/plugin-node-resolve": "^13.3.0", "@testing-library/dom": "^8.13.0", "@testing-library/jest-dom": "^5.16.4", "@types/node": "^17.0.35", "all-contributors-cli": "^6.20.0", "babel-jest": "^28.1.0", "brfs": "^2.0.2", "browserify": "^17.0.0", "budo": "^11.7.0", "cross-env": "^7.0.3", "cypress": "^9.7.0", "eslint": "^8.16.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-cypress": "^2.12.1", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jest": "^26.2.2", "jest": "^28.1.0", "jest-watch-typeahead": "^1.1.0", "onchange": "^7.1.0", "prettier": "^2.6.2", "rollup": "^2.74.1", "rollup-plugin-sourcemaps": "^0.6.3", "rollup-plugin-terser": "^7.0.2", "typescript": "^4.7.2", "watchify": "^4.0.0"}}