import axios, { AxiosInstance } from 'axios';
import { AIProvider, ChatMessage, ChatResponse } from '../AIProviderManager';
import { AIProviderConfig } from '../../config/ConfigManager';
import { Logger } from '../../utils/Logger';

export class OpenAIProvider implements AIProvider {
  public readonly name = 'OpenAI';
  private client: AxiosInstance;
  private config: AIProviderConfig;
  private logger: Logger;

  constructor(config: AIProviderConfig, logger: Logger) {
    this.config = config;
    this.logger = logger;
    this.client = axios.create({
      baseURL: config.baseUrl || 'https://api.openai.com/v1',
      timeout: 120000, // Increased to 2 minutes
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${config.apiKey}`,
      },
    });
  }

  public async isAvailable(): Promise<boolean> {
    try {
      const response = await this.client.get('/models');
      return response.status === 200;
    } catch (error) {
      this.logger.debug(`OpenAI availability check failed: ${error}`);
      return false;
    }
  }

  public async chat(messages: ChatMessage[], model?: string): Promise<ChatResponse> {
    try {
      const modelName = model || this.config.defaultModel || 'gpt-4';
      
      const response = await this.client.post('/chat/completions', {
        model: modelName,
        messages: messages,
        temperature: 0.7,
        max_tokens: 4000,
      });

      if (response.data && response.data.choices && response.data.choices.length > 0) {
        const choice = response.data.choices[0];
        return {
          content: choice.message.content,
          usage: {
            promptTokens: response.data.usage?.prompt_tokens || 0,
            completionTokens: response.data.usage?.completion_tokens || 0,
            totalTokens: response.data.usage?.total_tokens || 0
          }
        };
      }

      throw new Error('Invalid response from OpenAI');
    } catch (error) {
      if (axios.isAxiosError(error)) {
        if (error.response?.status === 401) {
          throw new Error('Invalid OpenAI API key');
        }
        if (error.response?.status === 404) {
          throw new Error(`Model '${model || this.config.defaultModel}' not found`);
        }
        throw new Error(`OpenAI API error: ${error.response?.data?.error?.message || error.message}`);
      }
      throw error;
    }
  }

  public async listModels(): Promise<string[]> {
    try {
      const response = await this.client.get('/models');
      
      if (response.data && response.data.data) {
        return response.data.data
          .filter((model: any) => model.id.includes('gpt'))
          .map((model: any) => model.id)
          .sort();
      }
      
      return [];
    } catch (error) {
      this.logger.error(`Failed to list OpenAI models: ${error}`);
      return [];
    }
  }
}
