import fs from 'fs';
import path from 'path';
import os from 'os';
import { Logger } from '../utils/Logger';
import { AgentAction } from './AgentCore';

export interface Experience {
  id: string;
  timestamp: Date;
  task: string;
  action: AgentAction | null;
  outcome: 'success' | 'failure';
  error?: string;
  reflection?: string;
  context?: string;
  projectPath?: string;
  filesMentioned?: string[];
  userGuidelines?: string[];
}

export interface LearningPattern {
  pattern: string;
  frequency: number;
  successRate: number;
  examples: string[];
}

export class MemoryManager {
  private logger: Logger;
  private memoryPath: string;
  private experiences: Experience[] = [];
  private patterns: Map<string, LearningPattern> = new Map();

  constructor(logger: Logger) {
    this.logger = logger;
    this.memoryPath = path.join(os.homedir(), '.goc-agent', 'memory.json');
    this.loadMemory();
  }

  public addExperience(
    task: string,
    action: AgentAction | null,
    outcome: 'success' | 'failure',
    error?: string,
    projectPath?: string,
    filesMentioned?: string[],
    userGuidelines?: string[]
  ): void {
    const experience: Experience = {
      id: this.generateId(),
      timestamp: new Date(),
      task,
      action,
      outcome,
      error,
      projectPath,
      filesMentioned,
      userGuidelines
    };

    this.experiences.push(experience);
    this.updatePatterns(experience);
    this.saveMemory();

    this.logger.debug(`💾 Stored experience: ${task} -> ${outcome}`);
  }

  public addReflection(action: AgentAction, reflection: string): void {
    const lastExperience = this.experiences[this.experiences.length - 1];
    if (lastExperience && lastExperience.action?.type === action.type) {
      lastExperience.reflection = reflection;
      this.saveMemory();
    }
  }

  public getRelevantExperiences(task: string, limit: number = 5, projectPath?: string): string {
    let relevant = this.experiences
      .filter(exp => this.calculateSimilarity(exp.task, task) > 0.3);

    // Prioritize experiences from the same project
    if (projectPath) {
      relevant = relevant.sort((a, b) => {
        const aProjectMatch = a.projectPath === projectPath ? 1 : 0;
        const bProjectMatch = b.projectPath === projectPath ? 1 : 0;
        if (aProjectMatch !== bProjectMatch) {
          return bProjectMatch - aProjectMatch;
        }
        const timeA = a.timestamp instanceof Date ? a.timestamp.getTime() : new Date(a.timestamp).getTime();
        const timeB = b.timestamp instanceof Date ? b.timestamp.getTime() : new Date(b.timestamp).getTime();
        return timeB - timeA;
      });
    } else {
      relevant = relevant.sort((a, b) => {
        const timeA = a.timestamp instanceof Date ? a.timestamp.getTime() : new Date(a.timestamp).getTime();
        const timeB = b.timestamp instanceof Date ? b.timestamp.getTime() : new Date(b.timestamp).getTime();
        return timeB - timeA;
      });
    }

    relevant = relevant.slice(0, limit);

    if (relevant.length === 0) {
      return 'No relevant past experiences found.';
    }

    return relevant.map(exp =>
      `Task: ${exp.task}\nAction: ${exp.action?.type || 'none'}\nOutcome: ${exp.outcome}\n${exp.reflection ? `Reflection: ${exp.reflection}` : ''}${exp.filesMentioned ? `\nFiles: ${exp.filesMentioned.join(', ')}` : ''}`
    ).join('\n\n');
  }

  public getFileExperiences(filePath: string, limit: number = 3): string {
    const fileExperiences = this.experiences
      .filter(exp => exp.filesMentioned?.some(file => file.includes(filePath) || filePath.includes(file)))
      .sort((a, b) => {
        const timeA = a.timestamp instanceof Date ? a.timestamp.getTime() : new Date(a.timestamp).getTime();
        const timeB = b.timestamp instanceof Date ? b.timestamp.getTime() : new Date(b.timestamp).getTime();
        return timeB - timeA;
      })
      .slice(0, limit);

    if (fileExperiences.length === 0) {
      return `No past experiences found for file: ${filePath}`;
    }

    return `Past experiences with ${filePath}:\n\n` + fileExperiences.map(exp =>
      `${exp.task} -> ${exp.outcome}${exp.reflection ? ` (${exp.reflection})` : ''}`
    ).join('\n');
  }

  public getProjectExperiences(projectPath: string, limit: number = 10): Experience[] {
    return this.experiences
      .filter(exp => exp.projectPath === projectPath)
      .sort((a, b) => {
        const timeA = a.timestamp instanceof Date ? a.timestamp.getTime() : new Date(a.timestamp).getTime();
        const timeB = b.timestamp instanceof Date ? b.timestamp.getTime() : new Date(b.timestamp).getTime();
        return timeB - timeA;
      })
      .slice(0, limit);
  }

  public getSuccessPatterns(): LearningPattern[] {
    return Array.from(this.patterns.values())
      .filter(pattern => pattern.successRate > 0.7)
      .sort((a, b) => b.successRate - a.successRate);
  }

  public getFailurePatterns(): LearningPattern[] {
    return Array.from(this.patterns.values())
      .filter(pattern => pattern.successRate < 0.3)
      .sort((a, b) => a.successRate - b.successRate);
  }

  public analyzePerformance(): {
    totalExperiences: number;
    successRate: number;
    commonFailures: string[];
    improvementSuggestions: string[];
  } {
    const total = this.experiences.length;
    const successes = this.experiences.filter(exp => exp.outcome === 'success').length;
    const successRate = total > 0 ? successes / total : 0;

    const failures = this.experiences.filter(exp => exp.outcome === 'failure');
    const commonFailures = this.extractCommonFailures(failures);

    return {
      totalExperiences: total,
      successRate,
      commonFailures,
      improvementSuggestions: this.generateImprovementSuggestions(commonFailures)
    };
  }

  public trainFromExperiences(): void {
    this.logger.info('🧠 Training from past experiences...');

    // Analyze patterns
    this.analyzeTaskPatterns();
    this.analyzeActionEffectiveness();
    this.identifyOptimalStrategies();

    this.logger.success(`✅ Training completed. Analyzed ${this.experiences.length} experiences.`);
  }

  public quickTrainFromExperiences(): void {
    // Lightweight training for continuous learning
    const recentExperiences = this.getRecentExperiences(10);

    for (const experience of recentExperiences) {
      this.updatePatterns(experience);
    }

    // Quick pattern analysis without heavy computation
    this.quickAnalyzePatterns();
  }

  public getAdvancedPatterns(): {
    sequentialPatterns: string[];
    contextualPatterns: Map<string, LearningPattern>;
    temporalPatterns: string[];
  } {
    return {
      sequentialPatterns: this.identifySequentialPatterns(),
      contextualPatterns: this.identifyContextualPatterns(),
      temporalPatterns: this.identifyTemporalPatterns()
    };
  }

  public predictOptimalAction(task: string, context?: string): {
    recommendedAction: string;
    confidence: number;
    reasoning: string;
  } {
    const similarExperiences = this.experiences
      .filter(exp => this.calculateSimilarity(exp.task, task) > 0.5)
      .sort((a, b) => {
        const timeA = a.timestamp instanceof Date ? a.timestamp.getTime() : new Date(a.timestamp).getTime();
        const timeB = b.timestamp instanceof Date ? b.timestamp.getTime() : new Date(b.timestamp).getTime();
        return timeB - timeA;
      })
      .slice(0, 10);

    if (similarExperiences.length === 0) {
      return {
        recommendedAction: 'analyze',
        confidence: 0.3,
        reasoning: 'No similar experiences found, defaulting to analysis'
      };
    }

    const actionCounts = new Map<string, { count: number; successRate: number }>();

    for (const exp of similarExperiences) {
      if (exp.action) {
        const actionType = exp.action.type;
        const existing = actionCounts.get(actionType) || { count: 0, successRate: 0 };
        existing.count++;
        existing.successRate = (existing.successRate * (existing.count - 1) +
          (exp.outcome === 'success' ? 1 : 0)) / existing.count;
        actionCounts.set(actionType, existing);
      }
    }

    let bestAction = 'analyze';
    let bestScore = 0;
    let reasoning = '';

    for (const [action, stats] of actionCounts) {
      const score = stats.successRate * Math.log(stats.count + 1);
      if (score > bestScore) {
        bestScore = score;
        bestAction = action;
        reasoning = `Based on ${stats.count} similar experiences with ${(stats.successRate * 100).toFixed(1)}% success rate`;
      }
    }

    const confidence = Math.min(bestScore / 2, 0.9);

    return {
      recommendedAction: bestAction,
      confidence,
      reasoning
    };
  }

  public exportTrainingData(): any {
    return {
      experiences: this.experiences,
      patterns: Array.from(this.patterns.entries()),
      performance: this.analyzePerformance(),
      timestamp: new Date()
    };
  }

  public importTrainingData(data: any): void {
    if (data.experiences) {
      this.experiences = [...this.experiences, ...data.experiences];
    }
    
    if (data.patterns) {
      data.patterns.forEach(([key, pattern]: [string, LearningPattern]) => {
        this.patterns.set(key, pattern);
      });
    }
    
    this.saveMemory();
    this.logger.success('📥 Training data imported successfully');
  }

  private loadMemory(): void {
    try {
      if (fs.existsSync(this.memoryPath)) {
        const data = JSON.parse(fs.readFileSync(this.memoryPath, 'utf8'));
        this.experiences = data.experiences || [];
        
        if (data.patterns) {
          this.patterns = new Map(data.patterns);
        }
        
        this.logger.debug(`📚 Loaded ${this.experiences.length} experiences from memory`);
      }
    } catch (error) {
      this.logger.debug(`Failed to load memory: ${error}`);
    }
  }

  private saveMemory(): void {
    try {
      const memoryDir = path.dirname(this.memoryPath);
      if (!fs.existsSync(memoryDir)) {
        fs.mkdirSync(memoryDir, { recursive: true });
      }

      const data = {
        experiences: this.experiences,
        patterns: Array.from(this.patterns.entries()),
        lastUpdated: new Date()
      };

      fs.writeFileSync(this.memoryPath, JSON.stringify(data, null, 2));
    } catch (error) {
      this.logger.error(`Failed to save memory: ${error}`);
    }
  }

  private updatePatterns(experience: Experience): void {
    if (!experience.action) return;

    const patternKey = `${experience.action.type}:${this.extractTaskType(experience.task)}`;
    const existing = this.patterns.get(patternKey);

    if (existing) {
      existing.frequency++;
      const totalOutcomes = existing.frequency;
      const successes = existing.successRate * (totalOutcomes - 1) + (experience.outcome === 'success' ? 1 : 0);
      existing.successRate = successes / totalOutcomes;

      // Limit examples to prevent memory bloat
      if (existing.examples.length < 10) {
        existing.examples.push(experience.task);
      }
    } else {
      this.patterns.set(patternKey, {
        pattern: patternKey,
        frequency: 1,
        successRate: experience.outcome === 'success' ? 1 : 0,
        examples: [experience.task]
      });
    }

    // Clean up old patterns if we have too many
    if (this.patterns.size > 100) {
      this.cleanupOldPatterns();
    }
  }

  private cleanupOldPatterns(): void {
    // Remove patterns with low frequency and success rate
    const patternsToRemove: string[] = [];

    for (const [key, pattern] of this.patterns.entries()) {
      if (pattern.frequency < 3 && pattern.successRate < 0.5) {
        patternsToRemove.push(key);
      }
    }

    // Remove up to 20 patterns
    patternsToRemove.slice(0, 20).forEach(key => {
      this.patterns.delete(key);
    });
  }

  private calculateSimilarity(task1: string, task2: string): number {
    const words1 = task1.toLowerCase().split(/\s+/);
    const words2 = task2.toLowerCase().split(/\s+/);
    
    const intersection = words1.filter(word => words2.includes(word));
    const union = [...new Set([...words1, ...words2])];
    
    return intersection.length / union.length;
  }

  private extractTaskType(task: string): string {
    const keywords = ['create', 'edit', 'fix', 'implement', 'add', 'remove', 'refactor', 'test'];
    const taskLower = task.toLowerCase();
    
    for (const keyword of keywords) {
      if (taskLower.includes(keyword)) {
        return keyword;
      }
    }
    
    return 'general';
  }

  private extractCommonFailures(failures: Experience[]): string[] {
    const errorCounts = new Map<string, number>();
    
    failures.forEach(failure => {
      if (failure.error) {
        const errorType = this.categorizeError(failure.error);
        errorCounts.set(errorType, (errorCounts.get(errorType) || 0) + 1);
      }
    });
    
    return Array.from(errorCounts.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .map(([error]) => error);
  }

  private categorizeError(error: string): string {
    if (error.includes('not found')) return 'File not found';
    if (error.includes('permission')) return 'Permission denied';
    if (error.includes('syntax')) return 'Syntax error';
    if (error.includes('network')) return 'Network error';
    return 'Unknown error';
  }

  private generateImprovementSuggestions(commonFailures: string[]): string[] {
    const suggestions: string[] = [];
    
    commonFailures.forEach(failure => {
      switch (failure) {
        case 'File not found':
          suggestions.push('Verify file paths before operations');
          break;
        case 'Permission denied':
          suggestions.push('Check file permissions and user access');
          break;
        case 'Syntax error':
          suggestions.push('Validate code syntax before writing files');
          break;
        case 'Network error':
          suggestions.push('Implement retry logic for network operations');
          break;
      }
    });
    
    return suggestions;
  }

  private analyzeTaskPatterns(): void {
    // Analyze which types of tasks are most common and successful
    const taskTypes = new Map<string, { count: number; successRate: number }>();
    
    this.experiences.forEach(exp => {
      const taskType = this.extractTaskType(exp.task);
      const existing = taskTypes.get(taskType) || { count: 0, successRate: 0 };
      
      existing.count++;
      existing.successRate = (existing.successRate * (existing.count - 1) + 
        (exp.outcome === 'success' ? 1 : 0)) / existing.count;
      
      taskTypes.set(taskType, existing);
    });
  }

  private analyzeActionEffectiveness(): void {
    // Analyze which actions are most effective for different task types
    const actionEffectiveness = new Map<string, number>();
    
    this.experiences.forEach(exp => {
      if (exp.action) {
        const key = exp.action.type;
        const existing = actionEffectiveness.get(key) || 0;
        actionEffectiveness.set(key, existing + (exp.outcome === 'success' ? 1 : -1));
      }
    });
  }

  private identifyOptimalStrategies(): void {
    // Identify sequences of actions that lead to success
    const successfulSequences = this.experiences
      .filter(exp => exp.outcome === 'success')
      .map(exp => exp.action?.type)
      .filter(Boolean);

    // This could be expanded to find common successful patterns
  }

  private quickAnalyzePatterns(): void {
    // Lightweight pattern analysis for continuous learning
    const recentPatterns = new Map<string, number>();
    const recentExperiences = this.getRecentExperiences(5);

    for (const exp of recentExperiences) {
      if (exp.action) {
        const key = `${exp.action.type}:${this.extractTaskType(exp.task)}`;
        recentPatterns.set(key, (recentPatterns.get(key) || 0) + 1);
      }
    }

    // Update existing patterns with recent data
    for (const [key, frequency] of recentPatterns) {
      const existing = this.patterns.get(key);
      if (existing) {
        existing.frequency += frequency;
      }
    }
  }

  private identifySequentialPatterns(): string[] {
    const sequences: string[] = [];
    const experiences = this.experiences.slice(-20); // Last 20 experiences

    for (let i = 0; i < experiences.length - 2; i++) {
      const sequence = experiences.slice(i, i + 3)
        .map(exp => exp.action?.type)
        .filter(Boolean)
        .join(' -> ');

      if (sequence.split(' -> ').length === 3) {
        sequences.push(sequence);
      }
    }

    // Count sequence frequencies
    const sequenceCounts = new Map<string, number>();
    for (const seq of sequences) {
      sequenceCounts.set(seq, (sequenceCounts.get(seq) || 0) + 1);
    }

    // Return sequences that appear more than once
    return Array.from(sequenceCounts.entries())
      .filter(([_, count]) => count > 1)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .map(([seq, _]) => seq);
  }

  private identifyContextualPatterns(): Map<string, LearningPattern> {
    const contextualPatterns = new Map<string, LearningPattern>();

    // Group experiences by project path (context)
    const contextGroups = new Map<string, Experience[]>();

    for (const exp of this.experiences) {
      const context = exp.projectPath || 'unknown';
      if (!contextGroups.has(context)) {
        contextGroups.set(context, []);
      }
      contextGroups.get(context)!.push(exp);
    }

    // Analyze patterns within each context
    for (const [context, contextExperiences] of contextGroups) {
      if (contextExperiences.length < 3) continue;

      const successRate = contextExperiences.filter(exp => exp.outcome === 'success').length / contextExperiences.length;
      const actionTypes = contextExperiences.map(exp => exp.action?.type).filter(Boolean);
      const mostCommonAction = this.getMostFrequent(actionTypes);

      if (mostCommonAction) {
        contextualPatterns.set(context, {
          pattern: `${context}:${mostCommonAction}`,
          frequency: contextExperiences.length,
          successRate,
          examples: contextExperiences.slice(0, 3).map(exp => exp.task)
        });
      }
    }

    return contextualPatterns;
  }

  private identifyTemporalPatterns(): string[] {
    const patterns: string[] = [];
    const now = new Date();
    const timeWindows = [
      { name: 'morning', start: 6, end: 12 },
      { name: 'afternoon', start: 12, end: 18 },
      { name: 'evening', start: 18, end: 24 },
      { name: 'night', start: 0, end: 6 }
    ];

    for (const window of timeWindows) {
      const windowExperiences = this.experiences.filter(exp => {
        const hour = exp.timestamp.getHours();
        return hour >= window.start && hour < window.end;
      });

      if (windowExperiences.length > 5) {
        const successRate = windowExperiences.filter(exp => exp.outcome === 'success').length / windowExperiences.length;
        const actionTypes = windowExperiences.map(exp => exp.action?.type).filter(Boolean);
        const mostCommonAction = this.getMostFrequent(actionTypes);

        if (mostCommonAction && successRate > 0.7) {
          patterns.push(`${window.name}: ${mostCommonAction} (${(successRate * 100).toFixed(1)}% success)`);
        }
      }
    }

    return patterns;
  }

  private getMostFrequent<T>(items: T[]): T | null {
    if (items.length === 0) return null;

    const counts = new Map<T, number>();
    for (const item of items) {
      counts.set(item, (counts.get(item) || 0) + 1);
    }

    let mostFrequent: T | null = null;
    let maxCount = 0;

    for (const [item, count] of counts) {
      if (count > maxCount) {
        maxCount = count;
        mostFrequent = item;
      }
    }

    return mostFrequent;
  }

  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  // Additional methods for intelligence engine
  public getRecentExperiences(count: number = 50): Experience[] {
    return this.experiences
      .sort((a, b) => {
        const timeA = a.timestamp instanceof Date ? a.timestamp.getTime() : new Date(a.timestamp).getTime();
        const timeB = b.timestamp instanceof Date ? b.timestamp.getTime() : new Date(b.timestamp).getTime();
        return timeB - timeA;
      })
      .slice(0, count);
  }

  public getAllExperiences(): Experience[] {
    return [...this.experiences];
  }

  public getExperiencesByOutcome(outcome: 'success' | 'failure'): Experience[] {
    return this.experiences.filter(exp => exp.outcome === outcome);
  }

  public getExperiencesByTimeRange(startDate: Date, endDate: Date): Experience[] {
    return this.experiences.filter(exp => {
      const expDate = exp.timestamp instanceof Date ? exp.timestamp : new Date(exp.timestamp);
      return expDate >= startDate && expDate <= endDate;
    });
  }

  public getPatternSuccessRate(pattern: string): number {
    const patternData = this.patterns.get(pattern);
    return patternData ? patternData.successRate : 0;
  }

  public getAllPatterns(): Map<string, LearningPattern> {
    return new Map(this.patterns);
  }


}
