import { Logger } from '../utils/Logger';
import { MemoryManager, Experience } from '../agent/MemoryManager';
import { AutoTrainingEngine } from './AutoTrainingEngine';
import { IntelligenceEngine } from './IntelligenceEngine';
import { TrainingManager } from './TrainingManager';
import { KnowledgeRepository } from './KnowledgeRepository';
import { CuriosityEngine } from './CuriosityEngine';
import { PerformanceMonitor } from './PerformanceMonitor';
import { WebLearningEngine } from '../web/WebLearningEngine';
import { AIProviderManager } from '../ai/AIProviderManager';

export interface SelfTrainingConfig {
  enabled: boolean;
  continuousLearning: boolean;
  autonomousResearch: boolean;
  performanceOptimization: boolean;
  knowledgeSharing: boolean;
  adaptiveBehavior: boolean;
  backgroundProcessing: boolean;
  learningIntensity: 'low' | 'medium' | 'high';
}

export interface LearningGoal {
  id: string;
  domain: string;
  objective: string;
  priority: number;
  progress: number;
  deadline?: Date;
  resources: string[];
  status: 'active' | 'completed' | 'paused';
}

export interface TrainingMetrics {
  totalLearningCycles: number;
  knowledgeGrowthRate: number;
  performanceImprovement: number;
  autonomyLevel: number;
  adaptabilityScore: number;
  lastTrainingSession: Date;
  averageSessionDuration: number;
}

export class SelfTrainingOrchestrator {
  private logger: Logger;
  private memoryManager: MemoryManager;
  private autoTrainingEngine: AutoTrainingEngine;
  private intelligenceEngine: IntelligenceEngine;
  private trainingManager: TrainingManager;
  private knowledgeRepository: KnowledgeRepository;
  private curiosityEngine: CuriosityEngine;
  private performanceMonitor: PerformanceMonitor;
  private webLearningEngine: WebLearningEngine;
  private aiManager: AIProviderManager;

  private config: SelfTrainingConfig;
  private learningGoals: LearningGoal[] = [];
  private metrics: TrainingMetrics;
  private isTraining: boolean = false;
  private trainingInterval?: NodeJS.Timeout;
  private isTaskExecuting: boolean = false;
  private taskExecutionCallbacks: Set<() => void> = new Set();

  constructor(
    logger: Logger,
    memoryManager: MemoryManager,
    autoTrainingEngine: AutoTrainingEngine,
    intelligenceEngine: IntelligenceEngine,
    trainingManager: TrainingManager,
    knowledgeRepository: KnowledgeRepository,
    curiosityEngine: CuriosityEngine,
    performanceMonitor: PerformanceMonitor,
    webLearningEngine: WebLearningEngine,
    aiManager: AIProviderManager
  ) {
    this.logger = logger;
    this.memoryManager = memoryManager;
    this.autoTrainingEngine = autoTrainingEngine;
    this.intelligenceEngine = intelligenceEngine;
    this.trainingManager = trainingManager;
    this.knowledgeRepository = knowledgeRepository;
    this.curiosityEngine = curiosityEngine;
    this.performanceMonitor = performanceMonitor;
    this.webLearningEngine = webLearningEngine;
    this.aiManager = aiManager;

    this.config = {
      enabled: true,
      continuousLearning: true,
      autonomousResearch: true,
      performanceOptimization: true,
      knowledgeSharing: true,
      adaptiveBehavior: true,
      backgroundProcessing: true,
      learningIntensity: 'medium'
    };

    this.metrics = {
      totalLearningCycles: 0,
      knowledgeGrowthRate: 0,
      performanceImprovement: 0,
      autonomyLevel: 0.5,
      adaptabilityScore: 0.5,
      lastTrainingSession: new Date(),
      averageSessionDuration: 0
    };

    this.initializeSelfTraining();
  }

  public async initializeSelfTraining(): Promise<void> {
    // Debug: Self-Training Orchestrator initialization started
    // this.logger.info('🧠 Initializing Self-Training Orchestrator...');

    try {
      // Load existing knowledge and metrics from previous sessions
      await this.loadTrainingState();

      // Initialize learning goals for the current session
      await this.initializeLearningGoals();

      // Start continuous learning background process if enabled
      if (this.config.continuousLearning) {
        this.startContinuousLearning();
      }

      // Enable autonomous research capabilities if configured
      if (this.config.autonomousResearch) {
        await this.enableAutonomousResearch();
      }

      // Debug: Self-Training Orchestrator initialized successfully
      // this.logger.success('✅ Self-Training Orchestrator initialized successfully');
    } catch (error) {
      // Log errors for debugging - these are important for troubleshooting
      this.logger.error(`❌ Failed to initialize self-training: ${error}`);
    }
  }

  public async startSelfTraining(): Promise<void> {
    if (this.isTraining) {
      this.logger.debug('Self-training already in progress');
      return;
    }

    // Don't start training if a task is currently executing
    if (this.isTaskExecuting) {
      this.logger.debug('Skipping self-training: task execution in progress');
      return;
    }

    this.isTraining = true;
    const startTime = Date.now();

    try {
      // Only show training messages if not in silent mode during task execution
      if (!this.isTaskExecuting) {
        this.logger.info('🚀 Starting comprehensive self-training session...');
      }

      // Phase 1: Experience Analysis and Pattern Recognition
      await this.analyzeExperiencesAndPatterns();

      // Phase 2: Knowledge Integration and Expansion
      await this.integrateAndExpandKnowledge();

      // Phase 3: Performance Evaluation and Optimization
      await this.evaluateAndOptimizePerformance();

      // Phase 4: Autonomous Learning and Research
      await this.conductAutonomousLearning();

      // Phase 5: Adaptive Behavior Updates
      await this.updateAdaptiveBehavior();

      // Update metrics
      this.updateTrainingMetrics(startTime);

      if (!this.isTaskExecuting) {
        this.logger.success('✅ Self-training session completed successfully');
      }
    } catch (error) {
      if (!this.isTaskExecuting) {
        this.logger.error(`❌ Self-training session failed: ${error}`);
      }
    } finally {
      this.isTraining = false;
    }
  }

  private async analyzeExperiencesAndPatterns(): Promise<void> {
    if (!this.isTaskExecuting) {
      this.logger.info('📊 Analyzing experiences and patterns...');
    }

    // Get recent experiences for analysis
    const experiences = this.memoryManager.getRecentExperiences(50);

    // Analyze success patterns
    const successPatterns = this.memoryManager.getSuccessPatterns();
    const failurePatterns = this.memoryManager.getFailurePatterns();

    // Store insights in knowledge repository
    await this.knowledgeRepository.storePatternAnalysis(successPatterns, failurePatterns);

    if (!this.isTaskExecuting) {
      this.logger.info('📊 Storing pattern analysis in knowledge repository...');
    }

    // Update learning goals based on patterns
    await this.updateLearningGoalsFromPatterns(successPatterns, failurePatterns);
  }

  private async integrateAndExpandKnowledge(): Promise<void> {
    if (!this.isTaskExecuting) {
      this.logger.info('🔗 Integrating and expanding knowledge...');
    }

    // Integrate new knowledge from recent experiences
    if (!this.isTaskExecuting) {
      this.logger.info('🔗 Integrating experiences into knowledge repository...');
    }
    await this.knowledgeRepository.integrateExperiences(
      this.memoryManager.getRecentExperiences(20)
    );

    // Expand knowledge through cross-domain connections
    if (!this.isTaskExecuting) {
      this.logger.info('🌐 Expanding knowledge connections...');
    }
    await this.knowledgeRepository.expandKnowledgeConnections();
  }

  private async evaluateAndOptimizePerformance(): Promise<void> {
    if (!this.isTaskExecuting) {
      this.logger.info('⚡ Evaluating and optimizing performance...');
    }

    // Monitor current performance
    if (!this.isTaskExecuting) {
      this.logger.info('📊 Evaluating current performance...');
    }
    const performance = await this.performanceMonitor.evaluateCurrentPerformance();

    // Identify optimization opportunities
    if (!this.isTaskExecuting) {
      this.logger.info('🔧 Identifying performance optimizations...');
    }
    const optimizations = await this.performanceMonitor.identifyOptimizations();

    // Apply performance improvements
    await this.applyPerformanceOptimizations(optimizations);
  }

  private async conductAutonomousLearning(): Promise<void> {
    if (!this.isTaskExecuting) {
      this.logger.info('🔍 Conducting autonomous learning...');
    }

    // Generate curiosity-driven research topics
    if (!this.isTaskExecuting) {
      this.logger.info('🔍 Generating curiosity-driven research topics...');
    }
    const researchTopics = await this.curiosityEngine.generateResearchTopics();

    // Conduct autonomous web research
    for (const topic of researchTopics.slice(0, 3)) {
      if (!this.isTaskExecuting) {
        this.logger.info(`🔍 Processing web search request: "${topic}"`);
      }
      await this.webLearningEngine.processSearchRequest(topic);
    }
  }

  private async updateAdaptiveBehavior(): Promise<void> {
    if (!this.isTaskExecuting) {
      this.logger.info('🎯 Updating adaptive behavior...');
    }

    // Analyze behavior effectiveness
    if (!this.isTaskExecuting) {
      this.logger.info('🎯 Analyzing behavior effectiveness...');
    }
    const behaviorAnalysis = await this.performanceMonitor.analyzeBehaviorEffectiveness();

    // Update intelligence engine with new behaviors
    if (!this.isTaskExecuting) {
      this.logger.info('🎯 Adapting behavior based on context...');
    }
    await this.intelligenceEngine.adaptBehaviorBasedOnContext();
  }

  private startContinuousLearning(): void {
    const interval = this.getLearningInterval();

    this.trainingInterval = setInterval(async () => {
      if (!this.isTraining && this.config.enabled && !this.isTaskExecuting) {
        await this.startSelfTraining();
      }
    }, interval);

    // Debug: Continuous learning enabled with specified interval
    // this.logger.info(`🔄 Continuous learning enabled (interval: ${interval}ms)`);
  }

  private getLearningInterval(): number {
    switch (this.config.learningIntensity) {
      case 'low': return 30 * 60 * 1000; // 30 minutes
      case 'medium': return 15 * 60 * 1000; // 15 minutes
      case 'high': return 5 * 60 * 1000; // 5 minutes
      default: return 15 * 60 * 1000;
    }
  }

  private async loadTrainingState(): Promise<void> {
    try {
      // Load existing metrics and learning goals from knowledge repository
      const knowledgeStats = this.knowledgeRepository.getKnowledgeStats();

      // Update metrics based on stored knowledge
      this.metrics.knowledgeGrowthRate = knowledgeStats.totalNodes / 100; // Normalize
      this.metrics.autonomyLevel = Math.min(knowledgeStats.averageConfidence, 1.0);

      this.logger.debug('📊 Training state loaded successfully');
    } catch (error) {
      this.logger.debug(`Failed to load training state: ${error}`);
    }
  }

  private async initializeLearningGoals(): Promise<void> {
    // Initialize default learning goals for comprehensive development
    const defaultGoals: LearningGoal[] = [
      {
        id: this.generateId(),
        domain: 'software-architecture',
        objective: 'Master modern software architecture patterns and principles',
        priority: 0.9,
        progress: 0,
        resources: ['web-research', 'pattern-analysis'],
        status: 'active'
      },
      {
        id: this.generateId(),
        domain: 'performance-optimization',
        objective: 'Learn advanced performance optimization techniques',
        priority: 0.8,
        progress: 0,
        resources: ['web-research', 'code-analysis'],
        status: 'active'
      },
      {
        id: this.generateId(),
        domain: 'security-practices',
        objective: 'Understand modern security best practices and vulnerabilities',
        priority: 0.85,
        progress: 0,
        resources: ['web-research', 'security-analysis'],
        status: 'active'
      },
      {
        id: this.generateId(),
        domain: 'emerging-technologies',
        objective: 'Stay current with emerging development technologies',
        priority: 0.6,
        progress: 0,
        resources: ['web-research', 'trend-analysis'],
        status: 'active'
      }
    ];

    this.learningGoals.push(...defaultGoals);
    // Debug: Learning goals initialized
    // this.logger.info(`🎯 Initialized ${defaultGoals.length} learning goals`);
  }

  private async enableAutonomousResearch(): Promise<void> {
    try {
      // Enable continuous web learning
      await this.webLearningEngine.enableContinuousLearning();

      // Start curiosity-driven exploration
      const researchTopics = await this.curiosityEngine.generateResearchTopics();

      if (researchTopics.length > 0) {
        this.logger.info(`🔍 Generated ${researchTopics.length} autonomous research topics`);

        // Conduct initial research on high-priority topics
        for (const topic of researchTopics.slice(0, 2)) {
          await this.webLearningEngine.processSearchRequest(topic);
        }
      }

      this.logger.success('✅ Autonomous research capabilities enabled');
    } catch (error) {
      this.logger.error(`❌ Failed to enable autonomous research: ${error}`);
    }
  }

  private async updateLearningGoalsFromPatterns(successPatterns: any[], failurePatterns: any[]): Promise<void> {
    // Analyze patterns to identify learning opportunities
    for (const pattern of failurePatterns) {
      if (pattern.frequency > 2) {
        // Create learning goal to address frequent failures
        const goal: LearningGoal = {
          id: this.generateId(),
          domain: 'problem-solving',
          objective: `Learn to avoid pattern: ${pattern.pattern}`,
          priority: 0.8,
          progress: 0,
          resources: ['web-research', 'pattern-analysis'],
          status: 'active'
        };

        this.learningGoals.push(goal);
      }
    }

    // Update progress on existing goals based on success patterns
    for (const goal of this.learningGoals) {
      const relatedSuccesses = successPatterns.filter(p =>
        p.pattern.includes(goal.domain) || goal.objective.toLowerCase().includes(p.pattern.toLowerCase())
      );

      if (relatedSuccesses.length > 0) {
        goal.progress = Math.min(goal.progress + 0.1, 1.0);
      }
    }
  }

  private async applyPerformanceOptimizations(optimizations: any[]): Promise<void> {
    for (const optimization of optimizations) {
      try {
        this.logger.info(`🔧 Applying optimization: ${optimization.description}`);

        switch (optimization.type) {
          case 'speed':
            await this.optimizeSpeed(optimization);
            break;
          case 'accuracy':
            await this.optimizeAccuracy(optimization);
            break;
          case 'learning':
            await this.optimizeLearning(optimization);
            break;
          case 'efficiency':
            await this.optimizeEfficiency(optimization);
            break;
        }

        this.logger.success(`✅ Applied optimization: ${optimization.type}`);
      } catch (error) {
        this.logger.error(`❌ Failed to apply optimization ${optimization.type}: ${error}`);
      }
    }
  }

  private async optimizeSpeed(optimization: any): Promise<void> {
    // Implement speed optimizations
    this.config.learningIntensity = 'low'; // Reduce learning intensity for speed
  }

  private async optimizeAccuracy(optimization: any): Promise<void> {
    // Implement accuracy optimizations
    this.config.learningIntensity = 'high'; // Increase learning for better accuracy
  }

  private async optimizeLearning(optimization: any): Promise<void> {
    // Implement learning optimizations
    await this.knowledgeRepository.expandKnowledgeConnections();
  }

  private async optimizeEfficiency(optimization: any): Promise<void> {
    // Implement efficiency optimizations
    this.memoryManager.trainFromExperiences();
  }

  private updateTrainingMetrics(startTime: number): void {
    const duration = Date.now() - startTime;
    this.metrics.totalLearningCycles++;
    this.metrics.lastTrainingSession = new Date();
    this.metrics.averageSessionDuration = 
      (this.metrics.averageSessionDuration + duration) / 2;
  }

  public getMetrics(): TrainingMetrics {
    return { ...this.metrics };
  }

  public async stopSelfTraining(): Promise<void> {
    this.config.enabled = false;
    if (this.trainingInterval) {
      clearInterval(this.trainingInterval);
    }
    this.logger.info('🛑 Self-training stopped');
  }

  public updateConfig(newConfig: Partial<SelfTrainingConfig>): void {
    this.config = { ...this.config, ...newConfig };
    // Debug: Self-training configuration updated
    // this.logger.info('⚙️ Self-training configuration updated');
  }

  public getLearningGoals(): LearningGoal[] {
    return [...this.learningGoals];
  }

  public async addLearningGoal(goal: Omit<LearningGoal, 'id'>): Promise<string> {
    const newGoal: LearningGoal = {
      ...goal,
      id: this.generateId()
    };

    this.learningGoals.push(newGoal);
    this.logger.info(`🎯 Added new learning goal: ${goal.objective}`);

    return newGoal.id;
  }

  public async completeLearningGoal(goalId: string): Promise<void> {
    const goal = this.learningGoals.find(g => g.id === goalId);
    if (goal) {
      goal.status = 'completed';
      goal.progress = 1.0;
      this.logger.success(`✅ Completed learning goal: ${goal.objective}`);
    }
  }

  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  public getTrainingStatus(): {
    isTraining: boolean;
    config: SelfTrainingConfig;
    metrics: TrainingMetrics;
    activeGoals: number;
    completedGoals: number;
  } {
    return {
      isTraining: this.isTraining,
      config: { ...this.config },
      metrics: { ...this.metrics },
      activeGoals: this.learningGoals.filter(g => g.status === 'active').length,
      completedGoals: this.learningGoals.filter(g => g.status === 'completed').length
    };
  }

  // Task execution state management
  public setTaskExecuting(executing: boolean): void {
    this.isTaskExecuting = executing;
    if (executing) {
      // Notify any waiting callbacks that task execution has started
      this.taskExecutionCallbacks.forEach(callback => callback());
      this.taskExecutionCallbacks.clear();
    }
  }

  public isTaskCurrentlyExecuting(): boolean {
    return this.isTaskExecuting;
  }

  public onTaskExecutionEnd(callback: () => void): void {
    if (!this.isTaskExecuting) {
      callback();
    } else {
      this.taskExecutionCallbacks.add(callback);
    }
  }
}
