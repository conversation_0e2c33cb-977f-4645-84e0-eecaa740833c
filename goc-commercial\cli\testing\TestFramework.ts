import { Logger } from '../utils/Logger';
import { AIProviderManager } from '../ai/AIProviderManager';
import { CodebaseAnalyzer } from '../codebase/CodebaseAnalyzer';
import { ConfigManager } from '../config/ConfigManager';
import fs from 'fs';
import path from 'path';

export interface TestResult {
  name: string;
  passed: boolean;
  duration: number;
  error?: string;
  details?: any;
}

export interface TestSuite {
  name: string;
  tests: TestCase[];
  setup?: () => Promise<void>;
  teardown?: () => Promise<void>;
}

export interface TestCase {
  name: string;
  test: () => Promise<void>;
  timeout?: number;
}

export interface TestReport {
  totalTests: number;
  passed: number;
  failed: number;
  duration: number;
  results: TestResult[];
  coverage?: {
    lines: number;
    functions: number;
    branches: number;
  };
}

export class TestFramework {
  private logger: Logger;
  private config: ConfigManager;
  private aiManager: AIProviderManager;
  private codebaseAnalyzer: CodebaseAnalyzer;
  private testSuites: TestSuite[] = [];

  constructor(
    logger: Logger,
    config: ConfigManager,
    aiManager: AIProviderManager,
    codebaseAnalyzer: CodebaseAnalyzer
  ) {
    this.logger = logger;
    this.config = config;
    this.aiManager = aiManager;
    this.codebaseAnalyzer = codebaseAnalyzer;
    this.initializeTestSuites();
  }

  private initializeTestSuites(): void {
    // Core component tests
    this.testSuites = [
      this.createAIProviderTests(),
      this.createCodebaseAnalyzerTests(),
      this.createConfigManagerTests(),
      this.createIntegrationTests()
    ];
  }

  public async runAllTests(): Promise<TestReport> {
    this.logger.info('🧪 Starting comprehensive test suite...');
    const startTime = Date.now();
    const allResults: TestResult[] = [];

    for (const suite of this.testSuites) {
      this.logger.info(`📋 Running test suite: ${suite.name}`);
      
      try {
        if (suite.setup) {
          await suite.setup();
        }

        for (const testCase of suite.tests) {
          const result = await this.runSingleTest(suite.name, testCase);
          allResults.push(result);
        }

        if (suite.teardown) {
          await suite.teardown();
        }
      } catch (error) {
        this.logger.error(`Test suite ${suite.name} failed: ${error}`);
        allResults.push({
          name: `${suite.name} (suite failure)`,
          passed: false,
          duration: 0,
          error: error instanceof Error ? error.message : String(error)
        });
      }
    }

    const duration = Date.now() - startTime;
    const passed = allResults.filter(r => r.passed).length;
    const failed = allResults.length - passed;

    const report: TestReport = {
      totalTests: allResults.length,
      passed,
      failed,
      duration,
      results: allResults
    };

    this.displayTestReport(report);
    return report;
  }

  public async runTestSuite(suiteName: string): Promise<TestResult[]> {
    const suite = this.testSuites.find(s => s.name === suiteName);
    if (!suite) {
      throw new Error(`Test suite '${suiteName}' not found`);
    }

    this.logger.info(`🧪 Running test suite: ${suiteName}`);
    const results: TestResult[] = [];

    try {
      if (suite.setup) {
        await suite.setup();
      }

      for (const testCase of suite.tests) {
        const result = await this.runSingleTest(suiteName, testCase);
        results.push(result);
      }

      if (suite.teardown) {
        await suite.teardown();
      }
    } catch (error) {
      this.logger.error(`Test suite ${suiteName} failed: ${error}`);
    }

    return results;
  }

  private async runSingleTest(suiteName: string, testCase: TestCase): Promise<TestResult> {
    const startTime = Date.now();
    const timeout = testCase.timeout || 30000; // 30 seconds default

    try {
      // Run test with timeout
      await Promise.race([
        testCase.test(),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Test timeout')), timeout)
        )
      ]);

      const duration = Date.now() - startTime;
      this.logger.success(`✅ ${suiteName}: ${testCase.name} (${duration}ms)`);
      
      return {
        name: `${suiteName}: ${testCase.name}`,
        passed: true,
        duration
      };
    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);
      
      this.logger.error(`❌ ${suiteName}: ${testCase.name} - ${errorMessage}`);
      
      return {
        name: `${suiteName}: ${testCase.name}`,
        passed: false,
        duration,
        error: errorMessage
      };
    }
  }

  private createAIProviderTests(): TestSuite {
    return {
      name: 'AI Provider Manager',
      tests: [
        {
          name: 'should initialize providers',
          test: async () => {
            const providers = await this.aiManager.getAvailableProviders();
            if (providers.length === 0) {
              throw new Error('No AI providers available');
            }
          }
        },
        {
          name: 'should handle provider fallback',
          test: async () => {
            try {
              const messages = [{ role: 'user' as const, content: 'test' }];
              await this.aiManager.chat(messages, 'nonexistent-provider');
            } catch (error) {
              // Should fail gracefully with helpful error
              if (!error || typeof error !== 'object' || !('message' in error)) {
                throw new Error('Provider fallback not working correctly');
              }
            }
          }
        },
        {
          name: 'should list models for available providers',
          test: async () => {
            const providers = await this.aiManager.getAvailableProviders();
            if (providers.length > 0) {
              const provider = this.aiManager.getProvider(providers[0]);
              if (provider) {
                const models = await provider.listModels();
                // Should not throw error even if no models
              }
            }
          }
        }
      ]
    };
  }

  private createCodebaseAnalyzerTests(): TestSuite {
    return {
      name: 'Codebase Analyzer',
      tests: [
        {
          name: 'should scan current codebase',
          test: async () => {
            const context = await this.codebaseAnalyzer.scanCodebase();
            if (!context || context.totalFiles === 0) {
              throw new Error('Failed to scan codebase');
            }
          }
        },
        {
          name: 'should search files',
          test: async () => {
            const results = await this.codebaseAnalyzer.searchFiles('test');
            // Should return array (even if empty)
            if (!Array.isArray(results)) {
              throw new Error('Search should return array');
            }
          }
        },
        {
          name: 'should get file content',
          test: async () => {
            // Test with package.json which should exist
            const content = await this.codebaseAnalyzer.getFileContent('package.json');
            if (!content || content.length === 0) {
              throw new Error('Failed to read package.json');
            }
          }
        }
      ]
    };
  }

  private createConfigManagerTests(): TestSuite {
    return {
      name: 'Config Manager',
      tests: [
        {
          name: 'should load configuration',
          test: async () => {
            const config = this.config.getConfig();
            if (!config || !config.providers) {
              throw new Error('Failed to load configuration');
            }
          }
        },
        {
          name: 'should validate provider configuration',
          test: async () => {
            const config = this.config.getConfig();
            const providers = Object.keys(config.providers);
            if (providers.length === 0) {
              throw new Error('No providers configured');
            }
          }
        },
        {
          name: 'should handle missing config gracefully',
          test: async () => {
            // Test config path handling
            const configPath = this.config.getConfigPath();
            if (!configPath) {
              throw new Error('Config path not set');
            }
          }
        }
      ]
    };
  }

  private createIntegrationTests(): TestSuite {
    return {
      name: 'Integration Tests',
      tests: [
        {
          name: 'should perform end-to-end chat',
          test: async () => {
            const providers = await this.aiManager.getAvailableProviders();
            if (providers.length > 0) {
              const messages = [{ 
                role: 'user' as const, 
                content: 'Say "test successful" if you can read this' 
              }];
              
              try {
                const response = await this.aiManager.chat(messages);
                if (!response || !response.content) {
                  throw new Error('No response from AI provider');
                }
              } catch (error) {
                // Skip if no providers are actually available
                this.logger.warn('Skipping AI integration test - no providers available');
              }
            }
          },
          timeout: 60000 // 1 minute for AI response
        },
        {
          name: 'should integrate codebase analysis with AI',
          test: async () => {
            const context = await this.codebaseAnalyzer.scanCodebase();
            if (context.totalFiles > 0) {
              // Test that we can build context for AI
              const summary = `Codebase has ${context.totalFiles} files`;
              if (!summary) {
                throw new Error('Failed to create codebase summary');
              }
            }
          }
        }
      ]
    };
  }

  private displayTestReport(report: TestReport): void {
    const passRate = (report.passed / report.totalTests * 100).toFixed(1);
    
    this.logger.info('\n📊 Test Report Summary:');
    this.logger.info(`Total Tests: ${report.totalTests}`);
    this.logger.info(`Passed: ${report.passed} (${passRate}%)`);
    this.logger.info(`Failed: ${report.failed}`);
    this.logger.info(`Duration: ${report.duration}ms`);

    if (report.failed > 0) {
      this.logger.warn('\n❌ Failed Tests:');
      report.results
        .filter(r => !r.passed)
        .forEach(r => {
          this.logger.error(`  - ${r.name}: ${r.error}`);
        });
    }

    if (report.passed === report.totalTests) {
      this.logger.success('\n🎉 All tests passed!');
    }
  }

  public async generateTestReport(outputPath?: string): Promise<void> {
    const report = await this.runAllTests();
    
    if (outputPath) {
      const reportData = {
        timestamp: new Date().toISOString(),
        ...report
      };
      
      fs.writeFileSync(outputPath, JSON.stringify(reportData, null, 2));
      this.logger.success(`Test report saved to: ${outputPath}`);
    }
  }
}
