#!/bin/bash

# GOC Agent Production Deployment Script
# This script handles the deployment of the GOC Agent Laravel backend to production

set -e

echo "🚀 Starting GOC Agent deployment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
APP_DIR="/var/www/goc-agent"
BACKUP_DIR="/var/backups/goc-agent"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

# Functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as correct user
if [ "$EUID" -eq 0 ]; then
    log_error "Do not run this script as root"
    exit 1
fi

# Create backup directory if it doesn't exist
mkdir -p $BACKUP_DIR

# Step 1: Backup current deployment
log_info "Creating backup of current deployment..."
if [ -d "$APP_DIR" ]; then
    tar -czf "$BACKUP_DIR/goc-agent-backup-$TIMESTAMP.tar.gz" -C "$APP_DIR" .
    log_info "Backup created: $BACKUP_DIR/goc-agent-backup-$TIMESTAMP.tar.gz"
fi

# Step 2: Pull latest code
log_info "Pulling latest code from repository..."
cd $APP_DIR
git pull origin main

# Step 3: Install/Update dependencies
log_info "Installing Composer dependencies..."
composer install --no-dev --optimize-autoloader --no-interaction

log_info "Installing NPM dependencies..."
npm ci --production

# Step 4: Build assets
log_info "Building production assets..."
npm run build

# Step 5: Run database migrations
log_info "Running database migrations..."
php artisan migrate --force

# Step 6: Seed essential data
log_info "Seeding subscription plans..."
php artisan db:seed --class=SubscriptionPlanSeeder --force

# Step 7: Clear and optimize caches
log_info "Optimizing application..."
php artisan config:cache
php artisan route:cache
php artisan view:cache
php artisan event:cache
php artisan queue:restart

# Step 8: Set proper permissions
log_info "Setting file permissions..."
sudo chown -R www-data:www-data $APP_DIR
sudo chmod -R 755 $APP_DIR
sudo chmod -R 775 $APP_DIR/storage
sudo chmod -R 775 $APP_DIR/bootstrap/cache

# Step 9: Restart services
log_info "Restarting services..."
sudo systemctl reload nginx
sudo systemctl restart php8.2-fpm
sudo supervisorctl restart goc-agent-worker:*

# Step 10: Health check
log_info "Performing health check..."
sleep 5

# Check if application is responding
if curl -f -s http://localhost/health > /dev/null; then
    log_info "✅ Application is healthy and responding"
else
    log_error "❌ Application health check failed"
    log_warning "Consider rolling back to previous version"
    exit 1
fi

# Step 11: Cleanup old backups (keep last 10)
log_info "Cleaning up old backups..."
cd $BACKUP_DIR
ls -t goc-agent-backup-*.tar.gz | tail -n +11 | xargs -r rm

log_info "🎉 Deployment completed successfully!"
log_info "Application is now running the latest version"

# Display deployment summary
echo ""
echo "=== Deployment Summary ==="
echo "Timestamp: $TIMESTAMP"
echo "Git Commit: $(git rev-parse --short HEAD)"
echo "Backup Location: $BACKUP_DIR/goc-agent-backup-$TIMESTAMP.tar.gz"
echo "=========================="
