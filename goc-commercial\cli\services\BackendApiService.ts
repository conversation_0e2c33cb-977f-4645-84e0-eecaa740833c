import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { ConfigManager } from '../config/ConfigManager';

export interface AuthResponse {
    user: {
        id: number;
        name: string;
        email: string;
        role: string;
        api_quota_daily: number;
        api_quota_monthly: number;
    };
    token: string;
    message: string;
}

export interface SessionResponse {
    id: number;
    session_uuid: string;
    title: string;
    provider: string;
    model: string;
    status: string;
    created_at: string;
    updated_at: string;
}

export interface ChatResponse {
    user_message: {
        id: number;
        role: string;
        content: string;
        created_at: string;
    };
    assistant_message: {
        id: number;
        role: string;
        content: string;
        metadata: any;
        token_usage: any;
        created_at: string;
    };
    session: SessionResponse;
}

export interface ToolResponse {
    success: boolean;
    tool: string;
    result: any;
    execution_time_ms: number;
}

export interface ProviderInfo {
    name: string;
    available: boolean;
    models: string[];
}

export class BackendApiService {
    private client: AxiosInstance;
    private token: string | null = null;
    private baseUrl: string;

    constructor() {
        const config = ConfigManager.getInstance();
        this.baseUrl = config.get('backend.url') || 'http://localhost:8000/api';
        
        this.client = axios.create({
            baseURL: this.baseUrl,
            timeout: 120000,
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
            },
        });

        // Add request interceptor to include auth token
        this.client.interceptors.request.use((config) => {
            if (this.token) {
                config.headers.Authorization = `Bearer ${this.token}`;
            }
            return config;
        });

        // Add response interceptor for error handling
        this.client.interceptors.response.use(
            (response) => response,
            (error) => {
                if (error.response?.status === 401) {
                    this.token = null;
                    // Clear stored token
                    const configManager = ConfigManager.getInstance();
                    configManager.delete('auth.token');
                }
                return Promise.reject(error);
            }
        );

        // Load stored token
        this.loadStoredToken();
    }

    private loadStoredToken(): void {
        const config = ConfigManager.getInstance();
        this.token = config.get('auth.token');
    }

    private saveToken(token: string): void {
        this.token = token;
        const config = ConfigManager.getInstance();
        config.set('auth.token', token);
    }

    /**
     * Register a new user.
     */
    async register(name: string, email: string, password: string, passwordConfirmation: string): Promise<AuthResponse> {
        const response = await this.client.post('/auth/register', {
            name,
            email,
            password,
            password_confirmation: passwordConfirmation,
        });

        const data = response.data;
        this.saveToken(data.token);
        return data;
    }

    /**
     * Login user.
     */
    async login(email: string, password: string): Promise<AuthResponse> {
        const response = await this.client.post('/auth/login', {
            email,
            password,
        });

        const data = response.data;
        this.saveToken(data.token);
        return data;
    }

    /**
     * Logout user.
     */
    async logout(): Promise<void> {
        if (this.token) {
            await this.client.post('/auth/logout');
        }
        this.token = null;
        const config = ConfigManager.getInstance();
        config.delete('auth.token');
    }

    /**
     * Get current user.
     */
    async getUser(): Promise<any> {
        const response = await this.client.get('/auth/user');
        return response.data.user;
    }

    /**
     * Check if user is authenticated.
     */
    isAuthenticated(): boolean {
        return !!this.token;
    }

    /**
     * Create a new session.
     */
    async createSession(title?: string, provider?: string, model?: string): Promise<SessionResponse> {
        const response = await this.client.post('/sessions', {
            title,
            provider,
            model,
        });
        return response.data.session;
    }

    /**
     * Get all sessions.
     */
    async getSessions(): Promise<SessionResponse[]> {
        const response = await this.client.get('/sessions');
        return response.data.data;
    }

    /**
     * Get a specific session.
     */
    async getSession(sessionId: number): Promise<SessionResponse> {
        const response = await this.client.get(`/sessions/${sessionId}`);
        return response.data.session;
    }

    /**
     * Update a session.
     */
    async updateSession(sessionId: number, updates: Partial<SessionResponse>): Promise<SessionResponse> {
        const response = await this.client.put(`/sessions/${sessionId}`, updates);
        return response.data.session;
    }

    /**
     * Delete a session.
     */
    async deleteSession(sessionId: number): Promise<void> {
        await this.client.delete(`/sessions/${sessionId}`);
    }

    /**
     * Send a chat message.
     */
    async chat(sessionId: number, message: string, provider?: string, model?: string): Promise<ChatResponse> {
        const response = await this.client.post('/agent/chat', {
            session_id: sessionId,
            message,
            provider,
            model,
        });
        return response.data;
    }

    /**
     * Execute a task.
     */
    async executeTask(task: string, sessionId?: number, provider?: string, model?: string, autoMode?: boolean): Promise<any> {
        const response = await this.client.post('/agent/task', {
            task,
            session_id: sessionId,
            provider,
            model,
            auto_mode: autoMode,
        });
        return response.data;
    }

    /**
     * Get agent status.
     */
    async getStatus(): Promise<any> {
        const response = await this.client.get('/agent/status');
        return response.data;
    }

    /**
     * Get available providers.
     */
    async getProviders(): Promise<Record<string, ProviderInfo>> {
        const response = await this.client.get('/agent/providers');
        return response.data.providers;
    }

    /**
     * Execute a tool.
     */
    async executeTool(tool: string, parameters: any, sessionId?: number): Promise<ToolResponse> {
        const response = await this.client.post(`/agent/tools/${tool}`, {
            parameters,
            session_id: sessionId,
        });
        return response.data;
    }

    /**
     * Index a project for context search.
     */
    async indexProject(projectPath: string): Promise<any> {
        const response = await this.client.post('/context/index', {
            project_path: projectPath,
        });
        return response.data;
    }

    /**
     * Search code using context engine.
     */
    async searchCode(query: string, projectPath?: string, limit?: number): Promise<any> {
        const response = await this.client.post('/context/search', {
            query,
            project_path: projectPath,
            limit,
        });
        return response.data;
    }

    /**
     * Get project statistics.
     */
    async getProjectStats(projectPath?: string): Promise<any> {
        const response = await this.client.get('/context/stats', {
            params: { project_path: projectPath },
        });
        return response.data;
    }

    /**
     * Check backend health.
     */
    async healthCheck(): Promise<boolean> {
        try {
            const response = await this.client.get('/health');
            return response.data.status === 'ok';
        } catch (error) {
            return false;
        }
    }
}
