import inquirer from 'inquirer';
import chalk from 'chalk';
import boxen from 'boxen';
import Table from 'cli-table3';
import { Logger } from '../utils/Logger';
import { IntelligenceEngine } from '../training/IntelligenceEngine';
import { TrainingManager } from '../training/TrainingManager';
import { MemoryManager } from '../agent/MemoryManager';

export interface TrainingCommandOptions {
  auto?: boolean;
  continuous?: boolean;
  analyze?: boolean;
  report?: boolean;
  goal?: string;
  status?: boolean;
}

export class TrainingCommandHandler {
  private logger: Logger;
  private intelligenceEngine: IntelligenceEngine;
  private trainingManager: TrainingManager;
  private memoryManager: MemoryManager;

  constructor(
    logger: Logger,
    intelligenceEngine: IntelligenceEngine,
    trainingManager: TrainingManager,
    memoryManager: MemoryManager
  ) {
    this.logger = logger;
    this.intelligenceEngine = intelligenceEngine;
    this.trainingManager = trainingManager;
    this.memoryManager = memoryManager;
  }

  public async handleTrainingCommand(options: TrainingCommandOptions = {}): Promise<void> {
    if (options.status) {
      this.intelligenceEngine.displayIntelligenceStatus();
      return;
    }

    if (options.analyze) {
      await this.intelligenceEngine.analyzePerformanceTrends();
      return;
    }

    if (options.report) {
      const reportPath = await this.intelligenceEngine.generateIntelligenceReport();
      this.logger.success(`📊 Intelligence report generated: ${reportPath}`);
      return;
    }

    if (options.continuous) {
      await this.startContinuousTraining();
      return;
    }

    if (options.auto) {
      await this.startAutoTraining();
      return;
    }

    if (options.goal) {
      await this.setLearningGoal(options.goal);
      return;
    }

    // Show interactive training menu
    await this.showTrainingMenu();
  }

  private async showTrainingMenu(): Promise<void> {
    const choices = [
      '🧠 Start Intelligence Training',
      '📊 Analyze Performance Trends',
      '🎯 Set Learning Goals',
      '📈 View Intelligence Status',
      '🔄 Start Continuous Learning',
      '🤖 Auto-Training Mode',
      '📋 Generate Intelligence Report',
      '🔍 Analyze Recent Experiences',
      '⚙️ Configure Training Settings',
      '❌ Exit'
    ];

    const { action } = await inquirer.prompt([
      {
        type: 'list',
        name: 'action',
        message: '🎓 What would you like to do?',
        choices
      }
    ]);

    switch (action) {
      case '🧠 Start Intelligence Training':
        await this.startIntelligenceTraining();
        break;
      case '📊 Analyze Performance Trends':
        await this.intelligenceEngine.analyzePerformanceTrends();
        break;
      case '🎯 Set Learning Goals':
        await this.interactiveSetLearningGoal();
        break;
      case '📈 View Intelligence Status':
        this.intelligenceEngine.displayIntelligenceStatus();
        break;
      case '🔄 Start Continuous Learning':
        await this.startContinuousTraining();
        break;
      case '🤖 Auto-Training Mode':
        await this.startAutoTraining();
        break;
      case '📋 Generate Intelligence Report':
        await this.intelligenceEngine.generateIntelligenceReport();
        break;
      case '🔍 Analyze Recent Experiences':
        await this.analyzeRecentExperiences();
        break;
      case '⚙️ Configure Training Settings':
        await this.configureTrainingSettings();
        break;
      case '❌ Exit':
        this.logger.info('👋 Training session ended');
        break;
    }
  }

  private async startIntelligenceTraining(): Promise<void> {
    this.logger.info('🧠 Starting intelligence training session...');
    
    const sessionId = await this.trainingManager.startTrainingSession();
    
    try {
      // Step 1: Analyze recent experiences
      this.logger.info('📚 Analyzing recent experiences...');
      await this.intelligenceEngine.startContinuousLearning();
      
      // Step 2: Adapt behavior based on context
      this.logger.info('🎯 Adapting behavior based on context...');
      await this.intelligenceEngine.adaptBehaviorBasedOnContext();
      
      // Step 3: Train from interactions
      this.logger.info('🔄 Training from user interactions...');
      await this.trainingManager.trainFromInteractions();
      
      // Step 4: Generate training from codebase
      this.logger.info('🔍 Generating training from codebase...');
      await this.trainingManager.generateTrainingFromCodebase();
      
      // Step 5: Optimize model
      this.logger.info('⚡ Optimizing model...');
      await this.trainingManager.optimizeModel();
      
      this.logger.success('✅ Intelligence training completed successfully!');
      
      // Show improvement summary
      await this.showTrainingResults();
      
    } catch (error) {
      this.logger.error(`❌ Training failed: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      await this.trainingManager.endTrainingSession(sessionId);
    }
  }

  private async startContinuousTraining(): Promise<void> {
    this.logger.info('🔄 Starting continuous learning mode...');
    
    const { confirm } = await inquirer.prompt([
      {
        type: 'confirm',
        name: 'confirm',
        message: 'Enable continuous learning? (Agent will learn from every interaction)',
        default: true
      }
    ]);

    if (confirm) {
      await this.intelligenceEngine.startContinuousLearning();
      this.logger.success('✅ Continuous learning enabled');
    } else {
      this.logger.info('Continuous learning cancelled');
    }
  }

  private async startAutoTraining(): Promise<void> {
    this.logger.info('🤖 Starting auto-training mode...');
    
    const { confirm } = await inquirer.prompt([
      {
        type: 'confirm',
        name: 'confirm',
        message: chalk.yellow('⚠️  Auto-training will run autonomously. Continue?'),
        default: false
      }
    ]);

    if (!confirm) {
      this.logger.info('Auto-training cancelled');
      return;
    }

    // Run multiple training cycles
    for (let i = 1; i <= 3; i++) {
      this.logger.info(`🔄 Auto-training cycle ${i}/3`);
      await this.intelligenceEngine.startContinuousLearning();
      await new Promise(resolve => setTimeout(resolve, 1000)); // Brief pause
    }

    this.logger.success('✅ Auto-training completed');
    this.intelligenceEngine.displayIntelligenceStatus();
  }

  private async interactiveSetLearningGoal(): Promise<void> {
    const answers = await inquirer.prompt([
      {
        type: 'input',
        name: 'description',
        message: 'Describe your learning goal:',
        validate: (input) => input.trim() !== '' || 'Please provide a description'
      },
      {
        type: 'list',
        name: 'metric',
        message: 'What metric should be improved?',
        choices: [
          'successRate',
          'averageTaskCompletionTime',
          'learningVelocity',
          'adaptabilityScore'
        ]
      },
      {
        type: 'number',
        name: 'targetValue',
        message: 'Target value:',
        validate: (input) => !isNaN(input) || 'Please enter a valid number'
      },
      {
        type: 'list',
        name: 'priority',
        message: 'Priority level:',
        choices: ['low', 'medium', 'high'],
        default: 'medium'
      }
    ]);

    await this.intelligenceEngine.setLearningGoal(
      answers.description,
      answers.metric,
      answers.targetValue,
      answers.priority
    );
  }

  private async setLearningGoal(goalDescription: string): Promise<void> {
    // Parse goal from command line format
    const parts = goalDescription.split(':');
    if (parts.length < 3) {
      this.logger.error('Goal format: "description:metric:targetValue"');
      return;
    }

    const [description, metric, targetValue] = parts;
    await this.intelligenceEngine.setLearningGoal(
      description,
      metric,
      parseFloat(targetValue)
    );
  }

  private async analyzeRecentExperiences(): Promise<void> {
    this.logger.info('🔍 Analyzing recent experiences...');
    
    const experiences = this.memoryManager.getRecentExperiences(20);
    
    if (experiences.length === 0) {
      this.logger.info('No recent experiences found');
      return;
    }

    const table = new Table({
      head: ['Task', 'Outcome', 'Time', 'Action Type'],
      colWidths: [40, 12, 12, 15]
    });

    experiences.forEach(exp => {
      const outcomeColor = exp.outcome === 'success' ? chalk.green : chalk.red;
      table.push([
        exp.task.substring(0, 35) + (exp.task.length > 35 ? '...' : ''),
        outcomeColor(exp.outcome),
        exp.timestamp.toLocaleTimeString(),
        exp.action?.type || 'N/A'
      ]);
    });

    console.log('\n📊 Recent Experiences:');
    console.log(table.toString());

    const successRate = (experiences.filter(e => e.outcome === 'success').length / experiences.length) * 100;
    this.logger.info(`📈 Recent success rate: ${successRate.toFixed(1)}%`);
  }

  private async showTrainingResults(): Promise<void> {
    this.logger.info('\n📊 Training Results:');
    this.intelligenceEngine.displayIntelligenceStatus();
  }

  private async configureTrainingSettings(): Promise<void> {
    this.logger.info('⚙️ Training settings configuration not yet implemented');
    // This would allow users to configure training parameters
  }
}
