/*!
* focus-trap 6.9.4
* @license MIT, https://github.com/focus-trap/focus-trap/blob/master/LICENSE
*/
"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("tabbable");function t(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function n(e){for(var n=1;n<arguments.length;n++){var r=null!=arguments[n]?arguments[n]:{};n%2?t(Object(r),!0).forEach((function(t){a(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):t(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function a(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var r,o=(r=[],{activateTrap:function(e){if(r.length>0){var t=r[r.length-1];t!==e&&t.pause()}var n=r.indexOf(e);-1===n||r.splice(n,1),r.push(e)},deactivateTrap:function(e){var t=r.indexOf(e);-1!==t&&r.splice(t,1),r.length>0&&r[r.length-1].unpause()}}),i=function(e){return setTimeout(e,0)},c=function(e,t){var n=-1;return e.every((function(e,a){return!t(e)||(n=a,!1)})),n},u=function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),a=1;a<t;a++)n[a-1]=arguments[a];return"function"==typeof e?e.apply(void 0,n):e},s=function(e){return e.target.shadowRoot&&"function"==typeof e.composedPath?e.composedPath()[0]:e.target};exports.createFocusTrap=function(t,a){var r,l=(null==a?void 0:a.document)||document,b=n({returnFocusOnDeactivate:!0,escapeDeactivates:!0,delayInitialFocus:!0},a),f={containers:[],containerGroups:[],tabbableGroups:[],nodeFocusedBeforeActivation:null,mostRecentlyFocusedNode:null,active:!1,paused:!1,delayInitialFocusTimer:void 0},v=function(e,t,n){return e&&void 0!==e[t]?e[t]:b[n||t]},d=function(e){return f.containerGroups.findIndex((function(t){var n=t.container,a=t.tabbableNodes;return n.contains(e)||a.find((function(t){return t===e}))}))},p=function(e){var t=b[e];if("function"==typeof t){for(var n=arguments.length,a=new Array(n>1?n-1:0),r=1;r<n;r++)a[r-1]=arguments[r];t=t.apply(void 0,a)}if(!0===t&&(t=void 0),!t){if(void 0===t||!1===t)return t;throw new Error("`".concat(e,"` was specified but was not a node, or did not return a node"))}var o=t;if("string"==typeof t&&!(o=l.querySelector(t)))throw new Error("`".concat(e,"` as selector refers to no known node"));return o},h=function(){var e=p("initialFocus");if(!1===e)return!1;if(void 0===e)if(d(l.activeElement)>=0)e=l.activeElement;else{var t=f.tabbableGroups[0];e=t&&t.firstTabbableNode||p("fallbackFocus")}if(!e)throw new Error("Your focus-trap needs to have at least one focusable element");return e},y=function(){if(f.containerGroups=f.containers.map((function(t){var n=e.tabbable(t,b.tabbableOptions),a=e.focusable(t,b.tabbableOptions);return{container:t,tabbableNodes:n,focusableNodes:a,firstTabbableNode:n.length>0?n[0]:null,lastTabbableNode:n.length>0?n[n.length-1]:null,nextTabbableNode:function(t){var n=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],r=a.findIndex((function(e){return e===t}));if(!(r<0))return n?a.slice(r+1).find((function(t){return e.isTabbable(t,b.tabbableOptions)})):a.slice(0,r).reverse().find((function(t){return e.isTabbable(t,b.tabbableOptions)}))}}})),f.tabbableGroups=f.containerGroups.filter((function(e){return e.tabbableNodes.length>0})),f.tabbableGroups.length<=0&&!p("fallbackFocus"))throw new Error("Your focus-trap must have at least one container with at least one tabbable node in it at all times")},m=function e(t){!1!==t&&t!==l.activeElement&&(t&&t.focus?(t.focus({preventScroll:!!b.preventScroll}),f.mostRecentlyFocusedNode=t,function(e){return e.tagName&&"input"===e.tagName.toLowerCase()&&"function"==typeof e.select}(t)&&t.select()):e(h()))},O=function(e){var t=p("setReturnFocus",e);return t||!1!==t&&e},g=function(t){var n=s(t);d(n)>=0||(u(b.clickOutsideDeactivates,t)?r.deactivate({returnFocus:b.returnFocusOnDeactivate&&!e.isFocusable(n,b.tabbableOptions)}):u(b.allowOutsideClick,t)||t.preventDefault())},F=function(e){var t=s(e),n=d(t)>=0;n||t instanceof Document?n&&(f.mostRecentlyFocusedNode=t):(e.stopImmediatePropagation(),m(f.mostRecentlyFocusedNode||h()))},w=function(t){if(function(e){return"Escape"===e.key||"Esc"===e.key||27===e.keyCode}(t)&&!1!==u(b.escapeDeactivates,t))return t.preventDefault(),void r.deactivate();(function(e){return"Tab"===e.key||9===e.keyCode})(t)&&function(t){var n=s(t);y();var a=null;if(f.tabbableGroups.length>0){var r=d(n),o=r>=0?f.containerGroups[r]:void 0;if(r<0)a=t.shiftKey?f.tabbableGroups[f.tabbableGroups.length-1].lastTabbableNode:f.tabbableGroups[0].firstTabbableNode;else if(t.shiftKey){var i=c(f.tabbableGroups,(function(e){var t=e.firstTabbableNode;return n===t}));if(i<0&&(o.container===n||e.isFocusable(n,b.tabbableOptions)&&!e.isTabbable(n,b.tabbableOptions)&&!o.nextTabbableNode(n,!1))&&(i=r),i>=0){var u=0===i?f.tabbableGroups.length-1:i-1;a=f.tabbableGroups[u].lastTabbableNode}}else{var l=c(f.tabbableGroups,(function(e){var t=e.lastTabbableNode;return n===t}));if(l<0&&(o.container===n||e.isFocusable(n,b.tabbableOptions)&&!e.isTabbable(n,b.tabbableOptions)&&!o.nextTabbableNode(n))&&(l=r),l>=0){var v=l===f.tabbableGroups.length-1?0:l+1;a=f.tabbableGroups[v].firstTabbableNode}}}else a=p("fallbackFocus");a&&(t.preventDefault(),m(a))}(t)},T=function(e){var t=s(e);d(t)>=0||u(b.clickOutsideDeactivates,e)||u(b.allowOutsideClick,e)||(e.preventDefault(),e.stopImmediatePropagation())},E=function(){if(f.active)return o.activateTrap(r),f.delayInitialFocusTimer=b.delayInitialFocus?i((function(){m(h())})):m(h()),l.addEventListener("focusin",F,!0),l.addEventListener("mousedown",g,{capture:!0,passive:!1}),l.addEventListener("touchstart",g,{capture:!0,passive:!1}),l.addEventListener("click",T,{capture:!0,passive:!1}),l.addEventListener("keydown",w,{capture:!0,passive:!1}),r},k=function(){if(f.active)return l.removeEventListener("focusin",F,!0),l.removeEventListener("mousedown",g,!0),l.removeEventListener("touchstart",g,!0),l.removeEventListener("click",T,!0),l.removeEventListener("keydown",w,!0),r};return(r={get active(){return f.active},get paused(){return f.paused},activate:function(e){if(f.active)return this;var t=v(e,"onActivate"),n=v(e,"onPostActivate"),a=v(e,"checkCanFocusTrap");a||y(),f.active=!0,f.paused=!1,f.nodeFocusedBeforeActivation=l.activeElement,t&&t();var r=function(){a&&y(),E(),n&&n()};return a?(a(f.containers.concat()).then(r,r),this):(r(),this)},deactivate:function(e){if(!f.active)return this;var t=n({onDeactivate:b.onDeactivate,onPostDeactivate:b.onPostDeactivate,checkCanReturnFocus:b.checkCanReturnFocus},e);clearTimeout(f.delayInitialFocusTimer),f.delayInitialFocusTimer=void 0,k(),f.active=!1,f.paused=!1,o.deactivateTrap(r);var a=v(t,"onDeactivate"),c=v(t,"onPostDeactivate"),u=v(t,"checkCanReturnFocus"),s=v(t,"returnFocus","returnFocusOnDeactivate");a&&a();var l=function(){i((function(){s&&m(O(f.nodeFocusedBeforeActivation)),c&&c()}))};return s&&u?(u(O(f.nodeFocusedBeforeActivation)).then(l,l),this):(l(),this)},pause:function(){return f.paused||!f.active||(f.paused=!0,k()),this},unpause:function(){return f.paused&&f.active?(f.paused=!1,y(),E(),this):this},updateContainerElements:function(e){var t=[].concat(e).filter(Boolean);return f.containers=t.map((function(e){return"string"==typeof e?l.querySelector(e):e})),f.active&&y(),this}}).updateContainerElements(t),r};
//# sourceMappingURL=focus-trap.min.js.map
