{"version": 3, "file": "focus-trap.js", "sources": ["../index.js"], "sourcesContent": ["import { tabbable, focusable, isFocusable, isTabbable } from 'tabbable';\n\nconst activeFocusTraps = (function () {\n  const trapQueue = [];\n  return {\n    activateTrap(trap) {\n      if (trapQueue.length > 0) {\n        const activeTrap = trapQueue[trapQueue.length - 1];\n        if (activeTrap !== trap) {\n          activeTrap.pause();\n        }\n      }\n\n      const trapIndex = trapQueue.indexOf(trap);\n      if (trapIndex === -1) {\n        trapQueue.push(trap);\n      } else {\n        // move this existing trap to the front of the queue\n        trapQueue.splice(trapIndex, 1);\n        trapQueue.push(trap);\n      }\n    },\n\n    deactivateTrap(trap) {\n      const trapIndex = trapQueue.indexOf(trap);\n      if (trapIndex !== -1) {\n        trapQueue.splice(trapIndex, 1);\n      }\n\n      if (trapQueue.length > 0) {\n        trapQueue[trapQueue.length - 1].unpause();\n      }\n    },\n  };\n})();\n\nconst isSelectableInput = function (node) {\n  return (\n    node.tagName &&\n    node.tagName.toLowerCase() === 'input' &&\n    typeof node.select === 'function'\n  );\n};\n\nconst isEscapeEvent = function (e) {\n  return e.key === 'Escape' || e.key === 'Esc' || e.keyCode === 27;\n};\n\nconst isTabEvent = function (e) {\n  return e.key === 'Tab' || e.keyCode === 9;\n};\n\nconst delay = function (fn) {\n  return setTimeout(fn, 0);\n};\n\n// Array.find/findIndex() are not supported on IE; this replicates enough\n//  of Array.findIndex() for our needs\nconst findIndex = function (arr, fn) {\n  let idx = -1;\n\n  arr.every(function (value, i) {\n    if (fn(value)) {\n      idx = i;\n      return false; // break\n    }\n\n    return true; // next\n  });\n\n  return idx;\n};\n\n/**\n * Get an option's value when it could be a plain value, or a handler that provides\n *  the value.\n * @param {*} value Option's value to check.\n * @param {...*} [params] Any parameters to pass to the handler, if `value` is a function.\n * @returns {*} The `value`, or the handler's returned value.\n */\nconst valueOrHandler = function (value, ...params) {\n  return typeof value === 'function' ? value(...params) : value;\n};\n\nconst getActualTarget = function (event) {\n  // NOTE: If the trap is _inside_ a shadow DOM, event.target will always be the\n  //  shadow host. However, event.target.composedPath() will be an array of\n  //  nodes \"clicked\" from inner-most (the actual element inside the shadow) to\n  //  outer-most (the host HTML document). If we have access to composedPath(),\n  //  then use its first element; otherwise, fall back to event.target (and\n  //  this only works for an _open_ shadow DOM; otherwise,\n  //  composedPath()[0] === event.target always).\n  return event.target.shadowRoot && typeof event.composedPath === 'function'\n    ? event.composedPath()[0]\n    : event.target;\n};\n\nconst createFocusTrap = function (elements, userOptions) {\n  // SSR: a live trap shouldn't be created in this type of environment so this\n  //  should be safe code to execute if the `document` option isn't specified\n  const doc = userOptions?.document || document;\n\n  const config = {\n    returnFocusOnDeactivate: true,\n    escapeDeactivates: true,\n    delayInitialFocus: true,\n    ...userOptions,\n  };\n\n  const state = {\n    // containers given to createFocusTrap()\n    // @type {Array<HTMLElement>}\n    containers: [],\n\n    // list of objects identifying tabbable nodes in `containers` in the trap\n    // NOTE: it's possible that a group has no tabbable nodes if nodes get removed while the trap\n    //  is active, but the trap should never get to a state where there isn't at least one group\n    //  with at least one tabbable node in it (that would lead to an error condition that would\n    //  result in an error being thrown)\n    // @type {Array<{\n    //   container: HTMLElement,\n    //   tabbableNodes: Array<HTMLElement>, // empty if none\n    //   focusableNodes: Array<HTMLElement>, // empty if none\n    //   firstTabbableNode: HTMLElement|null,\n    //   lastTabbableNode: HTMLElement|null,\n    //   nextTabbableNode: (node: HTMLElement, forward: boolean) => HTMLElement|undefined\n    // }>}\n    containerGroups: [], // same order/length as `containers` list\n\n    // references to objects in `containerGroups`, but only those that actually have\n    //  tabbable nodes in them\n    // NOTE: same order as `containers` and `containerGroups`, but __not necessarily__\n    //  the same length\n    tabbableGroups: [],\n\n    nodeFocusedBeforeActivation: null,\n    mostRecentlyFocusedNode: null,\n    active: false,\n    paused: false,\n\n    // timer ID for when delayInitialFocus is true and initial focus in this trap\n    //  has been delayed during activation\n    delayInitialFocusTimer: undefined,\n  };\n\n  let trap; // eslint-disable-line prefer-const -- some private functions reference it, and its methods reference private functions, so we must declare here and define later\n\n  /**\n   * Gets a configuration option value.\n   * @param {Object|undefined} configOverrideOptions If true, and option is defined in this set,\n   *  value will be taken from this object. Otherwise, value will be taken from base configuration.\n   * @param {string} optionName Name of the option whose value is sought.\n   * @param {string|undefined} [configOptionName] Name of option to use __instead of__ `optionName`\n   *  IIF `configOverrideOptions` is not defined. Otherwise, `optionName` is used.\n   */\n  const getOption = (configOverrideOptions, optionName, configOptionName) => {\n    return configOverrideOptions &&\n      configOverrideOptions[optionName] !== undefined\n      ? configOverrideOptions[optionName]\n      : config[configOptionName || optionName];\n  };\n\n  /**\n   * Finds the index of the container that contains the element.\n   * @param {HTMLElement} element\n   * @returns {number} Index of the container in either `state.containers` or\n   *  `state.containerGroups` (the order/length of these lists are the same); -1\n   *  if the element isn't found.\n   */\n  const findContainerIndex = function (element) {\n    // NOTE: search `containerGroups` because it's possible a group contains no tabbable\n    //  nodes, but still contains focusable nodes (e.g. if they all have `tabindex=-1`)\n    //  and we still need to find the element in there\n    return state.containerGroups.findIndex(\n      ({ container, tabbableNodes }) =>\n        container.contains(element) ||\n        // fall back to explicit tabbable search which will take into consideration any\n        //  web components if the `tabbableOptions.getShadowRoot` option was used for\n        //  the trap, enabling shadow DOM support in tabbable (`Node.contains()` doesn't\n        //  look inside web components even if open)\n        tabbableNodes.find((node) => node === element)\n    );\n  };\n\n  /**\n   * Gets the node for the given option, which is expected to be an option that\n   *  can be either a DOM node, a string that is a selector to get a node, `false`\n   *  (if a node is explicitly NOT given), or a function that returns any of these\n   *  values.\n   * @param {string} optionName\n   * @returns {undefined | false | HTMLElement | SVGElement} Returns\n   *  `undefined` if the option is not specified; `false` if the option\n   *  resolved to `false` (node explicitly not given); otherwise, the resolved\n   *  DOM node.\n   * @throws {Error} If the option is set, not `false`, and is not, or does not\n   *  resolve to a node.\n   */\n  const getNodeForOption = function (optionName, ...params) {\n    let optionValue = config[optionName];\n\n    if (typeof optionValue === 'function') {\n      optionValue = optionValue(...params);\n    }\n\n    if (optionValue === true) {\n      optionValue = undefined; // use default value\n    }\n\n    if (!optionValue) {\n      if (optionValue === undefined || optionValue === false) {\n        return optionValue;\n      }\n      // else, empty string (invalid), null (invalid), 0 (invalid)\n\n      throw new Error(\n        `\\`${optionName}\\` was specified but was not a node, or did not return a node`\n      );\n    }\n\n    let node = optionValue; // could be HTMLElement, SVGElement, or non-empty string at this point\n\n    if (typeof optionValue === 'string') {\n      node = doc.querySelector(optionValue); // resolve to node, or null if fails\n      if (!node) {\n        throw new Error(\n          `\\`${optionName}\\` as selector refers to no known node`\n        );\n      }\n    }\n\n    return node;\n  };\n\n  const getInitialFocusNode = function () {\n    let node = getNodeForOption('initialFocus');\n\n    // false explicitly indicates we want no initialFocus at all\n    if (node === false) {\n      return false;\n    }\n\n    if (node === undefined) {\n      // option not specified: use fallback options\n      if (findContainerIndex(doc.activeElement) >= 0) {\n        node = doc.activeElement;\n      } else {\n        const firstTabbableGroup = state.tabbableGroups[0];\n        const firstTabbableNode =\n          firstTabbableGroup && firstTabbableGroup.firstTabbableNode;\n\n        // NOTE: `fallbackFocus` option function cannot return `false` (not supported)\n        node = firstTabbableNode || getNodeForOption('fallbackFocus');\n      }\n    }\n\n    if (!node) {\n      throw new Error(\n        'Your focus-trap needs to have at least one focusable element'\n      );\n    }\n\n    return node;\n  };\n\n  const updateTabbableNodes = function () {\n    state.containerGroups = state.containers.map((container) => {\n      const tabbableNodes = tabbable(container, config.tabbableOptions);\n\n      // NOTE: if we have tabbable nodes, we must have focusable nodes; focusable nodes\n      //  are a superset of tabbable nodes\n      const focusableNodes = focusable(container, config.tabbableOptions);\n\n      return {\n        container,\n        tabbableNodes,\n        focusableNodes,\n        firstTabbableNode: tabbableNodes.length > 0 ? tabbableNodes[0] : null,\n        lastTabbableNode:\n          tabbableNodes.length > 0\n            ? tabbableNodes[tabbableNodes.length - 1]\n            : null,\n\n        /**\n         * Finds the __tabbable__ node that follows the given node in the specified direction,\n         *  in this container, if any.\n         * @param {HTMLElement} node\n         * @param {boolean} [forward] True if going in forward tab order; false if going\n         *  in reverse.\n         * @returns {HTMLElement|undefined} The next tabbable node, if any.\n         */\n        nextTabbableNode(node, forward = true) {\n          // NOTE: If tabindex is positive (in order to manipulate the tab order separate\n          //  from the DOM order), this __will not work__ because the list of focusableNodes,\n          //  while it contains tabbable nodes, does not sort its nodes in any order other\n          //  than DOM order, because it can't: Where would you place focusable (but not\n          //  tabbable) nodes in that order? They have no order, because they aren't tabbale...\n          // Support for positive tabindex is already broken and hard to manage (possibly\n          //  not supportable, TBD), so this isn't going to make things worse than they\n          //  already are, and at least makes things better for the majority of cases where\n          //  tabindex is either 0/unset or negative.\n          // FYI, positive tabindex issue: https://github.com/focus-trap/focus-trap/issues/375\n          const nodeIdx = focusableNodes.findIndex((n) => n === node);\n          if (nodeIdx < 0) {\n            return undefined;\n          }\n\n          if (forward) {\n            return focusableNodes\n              .slice(nodeIdx + 1)\n              .find((n) => isTabbable(n, config.tabbableOptions));\n          }\n\n          return focusableNodes\n            .slice(0, nodeIdx)\n            .reverse()\n            .find((n) => isTabbable(n, config.tabbableOptions));\n        },\n      };\n    });\n\n    state.tabbableGroups = state.containerGroups.filter(\n      (group) => group.tabbableNodes.length > 0\n    );\n\n    // throw if no groups have tabbable nodes and we don't have a fallback focus node either\n    if (\n      state.tabbableGroups.length <= 0 &&\n      !getNodeForOption('fallbackFocus') // returning false not supported for this option\n    ) {\n      throw new Error(\n        'Your focus-trap must have at least one container with at least one tabbable node in it at all times'\n      );\n    }\n  };\n\n  const tryFocus = function (node) {\n    if (node === false) {\n      return;\n    }\n\n    if (node === doc.activeElement) {\n      return;\n    }\n\n    if (!node || !node.focus) {\n      tryFocus(getInitialFocusNode());\n      return;\n    }\n\n    node.focus({ preventScroll: !!config.preventScroll });\n    state.mostRecentlyFocusedNode = node;\n\n    if (isSelectableInput(node)) {\n      node.select();\n    }\n  };\n\n  const getReturnFocusNode = function (previousActiveElement) {\n    const node = getNodeForOption('setReturnFocus', previousActiveElement);\n    return node ? node : node === false ? false : previousActiveElement;\n  };\n\n  // This needs to be done on mousedown and touchstart instead of click\n  // so that it precedes the focus event.\n  const checkPointerDown = function (e) {\n    const target = getActualTarget(e);\n\n    if (findContainerIndex(target) >= 0) {\n      // allow the click since it ocurred inside the trap\n      return;\n    }\n\n    if (valueOrHandler(config.clickOutsideDeactivates, e)) {\n      // immediately deactivate the trap\n      trap.deactivate({\n        // if, on deactivation, we should return focus to the node originally-focused\n        //  when the trap was activated (or the configured `setReturnFocus` node),\n        //  then assume it's also OK to return focus to the outside node that was\n        //  just clicked, causing deactivation, as long as that node is focusable;\n        //  if it isn't focusable, then return focus to the original node focused\n        //  on activation (or the configured `setReturnFocus` node)\n        // NOTE: by setting `returnFocus: false`, deactivate() will do nothing,\n        //  which will result in the outside click setting focus to the node\n        //  that was clicked, whether it's focusable or not; by setting\n        //  `returnFocus: true`, we'll attempt to re-focus the node originally-focused\n        //  on activation (or the configured `setReturnFocus` node)\n        returnFocus:\n          config.returnFocusOnDeactivate &&\n          !isFocusable(target, config.tabbableOptions),\n      });\n      return;\n    }\n\n    // This is needed for mobile devices.\n    // (If we'll only let `click` events through,\n    // then on mobile they will be blocked anyways if `touchstart` is blocked.)\n    if (valueOrHandler(config.allowOutsideClick, e)) {\n      // allow the click outside the trap to take place\n      return;\n    }\n\n    // otherwise, prevent the click\n    e.preventDefault();\n  };\n\n  // In case focus escapes the trap for some strange reason, pull it back in.\n  const checkFocusIn = function (e) {\n    const target = getActualTarget(e);\n    const targetContained = findContainerIndex(target) >= 0;\n\n    // In Firefox when you Tab out of an iframe the Document is briefly focused.\n    if (targetContained || target instanceof Document) {\n      if (targetContained) {\n        state.mostRecentlyFocusedNode = target;\n      }\n    } else {\n      // escaped! pull it back in to where it just left\n      e.stopImmediatePropagation();\n      tryFocus(state.mostRecentlyFocusedNode || getInitialFocusNode());\n    }\n  };\n\n  // Hijack Tab events on the first and last focusable nodes of the trap,\n  // in order to prevent focus from escaping. If it escapes for even a\n  // moment it can end up scrolling the page and causing confusion so we\n  // kind of need to capture the action at the keydown phase.\n  const checkTab = function (e) {\n    const target = getActualTarget(e);\n    updateTabbableNodes();\n\n    let destinationNode = null;\n\n    if (state.tabbableGroups.length > 0) {\n      // make sure the target is actually contained in a group\n      // NOTE: the target may also be the container itself if it's focusable\n      //  with tabIndex='-1' and was given initial focus\n      const containerIndex = findContainerIndex(target);\n      const containerGroup =\n        containerIndex >= 0 ? state.containerGroups[containerIndex] : undefined;\n\n      if (containerIndex < 0) {\n        // target not found in any group: quite possible focus has escaped the trap,\n        //  so bring it back in to...\n        if (e.shiftKey) {\n          // ...the last node in the last group\n          destinationNode =\n            state.tabbableGroups[state.tabbableGroups.length - 1]\n              .lastTabbableNode;\n        } else {\n          // ...the first node in the first group\n          destinationNode = state.tabbableGroups[0].firstTabbableNode;\n        }\n      } else if (e.shiftKey) {\n        // REVERSE\n\n        // is the target the first tabbable node in a group?\n        let startOfGroupIndex = findIndex(\n          state.tabbableGroups,\n          ({ firstTabbableNode }) => target === firstTabbableNode\n        );\n\n        if (\n          startOfGroupIndex < 0 &&\n          (containerGroup.container === target ||\n            (isFocusable(target, config.tabbableOptions) &&\n              !isTabbable(target, config.tabbableOptions) &&\n              !containerGroup.nextTabbableNode(target, false)))\n        ) {\n          // an exception case where the target is either the container itself, or\n          //  a non-tabbable node that was given focus (i.e. tabindex is negative\n          //  and user clicked on it or node was programmatically given focus)\n          //  and is not followed by any other tabbable node, in which\n          //  case, we should handle shift+tab as if focus were on the container's\n          //  first tabbable node, and go to the last tabbable node of the LAST group\n          startOfGroupIndex = containerIndex;\n        }\n\n        if (startOfGroupIndex >= 0) {\n          // YES: then shift+tab should go to the last tabbable node in the\n          //  previous group (and wrap around to the last tabbable node of\n          //  the LAST group if it's the first tabbable node of the FIRST group)\n          const destinationGroupIndex =\n            startOfGroupIndex === 0\n              ? state.tabbableGroups.length - 1\n              : startOfGroupIndex - 1;\n\n          const destinationGroup = state.tabbableGroups[destinationGroupIndex];\n          destinationNode = destinationGroup.lastTabbableNode;\n        }\n      } else {\n        // FORWARD\n\n        // is the target the last tabbable node in a group?\n        let lastOfGroupIndex = findIndex(\n          state.tabbableGroups,\n          ({ lastTabbableNode }) => target === lastTabbableNode\n        );\n\n        if (\n          lastOfGroupIndex < 0 &&\n          (containerGroup.container === target ||\n            (isFocusable(target, config.tabbableOptions) &&\n              !isTabbable(target, config.tabbableOptions) &&\n              !containerGroup.nextTabbableNode(target)))\n        ) {\n          // an exception case where the target is the container itself, or\n          //  a non-tabbable node that was given focus (i.e. tabindex is negative\n          //  and user clicked on it or node was programmatically given focus)\n          //  and is not followed by any other tabbable node, in which\n          //  case, we should handle tab as if focus were on the container's\n          //  last tabbable node, and go to the first tabbable node of the FIRST group\n          lastOfGroupIndex = containerIndex;\n        }\n\n        if (lastOfGroupIndex >= 0) {\n          // YES: then tab should go to the first tabbable node in the next\n          //  group (and wrap around to the first tabbable node of the FIRST\n          //  group if it's the last tabbable node of the LAST group)\n          const destinationGroupIndex =\n            lastOfGroupIndex === state.tabbableGroups.length - 1\n              ? 0\n              : lastOfGroupIndex + 1;\n\n          const destinationGroup = state.tabbableGroups[destinationGroupIndex];\n          destinationNode = destinationGroup.firstTabbableNode;\n        }\n      }\n    } else {\n      // NOTE: the fallbackFocus option does not support returning false to opt-out\n      destinationNode = getNodeForOption('fallbackFocus');\n    }\n\n    if (destinationNode) {\n      e.preventDefault();\n      tryFocus(destinationNode);\n    }\n    // else, let the browser take care of [shift+]tab and move the focus\n  };\n\n  const checkKey = function (e) {\n    if (\n      isEscapeEvent(e) &&\n      valueOrHandler(config.escapeDeactivates, e) !== false\n    ) {\n      e.preventDefault();\n      trap.deactivate();\n      return;\n    }\n\n    if (isTabEvent(e)) {\n      checkTab(e);\n      return;\n    }\n  };\n\n  const checkClick = function (e) {\n    const target = getActualTarget(e);\n\n    if (findContainerIndex(target) >= 0) {\n      return;\n    }\n\n    if (valueOrHandler(config.clickOutsideDeactivates, e)) {\n      return;\n    }\n\n    if (valueOrHandler(config.allowOutsideClick, e)) {\n      return;\n    }\n\n    e.preventDefault();\n    e.stopImmediatePropagation();\n  };\n\n  //\n  // EVENT LISTENERS\n  //\n\n  const addListeners = function () {\n    if (!state.active) {\n      return;\n    }\n\n    // There can be only one listening focus trap at a time\n    activeFocusTraps.activateTrap(trap);\n\n    // Delay ensures that the focused element doesn't capture the event\n    // that caused the focus trap activation.\n    state.delayInitialFocusTimer = config.delayInitialFocus\n      ? delay(function () {\n          tryFocus(getInitialFocusNode());\n        })\n      : tryFocus(getInitialFocusNode());\n\n    doc.addEventListener('focusin', checkFocusIn, true);\n    doc.addEventListener('mousedown', checkPointerDown, {\n      capture: true,\n      passive: false,\n    });\n    doc.addEventListener('touchstart', checkPointerDown, {\n      capture: true,\n      passive: false,\n    });\n    doc.addEventListener('click', checkClick, {\n      capture: true,\n      passive: false,\n    });\n    doc.addEventListener('keydown', checkKey, {\n      capture: true,\n      passive: false,\n    });\n\n    return trap;\n  };\n\n  const removeListeners = function () {\n    if (!state.active) {\n      return;\n    }\n\n    doc.removeEventListener('focusin', checkFocusIn, true);\n    doc.removeEventListener('mousedown', checkPointerDown, true);\n    doc.removeEventListener('touchstart', checkPointerDown, true);\n    doc.removeEventListener('click', checkClick, true);\n    doc.removeEventListener('keydown', checkKey, true);\n\n    return trap;\n  };\n\n  //\n  // TRAP DEFINITION\n  //\n\n  trap = {\n    get active() {\n      return state.active;\n    },\n\n    get paused() {\n      return state.paused;\n    },\n\n    activate(activateOptions) {\n      if (state.active) {\n        return this;\n      }\n\n      const onActivate = getOption(activateOptions, 'onActivate');\n      const onPostActivate = getOption(activateOptions, 'onPostActivate');\n      const checkCanFocusTrap = getOption(activateOptions, 'checkCanFocusTrap');\n\n      if (!checkCanFocusTrap) {\n        updateTabbableNodes();\n      }\n\n      state.active = true;\n      state.paused = false;\n      state.nodeFocusedBeforeActivation = doc.activeElement;\n\n      if (onActivate) {\n        onActivate();\n      }\n\n      const finishActivation = () => {\n        if (checkCanFocusTrap) {\n          updateTabbableNodes();\n        }\n        addListeners();\n        if (onPostActivate) {\n          onPostActivate();\n        }\n      };\n\n      if (checkCanFocusTrap) {\n        checkCanFocusTrap(state.containers.concat()).then(\n          finishActivation,\n          finishActivation\n        );\n        return this;\n      }\n\n      finishActivation();\n      return this;\n    },\n\n    deactivate(deactivateOptions) {\n      if (!state.active) {\n        return this;\n      }\n\n      const options = {\n        onDeactivate: config.onDeactivate,\n        onPostDeactivate: config.onPostDeactivate,\n        checkCanReturnFocus: config.checkCanReturnFocus,\n        ...deactivateOptions,\n      };\n\n      clearTimeout(state.delayInitialFocusTimer); // noop if undefined\n      state.delayInitialFocusTimer = undefined;\n\n      removeListeners();\n      state.active = false;\n      state.paused = false;\n\n      activeFocusTraps.deactivateTrap(trap);\n\n      const onDeactivate = getOption(options, 'onDeactivate');\n      const onPostDeactivate = getOption(options, 'onPostDeactivate');\n      const checkCanReturnFocus = getOption(options, 'checkCanReturnFocus');\n      const returnFocus = getOption(\n        options,\n        'returnFocus',\n        'returnFocusOnDeactivate'\n      );\n\n      if (onDeactivate) {\n        onDeactivate();\n      }\n\n      const finishDeactivation = () => {\n        delay(() => {\n          if (returnFocus) {\n            tryFocus(getReturnFocusNode(state.nodeFocusedBeforeActivation));\n          }\n          if (onPostDeactivate) {\n            onPostDeactivate();\n          }\n        });\n      };\n\n      if (returnFocus && checkCanReturnFocus) {\n        checkCanReturnFocus(\n          getReturnFocusNode(state.nodeFocusedBeforeActivation)\n        ).then(finishDeactivation, finishDeactivation);\n        return this;\n      }\n\n      finishDeactivation();\n      return this;\n    },\n\n    pause() {\n      if (state.paused || !state.active) {\n        return this;\n      }\n\n      state.paused = true;\n      removeListeners();\n\n      return this;\n    },\n\n    unpause() {\n      if (!state.paused || !state.active) {\n        return this;\n      }\n\n      state.paused = false;\n      updateTabbableNodes();\n      addListeners();\n\n      return this;\n    },\n\n    updateContainerElements(containerElements) {\n      const elementsAsArray = [].concat(containerElements).filter(Boolean);\n\n      state.containers = elementsAsArray.map((element) =>\n        typeof element === 'string' ? doc.querySelector(element) : element\n      );\n\n      if (state.active) {\n        updateTabbableNodes();\n      }\n\n      return this;\n    },\n  };\n\n  // initialize container elements\n  trap.updateContainerElements(elements);\n\n  return trap;\n};\n\nexport { createFocusTrap };\n"], "names": ["activeFocusTraps", "trapQueue", "activateTrap", "trap", "length", "activeTrap", "pause", "trapIndex", "indexOf", "push", "splice", "deactivateTrap", "unpause", "isSelectableInput", "node", "tagName", "toLowerCase", "select", "isEscapeEvent", "e", "key", "keyCode", "isTabEvent", "delay", "fn", "setTimeout", "findIndex", "arr", "idx", "every", "value", "i", "valueOrHandler", "params", "getActualTarget", "event", "target", "shadowRoot", "<PERSON><PERSON><PERSON>", "createFocusTrap", "elements", "userOptions", "doc", "document", "config", "_objectSpread", "returnFocusOnDeactivate", "escapeDeactivates", "delayInitialFocus", "state", "containers", "containerGroups", "tabbableGroups", "nodeFocusedBeforeActivation", "mostRecentlyFocusedNode", "active", "paused", "delayInitialFocusTimer", "undefined", "getOption", "configOverrideOptions", "optionName", "configOptionName", "findContainerIndex", "element", "container", "tabbableNodes", "contains", "find", "getNodeForOption", "optionValue", "Error", "querySelector", "getInitialFocusNode", "activeElement", "firstTabbableGroup", "firstTabbableNode", "updateTabbableNodes", "map", "tabbable", "tabbableOptions", "focusableNodes", "focusable", "lastTabbableNode", "nextTabbableNode", "forward", "nodeIdx", "n", "slice", "isTabbable", "reverse", "filter", "group", "tryFocus", "focus", "preventScroll", "getReturnFocusNode", "previousActiveElement", "checkPointerDown", "clickOutsideDeactivates", "deactivate", "returnFocus", "isFocusable", "allowOutsideClick", "preventDefault", "checkFocusIn", "targetContained", "Document", "stopImmediatePropagation", "checkTab", "destinationNode", "containerIndex", "containerGroup", "shift<PERSON>ey", "startOfGroupIndex", "destinationGroupIndex", "destinationGroup", "lastOfGroupIndex", "<PERSON><PERSON><PERSON>", "checkClick", "addListeners", "addEventListener", "capture", "passive", "removeListeners", "removeEventListener", "activate", "activateOptions", "onActivate", "onPostActivate", "checkCanFocusTrap", "finishActivation", "concat", "then", "deactivateOptions", "options", "onDeactivate", "onPostDeactivate", "checkCanReturnFocus", "clearTimeout", "finishDeactivation", "updateContainerElements", "containerElements", "elementsAsArray", "Boolean"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,IAAMA,gBAAgB,GAAI,YAAY;EACpC,IAAMC,SAAS,GAAG,EAAlB,CAAA;EACA,OAAO;IACLC,YADK,EAAA,SAAA,YAAA,CACQC,IADR,EACc;AACjB,MAAA,IAAIF,SAAS,CAACG,MAAV,GAAmB,CAAvB,EAA0B;QACxB,IAAMC,UAAU,GAAGJ,SAAS,CAACA,SAAS,CAACG,MAAV,GAAmB,CAApB,CAA5B,CAAA;;QACA,IAAIC,UAAU,KAAKF,IAAnB,EAAyB;AACvBE,UAAAA,UAAU,CAACC,KAAX,EAAA,CAAA;AACD,SAAA;AACF,OAAA;;AAED,MAAA,IAAMC,SAAS,GAAGN,SAAS,CAACO,OAAV,CAAkBL,IAAlB,CAAlB,CAAA;;AACA,MAAA,IAAII,SAAS,KAAK,CAAC,CAAnB,EAAsB;QACpBN,SAAS,CAACQ,IAAV,CAAeN,IAAf,CAAA,CAAA;AACD,OAFD,MAEO;AACL;AACAF,QAAAA,SAAS,CAACS,MAAV,CAAiBH,SAAjB,EAA4B,CAA5B,CAAA,CAAA;QACAN,SAAS,CAACQ,IAAV,CAAeN,IAAf,CAAA,CAAA;AACD,OAAA;KAhBE;IAmBLQ,cAnBK,EAAA,SAAA,cAAA,CAmBUR,IAnBV,EAmBgB;AACnB,MAAA,IAAMI,SAAS,GAAGN,SAAS,CAACO,OAAV,CAAkBL,IAAlB,CAAlB,CAAA;;AACA,MAAA,IAAII,SAAS,KAAK,CAAC,CAAnB,EAAsB;AACpBN,QAAAA,SAAS,CAACS,MAAV,CAAiBH,SAAjB,EAA4B,CAA5B,CAAA,CAAA;AACD,OAAA;;AAED,MAAA,IAAIN,SAAS,CAACG,MAAV,GAAmB,CAAvB,EAA0B;QACxBH,SAAS,CAACA,SAAS,CAACG,MAAV,GAAmB,CAApB,CAAT,CAAgCQ,OAAhC,EAAA,CAAA;AACD,OAAA;AACF,KAAA;GA5BH,CAAA;AA8BD,CAhCwB,EAAzB,CAAA;;AAkCA,IAAMC,iBAAiB,GAAG,SAApBA,iBAAoB,CAAUC,IAAV,EAAgB;AACxC,EAAA,OACEA,IAAI,CAACC,OAAL,IACAD,IAAI,CAACC,OAAL,CAAaC,WAAb,EAAA,KAA+B,OAD/B,IAEA,OAAOF,IAAI,CAACG,MAAZ,KAAuB,UAHzB,CAAA;AAKD,CAND,CAAA;;AAQA,IAAMC,aAAa,GAAG,SAAhBA,aAAgB,CAAUC,CAAV,EAAa;AACjC,EAAA,OAAOA,CAAC,CAACC,GAAF,KAAU,QAAV,IAAsBD,CAAC,CAACC,GAAF,KAAU,KAAhC,IAAyCD,CAAC,CAACE,OAAF,KAAc,EAA9D,CAAA;AACD,CAFD,CAAA;;AAIA,IAAMC,UAAU,GAAG,SAAbA,UAAa,CAAUH,CAAV,EAAa;EAC9B,OAAOA,CAAC,CAACC,GAAF,KAAU,KAAV,IAAmBD,CAAC,CAACE,OAAF,KAAc,CAAxC,CAAA;AACD,CAFD,CAAA;;AAIA,IAAME,KAAK,GAAG,SAARA,KAAQ,CAAUC,EAAV,EAAc;AAC1B,EAAA,OAAOC,UAAU,CAACD,EAAD,EAAK,CAAL,CAAjB,CAAA;AACD,CAFD;AAKA;;;AACA,IAAME,SAAS,GAAG,SAAZA,SAAY,CAAUC,GAAV,EAAeH,EAAf,EAAmB;EACnC,IAAII,GAAG,GAAG,CAAC,CAAX,CAAA;AAEAD,EAAAA,GAAG,CAACE,KAAJ,CAAU,UAAUC,KAAV,EAAiBC,CAAjB,EAAoB;AAC5B,IAAA,IAAIP,EAAE,CAACM,KAAD,CAAN,EAAe;AACbF,MAAAA,GAAG,GAAGG,CAAN,CAAA;MACA,OAAO,KAAP,CAFa;AAGd,KAAA;;IAED,OAAO,IAAP,CAN4B;GAA9B,CAAA,CAAA;AASA,EAAA,OAAOH,GAAP,CAAA;AACD,CAbD,CAAA;AAeA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,IAAMI,cAAc,GAAG,SAAjBA,cAAiB,CAAUF,KAAV,EAA4B;AAAA,EAAA,KAAA,IAAA,IAAA,GAAA,SAAA,CAAA,MAAA,EAARG,MAAQ,GAAA,IAAA,KAAA,CAAA,IAAA,GAAA,CAAA,GAAA,IAAA,GAAA,CAAA,GAAA,CAAA,CAAA,EAAA,IAAA,GAAA,CAAA,EAAA,IAAA,GAAA,IAAA,EAAA,IAAA,EAAA,EAAA;IAARA,MAAQ,CAAA,IAAA,GAAA,CAAA,CAAA,GAAA,SAAA,CAAA,IAAA,CAAA,CAAA;AAAA,GAAA;;EACjD,OAAO,OAAOH,KAAP,KAAiB,UAAjB,GAA8BA,KAAK,CAAA,KAAL,CAASG,KAAAA,CAAAA,EAAAA,MAAT,CAA9B,GAAiDH,KAAxD,CAAA;AACD,CAFD,CAAA;;AAIA,IAAMI,eAAe,GAAG,SAAlBA,eAAkB,CAAUC,KAAV,EAAiB;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;EACA,OAAOA,KAAK,CAACC,MAAN,CAAaC,UAAb,IAA2B,OAAOF,KAAK,CAACG,YAAb,KAA8B,UAAzD,GACHH,KAAK,CAACG,YAAN,EAAA,CAAqB,CAArB,CADG,GAEHH,KAAK,CAACC,MAFV,CAAA;AAGD,CAXD,CAAA;;AAaMG,IAAAA,eAAe,GAAG,SAAlBA,eAAkB,CAAUC,QAAV,EAAoBC,WAApB,EAAiC;AACvD;AACA;AACA,EAAA,IAAMC,GAAG,GAAG,CAAAD,WAAW,KAAX,IAAA,IAAAA,WAAW,KAAA,KAAA,CAAX,GAAAA,KAAAA,CAAAA,GAAAA,WAAW,CAAEE,QAAb,KAAyBA,QAArC,CAAA;;AAEA,EAAA,IAAMC,MAAM,GAAAC,cAAA,CAAA;AACVC,IAAAA,uBAAuB,EAAE,IADf;AAEVC,IAAAA,iBAAiB,EAAE,IAFT;AAGVC,IAAAA,iBAAiB,EAAE,IAAA;AAHT,GAAA,EAIPP,WAJO,CAAZ,CAAA;;AAOA,EAAA,IAAMQ,KAAK,GAAG;AACZ;AACA;AACAC,IAAAA,UAAU,EAAE,EAHA;AAKZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAC,IAAAA,eAAe,EAAE,EAlBL;AAkBS;AAErB;AACA;AACA;AACA;AACAC,IAAAA,cAAc,EAAE,EAxBJ;AA0BZC,IAAAA,2BAA2B,EAAE,IA1BjB;AA2BZC,IAAAA,uBAAuB,EAAE,IA3Bb;AA4BZC,IAAAA,MAAM,EAAE,KA5BI;AA6BZC,IAAAA,MAAM,EAAE,KA7BI;AA+BZ;AACA;AACAC,IAAAA,sBAAsB,EAAEC,SAAAA;GAjC1B,CAAA;EAoCA,IAAIvD,IAAJ,CAhDuD;;AAkDvD;AACF;AACA;AACA;AACA;AACA;AACA;AACA;;EACE,IAAMwD,SAAS,GAAG,SAAZA,SAAY,CAACC,qBAAD,EAAwBC,UAAxB,EAAoCC,gBAApC,EAAyD;AACzE,IAAA,OAAOF,qBAAqB,IAC1BA,qBAAqB,CAACC,UAAD,CAArB,KAAsCH,SADjC,GAEHE,qBAAqB,CAACC,UAAD,CAFlB,GAGHjB,MAAM,CAACkB,gBAAgB,IAAID,UAArB,CAHV,CAAA;GADF,CAAA;AAOA;AACF;AACA;AACA;AACA;AACA;AACA;;;AACE,EAAA,IAAME,kBAAkB,GAAG,SAArBA,kBAAqB,CAAUC,OAAV,EAAmB;AAC5C;AACA;AACA;AACA,IAAA,OAAOf,KAAK,CAACE,eAAN,CAAsBzB,SAAtB,CACL,UAAA,IAAA,EAAA;MAAA,IAAGuC,SAAH,QAAGA,SAAH;UAAcC,aAAd,QAAcA,aAAd,CAAA;AAAA,MAAA,OACED,SAAS,CAACE,QAAV,CAAmBH,OAAnB,CACA;AACA;AACA;AACA;AACAE,MAAAA,aAAa,CAACE,IAAd,CAAmB,UAACtD,IAAD,EAAA;QAAA,OAAUA,IAAI,KAAKkD,OAAnB,CAAA;AAAA,OAAnB,CANF,CAAA;AAAA,KADK,CAAP,CAAA;GAJF,CAAA;AAeA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACE,EAAA,IAAMK,gBAAgB,GAAG,SAAnBA,gBAAmB,CAAUR,UAAV,EAAiC;AACxD,IAAA,IAAIS,WAAW,GAAG1B,MAAM,CAACiB,UAAD,CAAxB,CAAA;;AAEA,IAAA,IAAI,OAAOS,WAAP,KAAuB,UAA3B,EAAuC;AAAA,MAAA,KAAA,IAAA,KAAA,GAAA,SAAA,CAAA,MAAA,EAHSrC,MAGT,GAAA,IAAA,KAAA,CAAA,KAAA,GAAA,CAAA,GAAA,KAAA,GAAA,CAAA,GAAA,CAAA,CAAA,EAAA,KAAA,GAAA,CAAA,EAAA,KAAA,GAAA,KAAA,EAAA,KAAA,EAAA,EAAA;QAHSA,MAGT,CAAA,KAAA,GAAA,CAAA,CAAA,GAAA,SAAA,CAAA,KAAA,CAAA,CAAA;AAAA,OAAA;;AACrCqC,MAAAA,WAAW,GAAGA,WAAW,CAAX,KAAA,CAAA,KAAA,CAAA,EAAerC,MAAf,CAAd,CAAA;AACD,KAAA;;IAED,IAAIqC,WAAW,KAAK,IAApB,EAA0B;MACxBA,WAAW,GAAGZ,SAAd,CADwB;AAEzB,KAAA;;IAED,IAAI,CAACY,WAAL,EAAkB;AAChB,MAAA,IAAIA,WAAW,KAAKZ,SAAhB,IAA6BY,WAAW,KAAK,KAAjD,EAAwD;AACtD,QAAA,OAAOA,WAAP,CAAA;AACD,OAHe;;;AAMhB,MAAA,MAAM,IAAIC,KAAJ,CACCV,GAAAA,CAAAA,MAAAA,CAAAA,UADD,EAAN,8DAAA,CAAA,CAAA,CAAA;AAGD,KAAA;;AAED,IAAA,IAAI/C,IAAI,GAAGwD,WAAX,CAtBwD;;AAwBxD,IAAA,IAAI,OAAOA,WAAP,KAAuB,QAA3B,EAAqC;MACnCxD,IAAI,GAAG4B,GAAG,CAAC8B,aAAJ,CAAkBF,WAAlB,CAAP,CADmC;;MAEnC,IAAI,CAACxD,IAAL,EAAW;AACT,QAAA,MAAM,IAAIyD,KAAJ,CACCV,GAAAA,CAAAA,MAAAA,CAAAA,UADD,EAAN,uCAAA,CAAA,CAAA,CAAA;AAGD,OAAA;AACF,KAAA;;AAED,IAAA,OAAO/C,IAAP,CAAA;GAjCF,CAAA;;AAoCA,EAAA,IAAM2D,mBAAmB,GAAG,SAAtBA,mBAAsB,GAAY;AACtC,IAAA,IAAI3D,IAAI,GAAGuD,gBAAgB,CAAC,cAAD,CAA3B,CADsC;;IAItC,IAAIvD,IAAI,KAAK,KAAb,EAAoB;AAClB,MAAA,OAAO,KAAP,CAAA;AACD,KAAA;;IAED,IAAIA,IAAI,KAAK4C,SAAb,EAAwB;AACtB;MACA,IAAIK,kBAAkB,CAACrB,GAAG,CAACgC,aAAL,CAAlB,IAAyC,CAA7C,EAAgD;QAC9C5D,IAAI,GAAG4B,GAAG,CAACgC,aAAX,CAAA;AACD,OAFD,MAEO;AACL,QAAA,IAAMC,kBAAkB,GAAG1B,KAAK,CAACG,cAAN,CAAqB,CAArB,CAA3B,CAAA;QACA,IAAMwB,iBAAiB,GACrBD,kBAAkB,IAAIA,kBAAkB,CAACC,iBAD3C,CAFK;;AAML9D,QAAAA,IAAI,GAAG8D,iBAAiB,IAAIP,gBAAgB,CAAC,eAAD,CAA5C,CAAA;AACD,OAAA;AACF,KAAA;;IAED,IAAI,CAACvD,IAAL,EAAW;AACT,MAAA,MAAM,IAAIyD,KAAJ,CACJ,8DADI,CAAN,CAAA;AAGD,KAAA;;AAED,IAAA,OAAOzD,IAAP,CAAA;GA5BF,CAAA;;AA+BA,EAAA,IAAM+D,mBAAmB,GAAG,SAAtBA,mBAAsB,GAAY;IACtC5B,KAAK,CAACE,eAAN,GAAwBF,KAAK,CAACC,UAAN,CAAiB4B,GAAjB,CAAqB,UAACb,SAAD,EAAe;MAC1D,IAAMC,aAAa,GAAGa,iBAAQ,CAACd,SAAD,EAAYrB,MAAM,CAACoC,eAAnB,CAA9B,CAD0D;AAI1D;;MACA,IAAMC,cAAc,GAAGC,kBAAS,CAACjB,SAAD,EAAYrB,MAAM,CAACoC,eAAnB,CAAhC,CAAA;MAEA,OAAO;AACLf,QAAAA,SAAS,EAATA,SADK;AAELC,QAAAA,aAAa,EAAbA,aAFK;AAGLe,QAAAA,cAAc,EAAdA,cAHK;AAILL,QAAAA,iBAAiB,EAAEV,aAAa,CAAC9D,MAAd,GAAuB,CAAvB,GAA2B8D,aAAa,CAAC,CAAD,CAAxC,GAA8C,IAJ5D;AAKLiB,QAAAA,gBAAgB,EACdjB,aAAa,CAAC9D,MAAd,GAAuB,CAAvB,GACI8D,aAAa,CAACA,aAAa,CAAC9D,MAAd,GAAuB,CAAxB,CADjB,GAEI,IARD;;AAUL;AACR;AACA;AACA;AACA;AACA;AACA;AACA;QACQgF,gBAlBK,EAAA,SAAA,gBAAA,CAkBYtE,IAlBZ,EAkBkC;UAAA,IAAhBuE,OAAgB,uEAAN,IAAM,CAAA;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAA,IAAMC,OAAO,GAAGL,cAAc,CAACvD,SAAf,CAAyB,UAAC6D,CAAD,EAAA;YAAA,OAAOA,CAAC,KAAKzE,IAAb,CAAA;AAAA,WAAzB,CAAhB,CAAA;;UACA,IAAIwE,OAAO,GAAG,CAAd,EAAiB;AACf,YAAA,OAAO5B,SAAP,CAAA;AACD,WAAA;;AAED,UAAA,IAAI2B,OAAJ,EAAa;YACX,OAAOJ,cAAc,CAClBO,KADI,CACEF,OAAO,GAAG,CADZ,CAEJlB,CAAAA,IAFI,CAEC,UAACmB,CAAD,EAAA;AAAA,cAAA,OAAOE,mBAAU,CAACF,CAAD,EAAI3C,MAAM,CAACoC,eAAX,CAAjB,CAAA;AAAA,aAFD,CAAP,CAAA;AAGD,WAAA;;AAED,UAAA,OAAOC,cAAc,CAClBO,KADI,CACE,CADF,EACKF,OADL,CAAA,CAEJI,OAFI,EAAA,CAGJtB,IAHI,CAGC,UAACmB,CAAD,EAAA;AAAA,YAAA,OAAOE,mBAAU,CAACF,CAAD,EAAI3C,MAAM,CAACoC,eAAX,CAAjB,CAAA;AAAA,WAHD,CAAP,CAAA;AAID,SAAA;OA5CH,CAAA;AA8CD,KArDuB,CAAxB,CAAA;IAuDA/B,KAAK,CAACG,cAAN,GAAuBH,KAAK,CAACE,eAAN,CAAsBwC,MAAtB,CACrB,UAACC,KAAD,EAAA;AAAA,MAAA,OAAWA,KAAK,CAAC1B,aAAN,CAAoB9D,MAApB,GAA6B,CAAxC,CAAA;KADqB,CAAvB,CAxDsC;;AA6DtC,IAAA,IACE6C,KAAK,CAACG,cAAN,CAAqBhD,MAArB,IAA+B,CAA/B,IACA,CAACiE,gBAAgB,CAAC,eAAD,CAFnB;MAGE;AACA,MAAA,MAAM,IAAIE,KAAJ,CACJ,qGADI,CAAN,CAAA;AAGD,KAAA;GApEH,CAAA;;AAuEA,EAAA,IAAMsB,QAAQ,GAAG,SAAXA,QAAW,CAAU/E,IAAV,EAAgB;IAC/B,IAAIA,IAAI,KAAK,KAAb,EAAoB;AAClB,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,IAAIA,IAAI,KAAK4B,GAAG,CAACgC,aAAjB,EAAgC;AAC9B,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,IAAI,CAAC5D,IAAD,IAAS,CAACA,IAAI,CAACgF,KAAnB,EAA0B;MACxBD,QAAQ,CAACpB,mBAAmB,EAApB,CAAR,CAAA;AACA,MAAA,OAAA;AACD,KAAA;;IAED3D,IAAI,CAACgF,KAAL,CAAW;AAAEC,MAAAA,aAAa,EAAE,CAAC,CAACnD,MAAM,CAACmD,aAAAA;KAArC,CAAA,CAAA;IACA9C,KAAK,CAACK,uBAAN,GAAgCxC,IAAhC,CAAA;;AAEA,IAAA,IAAID,iBAAiB,CAACC,IAAD,CAArB,EAA6B;AAC3BA,MAAAA,IAAI,CAACG,MAAL,EAAA,CAAA;AACD,KAAA;GAnBH,CAAA;;AAsBA,EAAA,IAAM+E,kBAAkB,GAAG,SAArBA,kBAAqB,CAAUC,qBAAV,EAAiC;AAC1D,IAAA,IAAMnF,IAAI,GAAGuD,gBAAgB,CAAC,gBAAD,EAAmB4B,qBAAnB,CAA7B,CAAA;IACA,OAAOnF,IAAI,GAAGA,IAAH,GAAUA,IAAI,KAAK,KAAT,GAAiB,KAAjB,GAAyBmF,qBAA9C,CAAA;AACD,GAHD,CApQuD;AA0QvD;;;AACA,EAAA,IAAMC,gBAAgB,GAAG,SAAnBA,gBAAmB,CAAU/E,CAAV,EAAa;AACpC,IAAA,IAAMiB,MAAM,GAAGF,eAAe,CAACf,CAAD,CAA9B,CAAA;;AAEA,IAAA,IAAI4C,kBAAkB,CAAC3B,MAAD,CAAlB,IAA8B,CAAlC,EAAqC;AACnC;AACA,MAAA,OAAA;AACD,KAAA;;IAED,IAAIJ,cAAc,CAACY,MAAM,CAACuD,uBAAR,EAAiChF,CAAjC,CAAlB,EAAuD;AACrD;MACAhB,IAAI,CAACiG,UAAL,CAAgB;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAC,QAAAA,WAAW,EACTzD,MAAM,CAACE,uBAAP,IACA,CAACwD,oBAAW,CAAClE,MAAD,EAASQ,MAAM,CAACoC,eAAhB,CAAA;OAdhB,CAAA,CAAA;AAgBA,MAAA,OAAA;AACD,KA3BmC;AA8BpC;AACA;;;IACA,IAAIhD,cAAc,CAACY,MAAM,CAAC2D,iBAAR,EAA2BpF,CAA3B,CAAlB,EAAiD;AAC/C;AACA,MAAA,OAAA;AACD,KAnCmC;;;AAsCpCA,IAAAA,CAAC,CAACqF,cAAF,EAAA,CAAA;AACD,GAvCD,CA3QuD;;;AAqTvD,EAAA,IAAMC,YAAY,GAAG,SAAfA,YAAe,CAAUtF,CAAV,EAAa;AAChC,IAAA,IAAMiB,MAAM,GAAGF,eAAe,CAACf,CAAD,CAA9B,CAAA;IACA,IAAMuF,eAAe,GAAG3C,kBAAkB,CAAC3B,MAAD,CAAlB,IAA8B,CAAtD,CAFgC;;AAKhC,IAAA,IAAIsE,eAAe,IAAItE,MAAM,YAAYuE,QAAzC,EAAmD;AACjD,MAAA,IAAID,eAAJ,EAAqB;QACnBzD,KAAK,CAACK,uBAAN,GAAgClB,MAAhC,CAAA;AACD,OAAA;AACF,KAJD,MAIO;AACL;AACAjB,MAAAA,CAAC,CAACyF,wBAAF,EAAA,CAAA;AACAf,MAAAA,QAAQ,CAAC5C,KAAK,CAACK,uBAAN,IAAiCmB,mBAAmB,EAArD,CAAR,CAAA;AACD,KAAA;AACF,GAdD,CArTuD;AAsUvD;AACA;AACA;;;AACA,EAAA,IAAMoC,QAAQ,GAAG,SAAXA,QAAW,CAAU1F,CAAV,EAAa;AAC5B,IAAA,IAAMiB,MAAM,GAAGF,eAAe,CAACf,CAAD,CAA9B,CAAA;IACA0D,mBAAmB,EAAA,CAAA;IAEnB,IAAIiC,eAAe,GAAG,IAAtB,CAAA;;AAEA,IAAA,IAAI7D,KAAK,CAACG,cAAN,CAAqBhD,MAArB,GAA8B,CAAlC,EAAqC;AACnC;AACA;AACA;AACA,MAAA,IAAM2G,cAAc,GAAGhD,kBAAkB,CAAC3B,MAAD,CAAzC,CAAA;AACA,MAAA,IAAM4E,cAAc,GAClBD,cAAc,IAAI,CAAlB,GAAsB9D,KAAK,CAACE,eAAN,CAAsB4D,cAAtB,CAAtB,GAA8DrD,SADhE,CAAA;;MAGA,IAAIqD,cAAc,GAAG,CAArB,EAAwB;AACtB;AACA;QACA,IAAI5F,CAAC,CAAC8F,QAAN,EAAgB;AACd;AACAH,UAAAA,eAAe,GACb7D,KAAK,CAACG,cAAN,CAAqBH,KAAK,CAACG,cAAN,CAAqBhD,MAArB,GAA8B,CAAnD,EACG+E,gBAFL,CAAA;AAGD,SALD,MAKO;AACL;AACA2B,UAAAA,eAAe,GAAG7D,KAAK,CAACG,cAAN,CAAqB,CAArB,EAAwBwB,iBAA1C,CAAA;AACD,SAAA;AACF,OAZD,MAYO,IAAIzD,CAAC,CAAC8F,QAAN,EAAgB;AACrB;AAEA;AACA,QAAA,IAAIC,iBAAiB,GAAGxF,SAAS,CAC/BuB,KAAK,CAACG,cADyB,EAE/B,UAAA,KAAA,EAAA;UAAA,IAAGwB,iBAAH,SAAGA,iBAAH,CAAA;UAAA,OAA2BxC,MAAM,KAAKwC,iBAAtC,CAAA;AAAA,SAF+B,CAAjC,CAAA;;AAKA,QAAA,IACEsC,iBAAiB,GAAG,CAApB,KACCF,cAAc,CAAC/C,SAAf,KAA6B7B,MAA7B,IACEkE,oBAAW,CAAClE,MAAD,EAASQ,MAAM,CAACoC,eAAhB,CAAX,IACC,CAACS,mBAAU,CAACrD,MAAD,EAASQ,MAAM,CAACoC,eAAhB,CADZ,IAEC,CAACgC,cAAc,CAAC5B,gBAAf,CAAgChD,MAAhC,EAAwC,KAAxC,CAJL,CADF,EAME;AACA;AACA;AACA;AACA;AACA;AACA;AACA8E,UAAAA,iBAAiB,GAAGH,cAApB,CAAA;AACD,SAAA;;QAED,IAAIG,iBAAiB,IAAI,CAAzB,EAA4B;AAC1B;AACA;AACA;AACA,UAAA,IAAMC,qBAAqB,GACzBD,iBAAiB,KAAK,CAAtB,GACIjE,KAAK,CAACG,cAAN,CAAqBhD,MAArB,GAA8B,CADlC,GAEI8G,iBAAiB,GAAG,CAH1B,CAAA;AAKA,UAAA,IAAME,gBAAgB,GAAGnE,KAAK,CAACG,cAAN,CAAqB+D,qBAArB,CAAzB,CAAA;UACAL,eAAe,GAAGM,gBAAgB,CAACjC,gBAAnC,CAAA;AACD,SAAA;AACF,OArCM,MAqCA;AACL;AAEA;AACA,QAAA,IAAIkC,gBAAgB,GAAG3F,SAAS,CAC9BuB,KAAK,CAACG,cADwB,EAE9B,UAAA,KAAA,EAAA;UAAA,IAAG+B,gBAAH,SAAGA,gBAAH,CAAA;UAAA,OAA0B/C,MAAM,KAAK+C,gBAArC,CAAA;AAAA,SAF8B,CAAhC,CAAA;;AAKA,QAAA,IACEkC,gBAAgB,GAAG,CAAnB,KACCL,cAAc,CAAC/C,SAAf,KAA6B7B,MAA7B,IACEkE,oBAAW,CAAClE,MAAD,EAASQ,MAAM,CAACoC,eAAhB,CAAX,IACC,CAACS,mBAAU,CAACrD,MAAD,EAASQ,MAAM,CAACoC,eAAhB,CADZ,IAEC,CAACgC,cAAc,CAAC5B,gBAAf,CAAgChD,MAAhC,CAJL,CADF,EAME;AACA;AACA;AACA;AACA;AACA;AACA;AACAiF,UAAAA,gBAAgB,GAAGN,cAAnB,CAAA;AACD,SAAA;;QAED,IAAIM,gBAAgB,IAAI,CAAxB,EAA2B;AACzB;AACA;AACA;AACA,UAAA,IAAMF,sBAAqB,GACzBE,gBAAgB,KAAKpE,KAAK,CAACG,cAAN,CAAqBhD,MAArB,GAA8B,CAAnD,GACI,CADJ,GAEIiH,gBAAgB,GAAG,CAHzB,CAAA;;AAKA,UAAA,IAAMD,iBAAgB,GAAGnE,KAAK,CAACG,cAAN,CAAqB+D,sBAArB,CAAzB,CAAA;UACAL,eAAe,GAAGM,iBAAgB,CAACxC,iBAAnC,CAAA;AACD,SAAA;AACF,OAAA;AACF,KA/FD,MA+FO;AACL;AACAkC,MAAAA,eAAe,GAAGzC,gBAAgB,CAAC,eAAD,CAAlC,CAAA;AACD,KAAA;;AAED,IAAA,IAAIyC,eAAJ,EAAqB;AACnB3F,MAAAA,CAAC,CAACqF,cAAF,EAAA,CAAA;MACAX,QAAQ,CAACiB,eAAD,CAAR,CAAA;AACD,KA7G2B;;GAA9B,CAAA;;AAiHA,EAAA,IAAMQ,QAAQ,GAAG,SAAXA,QAAW,CAAUnG,CAAV,EAAa;AAC5B,IAAA,IACED,aAAa,CAACC,CAAD,CAAb,IACAa,cAAc,CAACY,MAAM,CAACG,iBAAR,EAA2B5B,CAA3B,CAAd,KAAgD,KAFlD,EAGE;AACAA,MAAAA,CAAC,CAACqF,cAAF,EAAA,CAAA;AACArG,MAAAA,IAAI,CAACiG,UAAL,EAAA,CAAA;AACA,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,IAAI9E,UAAU,CAACH,CAAD,CAAd,EAAmB;MACjB0F,QAAQ,CAAC1F,CAAD,CAAR,CAAA;AACA,MAAA,OAAA;AACD,KAAA;GAbH,CAAA;;AAgBA,EAAA,IAAMoG,UAAU,GAAG,SAAbA,UAAa,CAAUpG,CAAV,EAAa;AAC9B,IAAA,IAAMiB,MAAM,GAAGF,eAAe,CAACf,CAAD,CAA9B,CAAA;;AAEA,IAAA,IAAI4C,kBAAkB,CAAC3B,MAAD,CAAlB,IAA8B,CAAlC,EAAqC;AACnC,MAAA,OAAA;AACD,KAAA;;IAED,IAAIJ,cAAc,CAACY,MAAM,CAACuD,uBAAR,EAAiChF,CAAjC,CAAlB,EAAuD;AACrD,MAAA,OAAA;AACD,KAAA;;IAED,IAAIa,cAAc,CAACY,MAAM,CAAC2D,iBAAR,EAA2BpF,CAA3B,CAAlB,EAAiD;AAC/C,MAAA,OAAA;AACD,KAAA;;AAEDA,IAAAA,CAAC,CAACqF,cAAF,EAAA,CAAA;AACArF,IAAAA,CAAC,CAACyF,wBAAF,EAAA,CAAA;AACD,GAjBD,CA1cuD;AA8dvD;AACA;;;AAEA,EAAA,IAAMY,YAAY,GAAG,SAAfA,YAAe,GAAY;AAC/B,IAAA,IAAI,CAACvE,KAAK,CAACM,MAAX,EAAmB;AACjB,MAAA,OAAA;AACD,KAH8B;;;AAM/BvD,IAAAA,gBAAgB,CAACE,YAAjB,CAA8BC,IAA9B,EAN+B;AAS/B;;IACA8C,KAAK,CAACQ,sBAAN,GAA+Bb,MAAM,CAACI,iBAAP,GAC3BzB,KAAK,CAAC,YAAY;MAChBsE,QAAQ,CAACpB,mBAAmB,EAApB,CAAR,CAAA;AACD,KAFI,CADsB,GAI3BoB,QAAQ,CAACpB,mBAAmB,EAApB,CAJZ,CAAA;AAMA/B,IAAAA,GAAG,CAAC+E,gBAAJ,CAAqB,SAArB,EAAgChB,YAAhC,EAA8C,IAA9C,CAAA,CAAA;AACA/D,IAAAA,GAAG,CAAC+E,gBAAJ,CAAqB,WAArB,EAAkCvB,gBAAlC,EAAoD;AAClDwB,MAAAA,OAAO,EAAE,IADyC;AAElDC,MAAAA,OAAO,EAAE,KAAA;KAFX,CAAA,CAAA;AAIAjF,IAAAA,GAAG,CAAC+E,gBAAJ,CAAqB,YAArB,EAAmCvB,gBAAnC,EAAqD;AACnDwB,MAAAA,OAAO,EAAE,IAD0C;AAEnDC,MAAAA,OAAO,EAAE,KAAA;KAFX,CAAA,CAAA;AAIAjF,IAAAA,GAAG,CAAC+E,gBAAJ,CAAqB,OAArB,EAA8BF,UAA9B,EAA0C;AACxCG,MAAAA,OAAO,EAAE,IAD+B;AAExCC,MAAAA,OAAO,EAAE,KAAA;KAFX,CAAA,CAAA;AAIAjF,IAAAA,GAAG,CAAC+E,gBAAJ,CAAqB,SAArB,EAAgCH,QAAhC,EAA0C;AACxCI,MAAAA,OAAO,EAAE,IAD+B;AAExCC,MAAAA,OAAO,EAAE,KAAA;KAFX,CAAA,CAAA;AAKA,IAAA,OAAOxH,IAAP,CAAA;GAlCF,CAAA;;AAqCA,EAAA,IAAMyH,eAAe,GAAG,SAAlBA,eAAkB,GAAY;AAClC,IAAA,IAAI,CAAC3E,KAAK,CAACM,MAAX,EAAmB;AACjB,MAAA,OAAA;AACD,KAAA;;AAEDb,IAAAA,GAAG,CAACmF,mBAAJ,CAAwB,SAAxB,EAAmCpB,YAAnC,EAAiD,IAAjD,CAAA,CAAA;AACA/D,IAAAA,GAAG,CAACmF,mBAAJ,CAAwB,WAAxB,EAAqC3B,gBAArC,EAAuD,IAAvD,CAAA,CAAA;AACAxD,IAAAA,GAAG,CAACmF,mBAAJ,CAAwB,YAAxB,EAAsC3B,gBAAtC,EAAwD,IAAxD,CAAA,CAAA;AACAxD,IAAAA,GAAG,CAACmF,mBAAJ,CAAwB,OAAxB,EAAiCN,UAAjC,EAA6C,IAA7C,CAAA,CAAA;AACA7E,IAAAA,GAAG,CAACmF,mBAAJ,CAAwB,SAAxB,EAAmCP,QAAnC,EAA6C,IAA7C,CAAA,CAAA;AAEA,IAAA,OAAOnH,IAAP,CAAA;AACD,GAZD,CAtgBuD;AAqhBvD;AACA;;;AAEAA,EAAAA,IAAI,GAAG;AACL,IAAA,IAAIoD,MAAJ,GAAa;MACX,OAAON,KAAK,CAACM,MAAb,CAAA;KAFG;;AAKL,IAAA,IAAIC,MAAJ,GAAa;MACX,OAAOP,KAAK,CAACO,MAAb,CAAA;KANG;;IASLsE,QATK,EAAA,SAAA,QAAA,CASIC,eATJ,EASqB;MACxB,IAAI9E,KAAK,CAACM,MAAV,EAAkB;AAChB,QAAA,OAAO,IAAP,CAAA;AACD,OAAA;;AAED,MAAA,IAAMyE,UAAU,GAAGrE,SAAS,CAACoE,eAAD,EAAkB,YAAlB,CAA5B,CAAA;AACA,MAAA,IAAME,cAAc,GAAGtE,SAAS,CAACoE,eAAD,EAAkB,gBAAlB,CAAhC,CAAA;AACA,MAAA,IAAMG,iBAAiB,GAAGvE,SAAS,CAACoE,eAAD,EAAkB,mBAAlB,CAAnC,CAAA;;MAEA,IAAI,CAACG,iBAAL,EAAwB;QACtBrD,mBAAmB,EAAA,CAAA;AACpB,OAAA;;MAED5B,KAAK,CAACM,MAAN,GAAe,IAAf,CAAA;MACAN,KAAK,CAACO,MAAN,GAAe,KAAf,CAAA;AACAP,MAAAA,KAAK,CAACI,2BAAN,GAAoCX,GAAG,CAACgC,aAAxC,CAAA;;AAEA,MAAA,IAAIsD,UAAJ,EAAgB;QACdA,UAAU,EAAA,CAAA;AACX,OAAA;;AAED,MAAA,IAAMG,gBAAgB,GAAG,SAAnBA,gBAAmB,GAAM;AAC7B,QAAA,IAAID,iBAAJ,EAAuB;UACrBrD,mBAAmB,EAAA,CAAA;AACpB,SAAA;;QACD2C,YAAY,EAAA,CAAA;;AACZ,QAAA,IAAIS,cAAJ,EAAoB;UAClBA,cAAc,EAAA,CAAA;AACf,SAAA;OAPH,CAAA;;AAUA,MAAA,IAAIC,iBAAJ,EAAuB;AACrBA,QAAAA,iBAAiB,CAACjF,KAAK,CAACC,UAAN,CAAiBkF,MAAjB,EAAD,CAAjB,CAA6CC,IAA7C,CACEF,gBADF,EAEEA,gBAFF,CAAA,CAAA;AAIA,QAAA,OAAO,IAAP,CAAA;AACD,OAAA;;MAEDA,gBAAgB,EAAA,CAAA;AAChB,MAAA,OAAO,IAAP,CAAA;KAjDG;IAoDL/B,UApDK,EAAA,SAAA,UAAA,CAoDMkC,iBApDN,EAoDyB;AAC5B,MAAA,IAAI,CAACrF,KAAK,CAACM,MAAX,EAAmB;AACjB,QAAA,OAAO,IAAP,CAAA;AACD,OAAA;;AAED,MAAA,IAAMgF,OAAO,GAAA1F,cAAA,CAAA;QACX2F,YAAY,EAAE5F,MAAM,CAAC4F,YADV;QAEXC,gBAAgB,EAAE7F,MAAM,CAAC6F,gBAFd;QAGXC,mBAAmB,EAAE9F,MAAM,CAAC8F,mBAAAA;AAHjB,OAAA,EAIRJ,iBAJQ,CAAb,CAAA;;AAOAK,MAAAA,YAAY,CAAC1F,KAAK,CAACQ,sBAAP,CAAZ,CAZ4B;;MAa5BR,KAAK,CAACQ,sBAAN,GAA+BC,SAA/B,CAAA;MAEAkE,eAAe,EAAA,CAAA;MACf3E,KAAK,CAACM,MAAN,GAAe,KAAf,CAAA;MACAN,KAAK,CAACO,MAAN,GAAe,KAAf,CAAA;MAEAxD,gBAAgB,CAACW,cAAjB,CAAgCR,IAAhC,CAAA,CAAA;AAEA,MAAA,IAAMqI,YAAY,GAAG7E,SAAS,CAAC4E,OAAD,EAAU,cAAV,CAA9B,CAAA;AACA,MAAA,IAAME,gBAAgB,GAAG9E,SAAS,CAAC4E,OAAD,EAAU,kBAAV,CAAlC,CAAA;AACA,MAAA,IAAMG,mBAAmB,GAAG/E,SAAS,CAAC4E,OAAD,EAAU,qBAAV,CAArC,CAAA;MACA,IAAMlC,WAAW,GAAG1C,SAAS,CAC3B4E,OAD2B,EAE3B,aAF2B,EAG3B,yBAH2B,CAA7B,CAAA;;AAMA,MAAA,IAAIC,YAAJ,EAAkB;QAChBA,YAAY,EAAA,CAAA;AACb,OAAA;;AAED,MAAA,IAAMI,kBAAkB,GAAG,SAArBA,kBAAqB,GAAM;AAC/BrH,QAAAA,KAAK,CAAC,YAAM;AACV,UAAA,IAAI8E,WAAJ,EAAiB;AACfR,YAAAA,QAAQ,CAACG,kBAAkB,CAAC/C,KAAK,CAACI,2BAAP,CAAnB,CAAR,CAAA;AACD,WAAA;;AACD,UAAA,IAAIoF,gBAAJ,EAAsB;YACpBA,gBAAgB,EAAA,CAAA;AACjB,WAAA;AACF,SAPI,CAAL,CAAA;OADF,CAAA;;MAWA,IAAIpC,WAAW,IAAIqC,mBAAnB,EAAwC;AACtCA,QAAAA,mBAAmB,CACjB1C,kBAAkB,CAAC/C,KAAK,CAACI,2BAAP,CADD,CAAnB,CAEEgF,IAFF,CAEOO,kBAFP,EAE2BA,kBAF3B,CAAA,CAAA;AAGA,QAAA,OAAO,IAAP,CAAA;AACD,OAAA;;MAEDA,kBAAkB,EAAA,CAAA;AAClB,MAAA,OAAO,IAAP,CAAA;KAzGG;AA4GLtI,IAAAA,KA5GK,EA4GG,SAAA,KAAA,GAAA;MACN,IAAI2C,KAAK,CAACO,MAAN,IAAgB,CAACP,KAAK,CAACM,MAA3B,EAAmC;AACjC,QAAA,OAAO,IAAP,CAAA;AACD,OAAA;;MAEDN,KAAK,CAACO,MAAN,GAAe,IAAf,CAAA;MACAoE,eAAe,EAAA,CAAA;AAEf,MAAA,OAAO,IAAP,CAAA;KApHG;AAuHLhH,IAAAA,OAvHK,EAuHK,SAAA,OAAA,GAAA;MACR,IAAI,CAACqC,KAAK,CAACO,MAAP,IAAiB,CAACP,KAAK,CAACM,MAA5B,EAAoC;AAClC,QAAA,OAAO,IAAP,CAAA;AACD,OAAA;;MAEDN,KAAK,CAACO,MAAN,GAAe,KAAf,CAAA;MACAqB,mBAAmB,EAAA,CAAA;MACnB2C,YAAY,EAAA,CAAA;AAEZ,MAAA,OAAO,IAAP,CAAA;KAhIG;IAmILqB,uBAnIK,EAAA,SAAA,uBAAA,CAmImBC,iBAnInB,EAmIsC;MACzC,IAAMC,eAAe,GAAG,EAAA,CAAGX,MAAH,CAAUU,iBAAV,CAA6BnD,CAAAA,MAA7B,CAAoCqD,OAApC,CAAxB,CAAA;MAEA/F,KAAK,CAACC,UAAN,GAAmB6F,eAAe,CAACjE,GAAhB,CAAoB,UAACd,OAAD,EAAA;AAAA,QAAA,OACrC,OAAOA,OAAP,KAAmB,QAAnB,GAA8BtB,GAAG,CAAC8B,aAAJ,CAAkBR,OAAlB,CAA9B,GAA2DA,OADtB,CAAA;AAAA,OAApB,CAAnB,CAAA;;MAIA,IAAIf,KAAK,CAACM,MAAV,EAAkB;QAChBsB,mBAAmB,EAAA,CAAA;AACpB,OAAA;;AAED,MAAA,OAAO,IAAP,CAAA;AACD,KAAA;AA/II,GAAP,CAxhBuD;;EA2qBvD1E,IAAI,CAAC0I,uBAAL,CAA6BrG,QAA7B,CAAA,CAAA;AAEA,EAAA,OAAOrC,IAAP,CAAA;AACD;;;;"}