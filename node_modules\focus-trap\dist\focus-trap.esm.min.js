/*!
* focus-trap 6.9.4
* @license MIT, https://github.com/focus-trap/focus-trap/blob/master/LICENSE
*/
import{tabbable as e,focusable as t,isTabbable as n,isFocusable as a}from"tabbable";function r(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function o(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?r(Object(n),!0).forEach((function(t){i(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):r(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function i(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var c,u=(c=[],{activateTrap:function(e){if(c.length>0){var t=c[c.length-1];t!==e&&t.pause()}var n=c.indexOf(e);-1===n||c.splice(n,1),c.push(e)},deactivateTrap:function(e){var t=c.indexOf(e);-1!==t&&c.splice(t,1),c.length>0&&c[c.length-1].unpause()}}),s=function(e){return setTimeout(e,0)},l=function(e,t){var n=-1;return e.every((function(e,a){return!t(e)||(n=a,!1)})),n},b=function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),a=1;a<t;a++)n[a-1]=arguments[a];return"function"==typeof e?e.apply(void 0,n):e},f=function(e){return e.target.shadowRoot&&"function"==typeof e.composedPath?e.composedPath()[0]:e.target},v=function(r,i){var c,v=(null==i?void 0:i.document)||document,d=o({returnFocusOnDeactivate:!0,escapeDeactivates:!0,delayInitialFocus:!0},i),p={containers:[],containerGroups:[],tabbableGroups:[],nodeFocusedBeforeActivation:null,mostRecentlyFocusedNode:null,active:!1,paused:!1,delayInitialFocusTimer:void 0},h=function(e,t,n){return e&&void 0!==e[t]?e[t]:d[n||t]},m=function(e){return p.containerGroups.findIndex((function(t){var n=t.container,a=t.tabbableNodes;return n.contains(e)||a.find((function(t){return t===e}))}))},y=function(e){var t=d[e];if("function"==typeof t){for(var n=arguments.length,a=new Array(n>1?n-1:0),r=1;r<n;r++)a[r-1]=arguments[r];t=t.apply(void 0,a)}if(!0===t&&(t=void 0),!t){if(void 0===t||!1===t)return t;throw new Error("`".concat(e,"` was specified but was not a node, or did not return a node"))}var o=t;if("string"==typeof t&&!(o=v.querySelector(t)))throw new Error("`".concat(e,"` as selector refers to no known node"));return o},O=function(){var e=y("initialFocus");if(!1===e)return!1;if(void 0===e)if(m(v.activeElement)>=0)e=v.activeElement;else{var t=p.tabbableGroups[0];e=t&&t.firstTabbableNode||y("fallbackFocus")}if(!e)throw new Error("Your focus-trap needs to have at least one focusable element");return e},g=function(){if(p.containerGroups=p.containers.map((function(a){var r=e(a,d.tabbableOptions),o=t(a,d.tabbableOptions);return{container:a,tabbableNodes:r,focusableNodes:o,firstTabbableNode:r.length>0?r[0]:null,lastTabbableNode:r.length>0?r[r.length-1]:null,nextTabbableNode:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],a=o.findIndex((function(t){return t===e}));if(!(a<0))return t?o.slice(a+1).find((function(e){return n(e,d.tabbableOptions)})):o.slice(0,a).reverse().find((function(e){return n(e,d.tabbableOptions)}))}}})),p.tabbableGroups=p.containerGroups.filter((function(e){return e.tabbableNodes.length>0})),p.tabbableGroups.length<=0&&!y("fallbackFocus"))throw new Error("Your focus-trap must have at least one container with at least one tabbable node in it at all times")},w=function e(t){!1!==t&&t!==v.activeElement&&(t&&t.focus?(t.focus({preventScroll:!!d.preventScroll}),p.mostRecentlyFocusedNode=t,function(e){return e.tagName&&"input"===e.tagName.toLowerCase()&&"function"==typeof e.select}(t)&&t.select()):e(O()))},F=function(e){var t=y("setReturnFocus",e);return t||!1!==t&&e},E=function(e){var t=f(e);m(t)>=0||(b(d.clickOutsideDeactivates,e)?c.deactivate({returnFocus:d.returnFocusOnDeactivate&&!a(t,d.tabbableOptions)}):b(d.allowOutsideClick,e)||e.preventDefault())},T=function(e){var t=f(e),n=m(t)>=0;n||t instanceof Document?n&&(p.mostRecentlyFocusedNode=t):(e.stopImmediatePropagation(),w(p.mostRecentlyFocusedNode||O()))},k=function(e){if(function(e){return"Escape"===e.key||"Esc"===e.key||27===e.keyCode}(e)&&!1!==b(d.escapeDeactivates,e))return e.preventDefault(),void c.deactivate();(function(e){return"Tab"===e.key||9===e.keyCode})(e)&&function(e){var t=f(e);g();var r=null;if(p.tabbableGroups.length>0){var o=m(t),i=o>=0?p.containerGroups[o]:void 0;if(o<0)r=e.shiftKey?p.tabbableGroups[p.tabbableGroups.length-1].lastTabbableNode:p.tabbableGroups[0].firstTabbableNode;else if(e.shiftKey){var c=l(p.tabbableGroups,(function(e){var n=e.firstTabbableNode;return t===n}));if(c<0&&(i.container===t||a(t,d.tabbableOptions)&&!n(t,d.tabbableOptions)&&!i.nextTabbableNode(t,!1))&&(c=o),c>=0){var u=0===c?p.tabbableGroups.length-1:c-1;r=p.tabbableGroups[u].lastTabbableNode}}else{var s=l(p.tabbableGroups,(function(e){var n=e.lastTabbableNode;return t===n}));if(s<0&&(i.container===t||a(t,d.tabbableOptions)&&!n(t,d.tabbableOptions)&&!i.nextTabbableNode(t))&&(s=o),s>=0){var b=s===p.tabbableGroups.length-1?0:s+1;r=p.tabbableGroups[b].firstTabbableNode}}}else r=y("fallbackFocus");r&&(e.preventDefault(),w(r))}(e)},D=function(e){var t=f(e);m(t)>=0||b(d.clickOutsideDeactivates,e)||b(d.allowOutsideClick,e)||(e.preventDefault(),e.stopImmediatePropagation())},N=function(){if(p.active)return u.activateTrap(c),p.delayInitialFocusTimer=d.delayInitialFocus?s((function(){w(O())})):w(O()),v.addEventListener("focusin",T,!0),v.addEventListener("mousedown",E,{capture:!0,passive:!1}),v.addEventListener("touchstart",E,{capture:!0,passive:!1}),v.addEventListener("click",D,{capture:!0,passive:!1}),v.addEventListener("keydown",k,{capture:!0,passive:!1}),c},G=function(){if(p.active)return v.removeEventListener("focusin",T,!0),v.removeEventListener("mousedown",E,!0),v.removeEventListener("touchstart",E,!0),v.removeEventListener("click",D,!0),v.removeEventListener("keydown",k,!0),c};return(c={get active(){return p.active},get paused(){return p.paused},activate:function(e){if(p.active)return this;var t=h(e,"onActivate"),n=h(e,"onPostActivate"),a=h(e,"checkCanFocusTrap");a||g(),p.active=!0,p.paused=!1,p.nodeFocusedBeforeActivation=v.activeElement,t&&t();var r=function(){a&&g(),N(),n&&n()};return a?(a(p.containers.concat()).then(r,r),this):(r(),this)},deactivate:function(e){if(!p.active)return this;var t=o({onDeactivate:d.onDeactivate,onPostDeactivate:d.onPostDeactivate,checkCanReturnFocus:d.checkCanReturnFocus},e);clearTimeout(p.delayInitialFocusTimer),p.delayInitialFocusTimer=void 0,G(),p.active=!1,p.paused=!1,u.deactivateTrap(c);var n=h(t,"onDeactivate"),a=h(t,"onPostDeactivate"),r=h(t,"checkCanReturnFocus"),i=h(t,"returnFocus","returnFocusOnDeactivate");n&&n();var l=function(){s((function(){i&&w(F(p.nodeFocusedBeforeActivation)),a&&a()}))};return i&&r?(r(F(p.nodeFocusedBeforeActivation)).then(l,l),this):(l(),this)},pause:function(){return p.paused||!p.active||(p.paused=!0,G()),this},unpause:function(){return p.paused&&p.active?(p.paused=!1,g(),N(),this):this},updateContainerElements:function(e){var t=[].concat(e).filter(Boolean);return p.containers=t.map((function(e){return"string"==typeof e?v.querySelector(e):e})),p.active&&g(),this}}).updateContainerElements(r),c};export{v as createFocusTrap};
//# sourceMappingURL=focus-trap.esm.min.js.map
