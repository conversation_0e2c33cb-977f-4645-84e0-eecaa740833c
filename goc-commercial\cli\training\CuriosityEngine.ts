import { Logger } from '../utils/Logger';
import { KnowledgeRepository, KnowledgeDomain } from './KnowledgeRepository';
import { MemoryManager, Experience } from '../agent/MemoryManager';
import { AIProviderManager } from '../ai/AIProviderManager';

export interface CuriosityTopic {
  id: string;
  topic: string;
  domain: string;
  priority: number;
  reasoning: string;
  explorationDepth: 'surface' | 'moderate' | 'deep';
  estimatedValue: number;
  lastExplored?: Date;
}

export interface ExplorationResult {
  topicId: string;
  findings: string[];
  newKnowledge: string[];
  connections: string[];
  followUpTopics: string[];
  value: number;
}

export interface CuriosityMetrics {
  totalTopicsGenerated: number;
  topicsExplored: number;
  knowledgeGained: number;
  averageTopicValue: number;
  explorationEfficiency: number;
}

export class CuriosityEngine {
  private logger: Logger;
  private knowledgeRepository: KnowledgeRepository;
  private memoryManager: MemoryManager;
  private aiManager: AIProviderManager;

  private curiosityTopics: CuriosityTopic[] = [];
  private explorationHistory: ExplorationResult[] = [];
  private metrics: CuriosityMetrics;

  constructor(
    logger: Logger,
    knowledgeRepository: KnowledgeRepository,
    memoryManager: MemoryManager,
    aiManager: AIProviderManager
  ) {
    this.logger = logger;
    this.knowledgeRepository = knowledgeRepository;
    this.memoryManager = memoryManager;
    this.aiManager = aiManager;

    this.metrics = {
      totalTopicsGenerated: 0,
      topicsExplored: 0,
      knowledgeGained: 0,
      averageTopicValue: 0,
      explorationEfficiency: 0
    };
  }

  public async generateResearchTopics(): Promise<string[]> {
    this.logger.info('🔍 Generating curiosity-driven research topics...');

    try {
      // Analyze current knowledge gaps
      const knowledgeGaps = await this.identifyKnowledgeGaps();
      
      // Generate topics based on recent experiences
      const experienceTopics = await this.generateTopicsFromExperiences();
      
      // Generate topics from domain expertise analysis
      const domainTopics = await this.generateTopicsFromDomains();
      
      // Generate trending technology topics
      const trendingTopics = await this.generateTrendingTopics();

      // Combine and prioritize topics
      const allTopics = [
        ...knowledgeGaps,
        ...experienceTopics,
        ...domainTopics,
        ...trendingTopics
      ];

      const prioritizedTopics = await this.prioritizeTopics(allTopics);
      
      // Store topics for tracking
      this.curiosityTopics.push(...prioritizedTopics);
      this.metrics.totalTopicsGenerated += prioritizedTopics.length;

      const topicStrings = prioritizedTopics
        .sort((a, b) => b.priority - a.priority)
        .slice(0, 5)
        .map(t => t.topic);

      this.logger.success(`✅ Generated ${topicStrings.length} research topics`);
      return topicStrings;

    } catch (error) {
      this.logger.error(`❌ Failed to generate research topics: ${error}`);
      return [];
    }
  }

  public async exploreTopicAutonomously(topic: string): Promise<ExplorationResult> {
    this.logger.info(`🔬 Autonomously exploring topic: ${topic}`);

    const topicObj = this.curiosityTopics.find(t => t.topic === topic);
    if (!topicObj) {
      throw new Error(`Topic not found: ${topic}`);
    }

    try {
      // Generate exploration questions
      const questions = await this.generateExplorationQuestions(topic);
      
      // Conduct research for each question
      const findings: string[] = [];
      const newKnowledge: string[] = [];
      const connections: string[] = [];

      for (const question of questions.slice(0, 3)) {
        const research = await this.conductResearch(question);
        findings.push(...research.findings);
        newKnowledge.push(...research.knowledge);
        connections.push(...research.connections);
      }

      // Generate follow-up topics
      const followUpTopics = await this.generateFollowUpTopics(topic, findings);

      // Calculate exploration value
      const value = this.calculateExplorationValue(findings, newKnowledge, connections);

      const result: ExplorationResult = {
        topicId: topicObj.id,
        findings,
        newKnowledge,
        connections,
        followUpTopics,
        value
      };

      // Store exploration result
      this.explorationHistory.push(result);
      topicObj.lastExplored = new Date();
      
      // Update metrics
      this.updateExplorationMetrics(result);

      this.logger.success(`✅ Exploration completed with value: ${value.toFixed(2)}`);
      return result;

    } catch (error) {
      this.logger.error(`❌ Failed to explore topic: ${error}`);
      throw error;
    }
  }

  private async identifyKnowledgeGaps(): Promise<CuriosityTopic[]> {
    const gaps: CuriosityTopic[] = [];
    const domainExpertise = this.knowledgeRepository.getDomainExpertise();

    // Find domains with low expertise
    for (const [domain, expertise] of domainExpertise) {
      if (expertise < 0.6) {
        gaps.push({
          id: this.generateId(),
          topic: `Advanced ${domain} techniques and best practices`,
          domain,
          priority: 0.8,
          reasoning: `Low expertise in ${domain} domain (${(expertise * 100).toFixed(1)}%)`,
          explorationDepth: 'moderate',
          estimatedValue: 0.8
        });
      }
    }

    // Identify missing fundamental domains
    const fundamentalDomains = [
      'software-architecture', 'design-patterns', 'performance-optimization',
      'security-practices', 'testing-strategies', 'deployment-automation'
    ];

    for (const domain of fundamentalDomains) {
      if (!domainExpertise.has(domain)) {
        gaps.push({
          id: this.generateId(),
          topic: `Fundamentals of ${domain.replace('-', ' ')}`,
          domain,
          priority: 0.9,
          reasoning: `Missing fundamental knowledge in ${domain}`,
          explorationDepth: 'deep',
          estimatedValue: 0.9
        });
      }
    }

    return gaps;
  }

  private async generateTopicsFromExperiences(): Promise<CuriosityTopic[]> {
    const topics: CuriosityTopic[] = [];
    const recentExperiences = this.memoryManager.getRecentExperiences(20);

    // Analyze failure patterns for learning opportunities
    const failures = recentExperiences.filter(exp => exp.outcome === 'failure');
    
    for (const failure of failures.slice(0, 3)) {
      if (failure.error) {
        topics.push({
          id: this.generateId(),
          topic: `How to avoid and fix: ${failure.error}`,
          domain: 'problem-solving',
          priority: 0.7,
          reasoning: `Learn from recent failure: ${failure.task}`,
          explorationDepth: 'moderate',
          estimatedValue: 0.7
        });
      }
    }

    // Analyze successful patterns for optimization
    const successes = recentExperiences.filter(exp => exp.outcome === 'success');
    const successPatterns = this.extractPatternsFromExperiences(successes);

    for (const pattern of successPatterns.slice(0, 2)) {
      topics.push({
        id: this.generateId(),
        topic: `Advanced techniques for ${pattern}`,
        domain: 'optimization',
        priority: 0.6,
        reasoning: `Optimize successful pattern: ${pattern}`,
        explorationDepth: 'surface',
        estimatedValue: 0.6
      });
    }

    return topics;
  }

  private async generateTopicsFromDomains(): Promise<CuriosityTopic[]> {
    const topics: CuriosityTopic[] = [];
    const domainExpertise = this.knowledgeRepository.getDomainExpertise();

    // Generate advanced topics for domains with high expertise
    for (const [domain, expertise] of domainExpertise) {
      if (expertise > 0.8) {
        topics.push({
          id: this.generateId(),
          topic: `Cutting-edge developments in ${domain}`,
          domain,
          priority: 0.5,
          reasoning: `High expertise in ${domain}, explore advanced topics`,
          explorationDepth: 'deep',
          estimatedValue: 0.5
        });
      }
    }

    return topics;
  }

  private async generateTrendingTopics(): Promise<CuriosityTopic[]> {
    const trendingTopics = [
      'AI-powered development tools',
      'WebAssembly applications',
      'Edge computing architectures',
      'Quantum computing programming',
      'Sustainable software development',
      'Zero-trust security models'
    ];

    return trendingTopics.map(topic => ({
      id: this.generateId(),
      topic,
      domain: 'emerging-tech',
      priority: 0.4,
      reasoning: 'Stay current with technology trends',
      explorationDepth: 'surface',
      estimatedValue: 0.4
    }));
  }

  private async prioritizeTopics(topics: CuriosityTopic[]): Promise<CuriosityTopic[]> {
    // Sort by priority and estimated value
    return topics.sort((a, b) => {
      const scoreA = a.priority * 0.7 + a.estimatedValue * 0.3;
      const scoreB = b.priority * 0.7 + b.estimatedValue * 0.3;
      return scoreB - scoreA;
    });
  }

  private async generateExplorationQuestions(topic: string): Promise<string[]> {
    try {
      const messages = [
        {
          role: 'system' as const,
          content: 'Generate 5 specific, actionable research questions about the given topic. Focus on practical knowledge that would improve coding abilities.'
        },
        {
          role: 'user' as const,
          content: `Topic: ${topic}\n\nGenerate research questions:`
        }
      ];

      const response = await this.aiManager.chat(messages);
      const questions = response.content
        .split('\n')
        .filter(line => line.trim().length > 0)
        .slice(0, 5);

      return questions;
    } catch (error) {
      this.logger.debug(`Failed to generate exploration questions: ${error}`);
      return [`What are the key concepts in ${topic}?`];
    }
  }

  private async conductResearch(question: string): Promise<{
    findings: string[];
    knowledge: string[];
    connections: string[];
  }> {
    // This would integrate with WebLearningEngine for actual research
    // For now, return structured placeholder data
    return {
      findings: [`Research finding for: ${question}`],
      knowledge: [`New knowledge from: ${question}`],
      connections: [`Connection discovered: ${question}`]
    };
  }

  private async generateFollowUpTopics(topic: string, findings: string[]): Promise<string[]> {
    if (findings.length === 0) return [];

    try {
      const messages = [
        {
          role: 'system' as const,
          content: 'Based on research findings, suggest 3 follow-up topics for deeper exploration.'
        },
        {
          role: 'user' as const,
          content: `Original topic: ${topic}\nFindings: ${findings.join(', ')}\n\nSuggest follow-up topics:`
        }
      ];

      const response = await this.aiManager.chat(messages);
      return response.content
        .split('\n')
        .filter(line => line.trim().length > 0)
        .slice(0, 3);
    } catch (error) {
      this.logger.debug(`Failed to generate follow-up topics: ${error}`);
      return [];
    }
  }

  private calculateExplorationValue(findings: string[], knowledge: string[], connections: string[]): number {
    const findingsValue = findings.length * 0.3;
    const knowledgeValue = knowledge.length * 0.5;
    const connectionsValue = connections.length * 0.2;
    
    return Math.min(findingsValue + knowledgeValue + connectionsValue, 1.0);
  }

  private extractPatternsFromExperiences(experiences: Experience[]): string[] {
    const patterns = new Set<string>();
    
    for (const exp of experiences) {
      if (exp.action) {
        patterns.add(exp.action.type);
      }
    }
    
    return Array.from(patterns);
  }

  private updateExplorationMetrics(result: ExplorationResult): void {
    this.metrics.topicsExplored++;
    this.metrics.knowledgeGained += result.newKnowledge.length;
    
    const totalValue = this.explorationHistory.reduce((sum, r) => sum + r.value, 0);
    this.metrics.averageTopicValue = totalValue / this.explorationHistory.length;
    
    this.metrics.explorationEfficiency = this.metrics.knowledgeGained / this.metrics.topicsExplored;
  }

  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  public getCuriosityMetrics(): CuriosityMetrics {
    return { ...this.metrics };
  }

  public getActiveTopics(): CuriosityTopic[] {
    return this.curiosityTopics.filter(t => !t.lastExplored);
  }

  public getExplorationHistory(): ExplorationResult[] {
    return [...this.explorationHistory];
  }
}
