/*!
* focus-trap 6.9.4
* @license MIT, https://github.com/focus-trap/focus-trap/blob/master/LICENSE
*/
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("tabbable")):"function"==typeof define&&define.amd?define(["exports","tabbable"],t):(e="undefined"!=typeof globalThis?globalThis:e||self,function(){var n=e.focusTrap,a=e.focusTrap={};t(a,e.tabbable),a.noConflict=function(){return e.focusTrap=n,a}}())}(this,(function(e,t){"use strict";function n(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function a(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?n(Object(a),!0).forEach((function(t){o(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):n(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}function o(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var r,i=(r=[],{activateTrap:function(e){if(r.length>0){var t=r[r.length-1];t!==e&&t.pause()}var n=r.indexOf(e);-1===n||r.splice(n,1),r.push(e)},deactivateTrap:function(e){var t=r.indexOf(e);-1!==t&&r.splice(t,1),r.length>0&&r[r.length-1].unpause()}}),c=function(e){return setTimeout(e,0)},u=function(e,t){var n=-1;return e.every((function(e,a){return!t(e)||(n=a,!1)})),n},s=function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),a=1;a<t;a++)n[a-1]=arguments[a];return"function"==typeof e?e.apply(void 0,n):e},l=function(e){return e.target.shadowRoot&&"function"==typeof e.composedPath?e.composedPath()[0]:e.target};e.createFocusTrap=function(e,n){var o,r=(null==n?void 0:n.document)||document,b=a({returnFocusOnDeactivate:!0,escapeDeactivates:!0,delayInitialFocus:!0},n),f={containers:[],containerGroups:[],tabbableGroups:[],nodeFocusedBeforeActivation:null,mostRecentlyFocusedNode:null,active:!1,paused:!1,delayInitialFocusTimer:void 0},v=function(e,t,n){return e&&void 0!==e[t]?e[t]:b[n||t]},d=function(e){return f.containerGroups.findIndex((function(t){var n=t.container,a=t.tabbableNodes;return n.contains(e)||a.find((function(t){return t===e}))}))},p=function(e){var t=b[e];if("function"==typeof t){for(var n=arguments.length,a=new Array(n>1?n-1:0),o=1;o<n;o++)a[o-1]=arguments[o];t=t.apply(void 0,a)}if(!0===t&&(t=void 0),!t){if(void 0===t||!1===t)return t;throw new Error("`".concat(e,"` was specified but was not a node, or did not return a node"))}var i=t;if("string"==typeof t&&!(i=r.querySelector(t)))throw new Error("`".concat(e,"` as selector refers to no known node"));return i},h=function(){var e=p("initialFocus");if(!1===e)return!1;if(void 0===e)if(d(r.activeElement)>=0)e=r.activeElement;else{var t=f.tabbableGroups[0];e=t&&t.firstTabbableNode||p("fallbackFocus")}if(!e)throw new Error("Your focus-trap needs to have at least one focusable element");return e},y=function(){if(f.containerGroups=f.containers.map((function(e){var n=t.tabbable(e,b.tabbableOptions),a=t.focusable(e,b.tabbableOptions);return{container:e,tabbableNodes:n,focusableNodes:a,firstTabbableNode:n.length>0?n[0]:null,lastTabbableNode:n.length>0?n[n.length-1]:null,nextTabbableNode:function(e){var n=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],o=a.findIndex((function(t){return t===e}));if(!(o<0))return n?a.slice(o+1).find((function(e){return t.isTabbable(e,b.tabbableOptions)})):a.slice(0,o).reverse().find((function(e){return t.isTabbable(e,b.tabbableOptions)}))}}})),f.tabbableGroups=f.containerGroups.filter((function(e){return e.tabbableNodes.length>0})),f.tabbableGroups.length<=0&&!p("fallbackFocus"))throw new Error("Your focus-trap must have at least one container with at least one tabbable node in it at all times")},m=function e(t){!1!==t&&t!==r.activeElement&&(t&&t.focus?(t.focus({preventScroll:!!b.preventScroll}),f.mostRecentlyFocusedNode=t,function(e){return e.tagName&&"input"===e.tagName.toLowerCase()&&"function"==typeof e.select}(t)&&t.select()):e(h()))},O=function(e){var t=p("setReturnFocus",e);return t||!1!==t&&e},g=function(e){var n=l(e);d(n)>=0||(s(b.clickOutsideDeactivates,e)?o.deactivate({returnFocus:b.returnFocusOnDeactivate&&!t.isFocusable(n,b.tabbableOptions)}):s(b.allowOutsideClick,e)||e.preventDefault())},T=function(e){var t=l(e),n=d(t)>=0;n||t instanceof Document?n&&(f.mostRecentlyFocusedNode=t):(e.stopImmediatePropagation(),m(f.mostRecentlyFocusedNode||h()))},F=function(e){if(function(e){return"Escape"===e.key||"Esc"===e.key||27===e.keyCode}(e)&&!1!==s(b.escapeDeactivates,e))return e.preventDefault(),void o.deactivate();(function(e){return"Tab"===e.key||9===e.keyCode})(e)&&function(e){var n=l(e);y();var a=null;if(f.tabbableGroups.length>0){var o=d(n),r=o>=0?f.containerGroups[o]:void 0;if(o<0)a=e.shiftKey?f.tabbableGroups[f.tabbableGroups.length-1].lastTabbableNode:f.tabbableGroups[0].firstTabbableNode;else if(e.shiftKey){var i=u(f.tabbableGroups,(function(e){var t=e.firstTabbableNode;return n===t}));if(i<0&&(r.container===n||t.isFocusable(n,b.tabbableOptions)&&!t.isTabbable(n,b.tabbableOptions)&&!r.nextTabbableNode(n,!1))&&(i=o),i>=0){var c=0===i?f.tabbableGroups.length-1:i-1;a=f.tabbableGroups[c].lastTabbableNode}}else{var s=u(f.tabbableGroups,(function(e){var t=e.lastTabbableNode;return n===t}));if(s<0&&(r.container===n||t.isFocusable(n,b.tabbableOptions)&&!t.isTabbable(n,b.tabbableOptions)&&!r.nextTabbableNode(n))&&(s=o),s>=0){var v=s===f.tabbableGroups.length-1?0:s+1;a=f.tabbableGroups[v].firstTabbableNode}}}else a=p("fallbackFocus");a&&(e.preventDefault(),m(a))}(e)},w=function(e){var t=l(e);d(t)>=0||s(b.clickOutsideDeactivates,e)||s(b.allowOutsideClick,e)||(e.preventDefault(),e.stopImmediatePropagation())},E=function(){if(f.active)return i.activateTrap(o),f.delayInitialFocusTimer=b.delayInitialFocus?c((function(){m(h())})):m(h()),r.addEventListener("focusin",T,!0),r.addEventListener("mousedown",g,{capture:!0,passive:!1}),r.addEventListener("touchstart",g,{capture:!0,passive:!1}),r.addEventListener("click",w,{capture:!0,passive:!1}),r.addEventListener("keydown",F,{capture:!0,passive:!1}),o},k=function(){if(f.active)return r.removeEventListener("focusin",T,!0),r.removeEventListener("mousedown",g,!0),r.removeEventListener("touchstart",g,!0),r.removeEventListener("click",w,!0),r.removeEventListener("keydown",F,!0),o};return(o={get active(){return f.active},get paused(){return f.paused},activate:function(e){if(f.active)return this;var t=v(e,"onActivate"),n=v(e,"onPostActivate"),a=v(e,"checkCanFocusTrap");a||y(),f.active=!0,f.paused=!1,f.nodeFocusedBeforeActivation=r.activeElement,t&&t();var o=function(){a&&y(),E(),n&&n()};return a?(a(f.containers.concat()).then(o,o),this):(o(),this)},deactivate:function(e){if(!f.active)return this;var t=a({onDeactivate:b.onDeactivate,onPostDeactivate:b.onPostDeactivate,checkCanReturnFocus:b.checkCanReturnFocus},e);clearTimeout(f.delayInitialFocusTimer),f.delayInitialFocusTimer=void 0,k(),f.active=!1,f.paused=!1,i.deactivateTrap(o);var n=v(t,"onDeactivate"),r=v(t,"onPostDeactivate"),u=v(t,"checkCanReturnFocus"),s=v(t,"returnFocus","returnFocusOnDeactivate");n&&n();var l=function(){c((function(){s&&m(O(f.nodeFocusedBeforeActivation)),r&&r()}))};return s&&u?(u(O(f.nodeFocusedBeforeActivation)).then(l,l),this):(l(),this)},pause:function(){return f.paused||!f.active||(f.paused=!0,k()),this},unpause:function(){return f.paused&&f.active?(f.paused=!1,y(),E(),this):this},updateContainerElements:function(e){var t=[].concat(e).filter(Boolean);return f.containers=t.map((function(e){return"string"==typeof e?r.querySelector(e):e})),f.active&&y(),this}}).updateContainerElements(e),o},Object.defineProperty(e,"__esModule",{value:!0})}));
//# sourceMappingURL=focus-trap.umd.min.js.map
