import { Logger } from '../utils/Logger';
import { IntelligenceEngine } from './IntelligenceEngine';
import { MemoryManager, Experience } from '../agent/MemoryManager';
import { TrainingManager } from './TrainingManager';

export interface AutoTrainingConfig {
  enabled: boolean;
  triggerThreshold: number; // Number of experiences before auto-training
  backgroundTraining: boolean;
  adaptiveFrequency: boolean;
  silentMode: boolean; // Train without showing messages
}

export interface TrainingTrigger {
  type: 'experience_count' | 'time_interval' | 'performance_drop' | 'new_domain';
  threshold: number;
  lastTriggered: Date;
}

export class AutoTrainingEngine {
  private logger: Logger;
  private intelligenceEngine: IntelligenceEngine;
  private memoryManager: MemoryManager;
  private trainingManager: TrainingManager;
  private config: AutoTrainingConfig;
  private triggers: TrainingTrigger[];
  private lastExperienceCount: number = 0;
  private isTraining: boolean = false;
  private isTaskExecuting: boolean = false;

  constructor(
    logger: Logger,
    intelligenceEngine: IntelligenceEngine,
    memoryManager: MemoryManager,
    trainingManager: TrainingManager
  ) {
    this.logger = logger;
    this.intelligenceEngine = intelligenceEngine;
    this.memoryManager = memoryManager;
    this.trainingManager = trainingManager;
    
    // Default auto-training configuration
    this.config = {
      enabled: true,
      triggerThreshold: 5, // Train after every 5 experiences
      backgroundTraining: true,
      adaptiveFrequency: true,
      silentMode: true // Silent by default to avoid noise
    };

    // Initialize training triggers
    this.triggers = [
      {
        type: 'experience_count',
        threshold: 5,
        lastTriggered: new Date()
      },
      {
        type: 'time_interval',
        threshold: 300000, // 5 minutes in milliseconds
        lastTriggered: new Date()
      }
    ];

    this.startAutoTrainingMonitor();
  }

  public enableAutoTraining(config?: Partial<AutoTrainingConfig>): void {
    this.config = { ...this.config, ...config, enabled: true };
    if (!this.config.silentMode) {
      this.logger.success('🧠 Auto-training enabled - Agent will learn continuously');
    }
  }

  public disableAutoTraining(): void {
    this.config.enabled = false;
    this.logger.info('🧠 Auto-training disabled');
  }

  public async onExperienceAdded(experience: Experience): Promise<void> {
    if (!this.config.enabled || this.isTraining || this.isTaskExecuting) return;

    // Check if we should trigger training
    const shouldTrain = this.shouldTriggerTraining();

    if (shouldTrain) {
      await this.triggerAutoTraining('experience_count');
    }
  }

  public async onFileOperation(operation: string, filePath: string, success: boolean): Promise<void> {
    if (!this.config.enabled || this.isTraining) return;

    // Record file operation as micro-experience for learning
    const experience: Experience = {
      id: this.generateId(),
      timestamp: new Date(),
      task: `File ${operation}: ${filePath}`,
      action: {
        type: operation === 'creating' ? 'create' : operation === 'editing' ? 'edit' : 'execute',
        instruction: `${operation} ${filePath}`,
        target: filePath,
        reasoning: `File operation: ${operation}`,
        confidence: 0.8
      },
      outcome: success ? 'success' : 'failure'
    };

    // Add to memory for learning
    this.memoryManager.addExperience(
      experience.task,
      experience.action,
      experience.outcome
    );

    // Check for training trigger
    await this.onExperienceAdded(experience);
  }

  public async onChatInteraction(userInput: string, aiResponse: string, success: boolean): Promise<void> {
    if (!this.config.enabled || this.isTraining) return;

    // Record chat interaction for learning
    const experience: Experience = {
      id: this.generateId(),
      timestamp: new Date(),
      task: `Chat: ${userInput.substring(0, 50)}...`,
      action: {
        type: 'analyze',
        instruction: userInput,
        target: 'conversation',
        reasoning: 'Chat interaction analysis',
        confidence: 0.7
      },
      outcome: success ? 'success' : 'failure'
    };

    this.memoryManager.addExperience(
      experience.task,
      experience.action,
      experience.outcome
    );

    await this.onExperienceAdded(experience);
  }

  private shouldTriggerTraining(): boolean {
    const currentExperienceCount = this.memoryManager.getAllExperiences().length;
    const newExperiences = currentExperienceCount - this.lastExperienceCount;

    // Experience count trigger
    if (newExperiences >= this.config.triggerThreshold) {
      return true;
    }

    // Time interval trigger
    const timeTrigger = this.triggers.find(t => t.type === 'time_interval');
    if (timeTrigger) {
      const timeSinceLastTrigger = Date.now() - timeTrigger.lastTriggered.getTime();
      if (timeSinceLastTrigger >= timeTrigger.threshold) {
        return true;
      }
    }

    return false;
  }

  private async triggerAutoTraining(triggerType: string): Promise<void> {
    if (this.isTraining) return;

    this.isTraining = true;
    
    try {
      if (!this.config.silentMode) {
        this.logger.info(`🧠 Auto-training triggered by: ${triggerType}`);
      }

      // Perform lightweight training
      await this.performLightweightTraining();

      // Update trigger timestamps
      const trigger = this.triggers.find(t => t.type === triggerType);
      if (trigger) {
        trigger.lastTriggered = new Date();
      }

      this.lastExperienceCount = this.memoryManager.getAllExperiences().length;

      if (!this.config.silentMode) {
        this.logger.success('✅ Auto-training completed');
      }

    } catch (error) {
      if (!this.config.silentMode) {
        this.logger.error(`❌ Auto-training failed: ${error instanceof Error ? error.message : String(error)}`);
      }
    } finally {
      this.isTraining = false;
    }
  }

  private async performLightweightTraining(): Promise<void> {
    // Lightweight training that doesn't interrupt user workflow

    try {
      // 1. Quick experience analysis (non-blocking)
      const recentExperiences = this.memoryManager.getRecentExperiences(10);
      if (recentExperiences.length > 0) {
        await this.intelligenceEngine.startContinuousLearning();
      }

      // 2. Pattern updates (fast, in-memory)
      this.memoryManager.quickTrainFromExperiences();

      // 3. Adaptive behavior updates (only if needed and performance allows)
      if (this.shouldAdaptBehavior() && this.canPerformAdaptiveBehaviorUpdate()) {
        await this.intelligenceEngine.adaptBehaviorBasedOnContext();
      }

      // 4. Update training metrics
      this.updateTrainingMetrics();

    } catch (error) {
      // Silent failure to avoid interrupting user workflow
      if (!this.config.silentMode) {
        this.logger.debug(`Lightweight training encountered issue: ${error}`);
      }
    }
  }

  private canPerformAdaptiveBehaviorUpdate(): boolean {
    // Only perform adaptive updates if system isn't under heavy load
    const memoryUsage = process.memoryUsage();
    const heapUsedMB = memoryUsage.heapUsed / 1024 / 1024;

    // Skip adaptive updates if memory usage is high (>100MB)
    return heapUsedMB < 100;
  }

  private updateTrainingMetrics(): void {
    // Update internal metrics for training effectiveness
    const experiences = this.memoryManager.getAllExperiences();
    const recentExperiences = this.memoryManager.getRecentExperiences(20);

    if (recentExperiences.length > 0) {
      const successRate = recentExperiences.filter(exp => exp.outcome === 'success').length / recentExperiences.length;

      // Adjust training frequency based on success rate
      if (this.config.adaptiveFrequency) {
        if (successRate > 0.9) {
          // High success rate - reduce training frequency
          this.config.triggerThreshold = Math.min(10, this.config.triggerThreshold + 1);
        } else if (successRate < 0.7) {
          // Low success rate - increase training frequency
          this.config.triggerThreshold = Math.max(3, this.config.triggerThreshold - 1);
        }
      }
    }
  }

  private shouldAdaptBehavior(): boolean {
    // Only adapt behavior occasionally to avoid overhead
    const recentExperiences = this.memoryManager.getRecentExperiences(10);
    const recentFailures = recentExperiences.filter(exp => exp.outcome === 'failure');
    
    // Adapt if failure rate is high
    return recentFailures.length / recentExperiences.length > 0.3;
  }

  private startAutoTrainingMonitor(): void {
    // Background monitor that checks for training opportunities
    if (this.config.backgroundTraining) {
      setInterval(() => {
        if (this.config.enabled && !this.isTraining && !this.isTaskExecuting) {
          this.checkPeriodicTraining();
        }
      }, 60000); // Check every minute
    }
  }

  private async checkPeriodicTraining(): Promise<void> {
    // Check if periodic training is needed
    const timeTrigger = this.triggers.find(t => t.type === 'time_interval');
    if (timeTrigger) {
      const timeSinceLastTrigger = Date.now() - timeTrigger.lastTriggered.getTime();
      if (timeSinceLastTrigger >= timeTrigger.threshold) {
        await this.triggerAutoTraining('time_interval');
      }
    }
  }

  public getAutoTrainingStatus(): {
    enabled: boolean;
    isTraining: boolean;
    experiencesSinceLastTraining: number;
    nextTrainingIn: string;
    config: AutoTrainingConfig;
  } {
    const currentExperiences = this.memoryManager.getAllExperiences().length;
    const experiencesSinceLastTraining = currentExperiences - this.lastExperienceCount;
    const experiencesUntilNext = Math.max(0, this.config.triggerThreshold - experiencesSinceLastTraining);
    
    const timeTrigger = this.triggers.find(t => t.type === 'time_interval');
    const timeUntilNext = timeTrigger ? 
      Math.max(0, timeTrigger.threshold - (Date.now() - timeTrigger.lastTriggered.getTime())) : 0;
    
    const nextTrainingIn = experiencesUntilNext > 0 ? 
      `${experiencesUntilNext} experiences` : 
      `${Math.round(timeUntilNext / 60000)} minutes`;

    return {
      enabled: this.config.enabled,
      isTraining: this.isTraining,
      experiencesSinceLastTraining,
      nextTrainingIn,
      config: this.config
    };
  }

  public updateConfig(newConfig: Partial<AutoTrainingConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    if (!this.config.silentMode) {
      this.logger.info('🧠 Auto-training configuration updated');
    }
  }

  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  // Task execution state management
  public setTaskExecuting(executing: boolean): void {
    this.isTaskExecuting = executing;
  }

  public isTaskCurrentlyExecuting(): boolean {
    return this.isTaskExecuting;
  }
}
