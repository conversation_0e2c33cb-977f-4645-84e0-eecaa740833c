import axios, { AxiosInstance } from 'axios';
import { AIProvider, ChatMessage, ChatResponse } from '../AIProviderManager';
import { AIProviderConfig } from '../../config/ConfigManager';
import { Logger } from '../../utils/Logger';

export class GeminiProvider implements AIProvider {
  public readonly name = '<PERSON>';
  private client: AxiosInstance;
  private config: AIProviderConfig;
  private logger: Logger;

  constructor(config: AIProviderConfig, logger: Logger) {
    this.config = config;
    this.logger = logger;
    this.client = axios.create({
      baseURL: config.baseUrl || 'https://generativelanguage.googleapis.com/v1beta',
      timeout: 120000, // Increased to 2 minutes
      headers: {
        'Content-Type': 'application/json',
      },
    });
  }

  public async isAvailable(): Promise<boolean> {
    try {
      const response = await this.client.get(`/models?key=${this.config.apiKey}`);
      return response.status === 200;
    } catch (error) {
      this.logger.debug(`Gemini availability check failed: ${error}`);
      return false;
    }
  }

  public async chat(messages: ChatMessage[], model?: string): Promise<ChatResponse> {
    try {
      const modelName = model || this.config.defaultModel || 'gemini-pro';
      
      // Convert messages to Gemini format
      const contents = this.formatMessages(messages);
      
      const response = await this.client.post(
        `/models/${modelName}:generateContent?key=${this.config.apiKey}`,
        {
          contents: contents,
          generationConfig: {
            temperature: 0.7,
            maxOutputTokens: 4000,
          },
        }
      );

      if (response.data && response.data.candidates && response.data.candidates.length > 0) {
        const candidate = response.data.candidates[0];
        const content = candidate.content?.parts?.[0]?.text || '';
        
        return {
          content: content,
          usage: {
            promptTokens: response.data.usageMetadata?.promptTokenCount || 0,
            completionTokens: response.data.usageMetadata?.candidatesTokenCount || 0,
            totalTokens: response.data.usageMetadata?.totalTokenCount || 0
          }
        };
      }

      throw new Error('Invalid response from Gemini');
    } catch (error) {
      if (axios.isAxiosError(error)) {
        if (error.response?.status === 401) {
          throw new Error('Invalid Gemini API key');
        }
        if (error.response?.status === 404) {
          throw new Error(`Model '${model || this.config.defaultModel}' not found`);
        }
        throw new Error(`Gemini API error: ${error.response?.data?.error?.message || error.message}`);
      }
      throw error;
    }
  }

  public async listModels(): Promise<string[]> {
    try {
      const response = await this.client.get(`/models?key=${this.config.apiKey}`);
      
      if (response.data && response.data.models) {
        return response.data.models
          .filter((model: any) => model.name.includes('gemini'))
          .map((model: any) => model.name.split('/').pop())
          .sort();
      }
      
      return [];
    } catch (error) {
      this.logger.error(`Failed to list Gemini models: ${error}`);
      return [];
    }
  }

  private formatMessages(messages: ChatMessage[]): any[] {
    const contents: any[] = [];
    
    for (const message of messages) {
      if (message.role === 'system') {
        // Gemini doesn't have a system role, so we'll prepend it to the first user message
        continue;
      }
      
      const role = message.role === 'assistant' ? 'model' : 'user';
      contents.push({
        role: role,
        parts: [{ text: message.content }]
      });
    }
    
    // If there was a system message, prepend it to the first user message
    const systemMessage = messages.find(m => m.role === 'system');
    if (systemMessage && contents.length > 0 && contents[0].role === 'user') {
      contents[0].parts[0].text = `${systemMessage.content}\n\n${contents[0].parts[0].text}`;
    }
    
    return contents;
  }
}
