# GOC Agent Commercial Distribution

## Overview

This is the commercial distribution version of GOC Agent - a comprehensive coding platform that includes:

- **CLI Tool**: Modified GOC Agent that connects to the existing Laravel backend
- **VS Code Extension**: Marketplace-ready extension for VS Code users
- **Desktop App**: Standalone Electron application with full GOC Agent capabilities

**Note**: Uses the existing Laravel backend from `../backend/` - no duplicate web application needed.

## Project Structure

```
goc-agent/
├── backend/               # Existing Laravel backend (shared)
└── goc-commercial/
    ├── cli/               # Commercial CLI version (backend-integrated)
    ├── extension/         # VS Code extension
    ├── desktop-app/       # Electron desktop application
    └── docs/              # Commercial documentation
```

## Development Strategy

### Phase 1: Backend-Integrated CLI
- Copy and modify the working GOC Agent CLI
- Integrate with Laravel backend for authentication and AI provider management
- Add subscription-based usage tracking

### Phase 2: Enhance Existing Backend
- Add subscription management to existing Laravel backend
- Implement user dashboard and analytics
- Add payment integration (Stripe/Paddle)
- Create landing page and marketing site

### Phase 3: VS Code Extension
- Extension that connects to the Laravel backend
- Marketplace-ready packaging and distribution
- OAuth integration for user authentication

### Phase 4: Desktop Application
- Electron-based standalone application
- Embedded GOC Agent functionality
- Cross-platform distribution (Windows/Mac/Linux)

## Key Differences from Personal Version

### Personal Version (Original)
- Direct AI provider integration (Ollama, OpenAI, etc.)
- Local configuration and storage
- No user accounts or billing
- Full local control

### Commercial Version (This Project)
- Backend-mediated AI provider access
- Cloud-based user accounts and settings
- Subscription-based billing and usage limits
- Centralized analytics and monitoring
- Multi-platform distribution

## Getting Started

1. **Backend Setup** (if not already running)
   ```bash
   cd ../backend
   composer install
   php artisan migrate
   php artisan serve
   ```

2. **Commercial CLI Development**
   ```bash
   cd cli
   npm install
   npm run build
   npm link
   ```

3. **Extension Development**
   ```bash
   cd extension
   npm install
   npm run compile
   ```

## Commercial Features

- **Subscription Management**: Free, Pro, Enterprise tiers
- **Usage Analytics**: Track AI interactions, code generation, and user behavior
- **Team Collaboration**: Shared projects and team management
- **Enterprise Features**: SSO, audit logs, custom AI models
- **Support & Documentation**: Professional support and comprehensive docs

## Monetization Strategy

### Subscription Tiers
- **Free**: Limited usage, basic features
- **Pro**: Higher limits, premium AI models, priority support
- **Enterprise**: Unlimited usage, custom deployment, dedicated support

### Distribution Channels
- Direct web sales through Laravel platform
- VS Code Marketplace
- Desktop app stores and direct downloads
- Enterprise sales and custom deployments

## Development Roadmap

- [ ] Phase 1: Backend-integrated CLI (Current)
- [ ] Phase 2: Laravel web application
- [ ] Phase 3: VS Code extension
- [ ] Phase 4: Desktop application
- [ ] Phase 5: Production deployment and marketing

## License

Commercial license - All rights reserved.
