import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { ConfigManager } from '../config/ConfigManager';
import { Logger } from '../utils/Logger';

export interface AuthResponse {
  success: boolean;
  token?: string;
  user?: any;
  message?: string;
}

export interface ChatRequest {
  session_id: string;
  message: string;
  provider?: string;
  model?: string;
}

export interface ChatResponse {
  user_message: any;
  assistant_message: any;
  session: any;
}

export interface SessionResponse {
  id: string;
  title: string;
  status: string;
  provider?: string;
  model?: string;
  created_at: string;
  updated_at: string;
}

export interface ProviderResponse {
  providers: {
    [key: string]: {
      name: string;
      available: boolean;
      models: string[];
    };
  };
}

export class BackendAPIClient {
  private client: AxiosInstance;
  private config: ConfigManager;
  private logger: Logger;
  private token?: string;

  constructor(config: ConfigManager, logger: Logger) {
    this.config = config;
    this.logger = logger;
    
    const backendConfig = config.getConfig().backend;
    
    this.client = axios.create({
      baseURL: backendConfig.url || 'http://localhost:8000/api',
      timeout: backendConfig.timeout || 30000,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    });

    // Load existing token if available
    this.token = config.getConfig().auth?.token;
    if (this.token) {
      this.setAuthToken(this.token);
    }

    // Add request interceptor for authentication
    this.client.interceptors.request.use((config) => {
      if (this.token) {
        config.headers.Authorization = `Bearer ${this.token}`;
      }
      return config;
    });

    // Add response interceptor for error handling
    this.client.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          this.logger.warn('Authentication expired. Please login again.');
          this.clearAuth();
        }
        return Promise.reject(error);
      }
    );
  }

  /**
   * Set authentication token
   */
  private setAuthToken(token: string): void {
    this.token = token;
    this.client.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    
    // Save token to config
    this.config.updateConfig({
      auth: {
        ...this.config.getConfig().auth,
        token: token,
      },
    });
  }

  /**
   * Clear authentication
   */
  private clearAuth(): void {
    this.token = undefined;
    delete this.client.defaults.headers.common['Authorization'];
    
    // Remove token from config
    this.config.updateConfig({
      auth: {
        ...this.config.getConfig().auth,
        token: undefined,
        user: undefined,
      },
    });
  }

  /**
   * Check if user is authenticated
   */
  public isAuthenticated(): boolean {
    return !!this.token;
  }

  /**
   * Register a new user
   */
  public async register(name: string, email: string, password: string): Promise<AuthResponse> {
    try {
      const response: AxiosResponse = await this.client.post('/auth/register', {
        name,
        email,
        password,
        password_confirmation: password,
      });

      if (response.data.token) {
        this.setAuthToken(response.data.token);
        
        // Save user info
        this.config.updateConfig({
          auth: {
            ...this.config.getConfig().auth,
            user: response.data.user,
          },
        });
      }

      return {
        success: true,
        token: response.data.token,
        user: response.data.user,
      };
    } catch (error: any) {
      this.logger.error('Registration failed:', error.response?.data?.message || error.message);
      return {
        success: false,
        message: error.response?.data?.message || 'Registration failed',
      };
    }
  }

  /**
   * Login user
   */
  public async login(email: string, password: string): Promise<AuthResponse> {
    try {
      const response: AxiosResponse = await this.client.post('/auth/login', {
        email,
        password,
      });

      if (response.data.token) {
        this.setAuthToken(response.data.token);
        
        // Save user info
        this.config.updateConfig({
          auth: {
            ...this.config.getConfig().auth,
            user: response.data.user,
          },
        });
      }

      return {
        success: true,
        token: response.data.token,
        user: response.data.user,
      };
    } catch (error: any) {
      this.logger.error('Login failed:', error.response?.data?.message || error.message);
      return {
        success: false,
        message: error.response?.data?.message || 'Login failed',
      };
    }
  }

  /**
   * Logout user
   */
  public async logout(): Promise<void> {
    try {
      if (this.token) {
        await this.client.post('/auth/logout');
      }
    } catch (error: any) {
      this.logger.debug('Logout request failed:', error.message);
    } finally {
      this.clearAuth();
    }
  }

  /**
   * Get current user info
   */
  public async getCurrentUser(): Promise<any> {
    try {
      const response: AxiosResponse = await this.client.get('/auth/user');
      return response.data;
    } catch (error: any) {
      this.logger.error('Failed to get user info:', error.response?.data?.message || error.message);
      throw error;
    }
  }

  /**
   * Create a new chat session
   */
  public async createSession(title?: string): Promise<SessionResponse> {
    try {
      const response: AxiosResponse = await this.client.post('/sessions', {
        title: title || `Session ${new Date().toLocaleString()}`,
      });
      return response.data;
    } catch (error: any) {
      this.logger.error('Failed to create session:', error.response?.data?.message || error.message);
      throw error;
    }
  }

  /**
   * Get user sessions
   */
  public async getSessions(): Promise<SessionResponse[]> {
    try {
      const response: AxiosResponse = await this.client.get('/sessions');
      return response.data;
    } catch (error: any) {
      this.logger.error('Failed to get sessions:', error.response?.data?.message || error.message);
      throw error;
    }
  }

  /**
   * Send chat message
   */
  public async chat(request: ChatRequest): Promise<ChatResponse> {
    try {
      const response: AxiosResponse = await this.client.post('/agent/chat', request);
      return response.data;
    } catch (error: any) {
      this.logger.error('Chat request failed:', error.response?.data?.message || error.message);
      throw error;
    }
  }

  /**
   * Execute agent task
   */
  public async executeTask(task: string, sessionId?: string, provider?: string, model?: string, autoMode?: boolean): Promise<any> {
    try {
      const response: AxiosResponse = await this.client.post('/agent/task', {
        task,
        session_id: sessionId,
        provider,
        model,
        auto_mode: autoMode,
      });
      return response.data;
    } catch (error: any) {
      this.logger.error('Task execution failed:', error.response?.data?.message || error.message);
      throw error;
    }
  }

  /**
   * Get available providers and models
   */
  public async getProviders(): Promise<ProviderResponse> {
    try {
      const response: AxiosResponse = await this.client.get('/agent/providers');
      return response.data;
    } catch (error: any) {
      this.logger.error('Failed to get providers:', error.response?.data?.message || error.message);
      throw error;
    }
  }

  /**
   * Get agent status and statistics
   */
  public async getStatus(): Promise<any> {
    try {
      const response: AxiosResponse = await this.client.get('/agent/status');
      return response.data;
    } catch (error: any) {
      this.logger.error('Failed to get status:', error.response?.data?.message || error.message);
      throw error;
    }
  }

  /**
   * Execute a tool
   */
  public async executeTool(tool: string, parameters: any = {}, sessionId?: string): Promise<any> {
    try {
      const response: AxiosResponse = await this.client.post(`/agent/tools/${tool}`, {
        parameters,
        session_id: sessionId,
      });
      return response.data;
    } catch (error: any) {
      this.logger.error(`Tool execution failed (${tool}):`, error.response?.data?.message || error.message);
      throw error;
    }
  }

  /**
   * Health check
   */
  public async healthCheck(): Promise<boolean> {
    try {
      const response: AxiosResponse = await this.client.get('/health');
      return response.data.status === 'ok';
    } catch (error: any) {
      this.logger.debug('Backend health check failed:', error.message);
      return false;
    }
  }
}
