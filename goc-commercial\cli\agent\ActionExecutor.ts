import fs from 'fs';
import { spawn } from 'child_process';
import { CodebaseAnalyzer } from '../codebase/CodebaseAnalyzer';
import { FileEditor } from '../commands/FileEditor';
import { ContextBuilder } from '../commands/ContextBuilder';
import { AIProviderManager } from '../ai/AIProviderManager';
import { Logger } from '../utils/Logger';
import { AgentAction } from './AgentCore';

export class ActionExecutor {
  private codebaseAnalyzer: CodebaseAnalyzer;
  private fileEditor: FileEditor;
  private logger: Logger;

  constructor(
    codebaseAnalyzer: CodebaseAnalyzer,
    fileEditor: FileEditor,
    logger: Logger
  ) {
    this.codebaseAnalyzer = codebaseAnalyzer;
    this.fileEditor = fileEditor;
    this.logger = logger;
  }

  public async execute(
    action: AgentAction,
    aiManager: AIProviderManager,
    contextBuilder: ContextBuilder
  ): Promise<any> {
    this.logger.info(`🎯 Executing ${action.type}: ${action.instruction}`);

    switch (action.type) {
      case 'analyze':
        return await this.executeAnalyze(action);
      
      case 'edit':
        return await this.executeEdit(action, aiManager, contextBuilder);
      
      case 'create':
        return await this.executeCreate(action, aiManager, contextBuilder);
      
      case 'search':
        return await this.executeSearch(action);
      
      case 'execute':
        return await this.executeCommand(action);
      
      case 'plan':
        return await this.executePlan(action);
      
      case 'reflect':
        return await this.executeReflect(action);
      
      default:
        throw new Error(`Unknown action type: ${action.type}`);
    }
  }

  private async executeAnalyze(action: AgentAction): Promise<any> {
    if (action.target) {
      // Analyze specific file
      if (fs.existsSync(action.target)) {
        const content = await this.codebaseAnalyzer.getFileContent(action.target);
        this.logger.info(`📄 Analyzed file: ${action.target} (${content.length} chars)`);
        return { type: 'file_analysis', file: action.target, content };
      } else {
        throw new Error(`File not found: ${action.target}`);
      }
    } else {
      // Analyze entire codebase
      const context = await this.codebaseAnalyzer.scanCodebase();
      this.logger.info(`📊 Analyzed codebase: ${context.totalFiles} files`);
      return { type: 'codebase_analysis', context };
    }
  }

  private async executeEdit(
    action: AgentAction,
    aiManager: AIProviderManager,
    contextBuilder: ContextBuilder
  ): Promise<any> {
    if (!action.target) {
      throw new Error('Edit action requires a target file');
    }

    if (!fs.existsSync(action.target)) {
      throw new Error(`File not found: ${action.target}`);
    }

    await this.fileEditor.editFile(action.target, action.instruction, aiManager, contextBuilder);
    this.logger.success(`✏️  Edited file: ${action.target}`);
    
    return { type: 'file_edited', file: action.target };
  }

  private async executeCreate(
    action: AgentAction,
    aiManager: AIProviderManager,
    contextBuilder: ContextBuilder
  ): Promise<any> {
    if (!action.target) {
      throw new Error('Create action requires a target file path');
    }

    await this.fileEditor.createFile(action.target, action.instruction, aiManager, contextBuilder);
    this.logger.success(`📝 Created file: ${action.target}`);
    
    return { type: 'file_created', file: action.target };
  }

  private async executeSearch(action: AgentAction): Promise<any> {
    if (!action.target) {
      throw new Error('Search action requires a search query');
    }

    const results = await this.codebaseAnalyzer.searchFiles(action.target);
    this.logger.info(`🔍 Search found ${results.length} matches for: ${action.target}`);
    
    return { 
      type: 'search_results', 
      query: action.target, 
      results: results.map(r => ({ path: r.relativePath, size: r.size }))
    };
  }

  private async executeCommand(action: AgentAction): Promise<any> {
    if (!action.target) {
      throw new Error('Execute action requires a command');
    }

    // Validate command - don't execute file paths as commands
    const command = action.target.trim();

    // Check if this looks like a file path rather than a command
    if (command.includes('/') && (command.endsWith('.html') || command.endsWith('.css') || command.endsWith('.js'))) {
      this.logger.error(`❌ Invalid command: "${command}" appears to be a file path, not a command`);
      throw new Error(`Invalid command: "${command}" appears to be a file path. Use 'create' action to create files.`);
    }

    return new Promise((resolve, reject) => {
      this.logger.info(`⚡ Executing command: ${command}`);

      const child = spawn(command, { shell: true, stdio: 'pipe' });
      let stdout = '';
      let stderr = '';

      child.stdout.on('data', (data: Buffer) => {
        stdout += data.toString();
      });

      child.stderr.on('data', (data: Buffer) => {
        stderr += data.toString();
      });

      child.on('close', (code: number | null) => {
        if (code === 0) {
          this.logger.success(`✅ Command completed successfully`);
          resolve({ type: 'command_executed', command: action.target, output: stdout });
        } else {
          this.logger.error(`❌ Command failed with code ${code}`);
          reject(new Error(`Command failed: ${stderr}`));
        }
      });

      child.on('error', (error: Error) => {
        this.logger.error(`❌ Command error: ${error.message}`);
        reject(error);
      });
    });
  }

  private async executePlan(action: AgentAction): Promise<any> {
    this.logger.info(`📋 Planning: ${action.instruction}`);
    // This would integrate with TaskPlanner for more complex planning
    return { type: 'plan_created', instruction: action.instruction };
  }

  private async executeReflect(action: AgentAction): Promise<any> {
    this.logger.info(`💭 Reflecting: ${action.instruction}`);
    // This would implement reflection logic
    return { type: 'reflection_completed', instruction: action.instruction };
  }
}
