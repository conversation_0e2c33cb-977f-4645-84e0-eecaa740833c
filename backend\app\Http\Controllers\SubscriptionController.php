<?php

namespace App\Http\Controllers;

use App\Models\SubscriptionPlan;
use App\Models\UserSubscription;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use <PERSON><PERSON>\Cashier\Cashier;

class SubscriptionController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Show subscription plans.
     */
    public function index()
    {
        $user = Auth::user();
        $currentSubscription = $user->subscription;
        $plans = SubscriptionPlan::active()->ordered()->get();

        return view('subscription.index', compact('currentSubscription', 'plans'));
    }

    /**
     * Subscribe to a plan.
     */
    public function subscribe(Request $request, SubscriptionPlan $plan)
    {
        $user = Auth::user();

        // Check if user already has an active subscription
        if ($user->subscription && $user->subscription->isActive()) {
            return redirect()->route('dashboard.subscription')
                ->with('error', 'You already have an active subscription.');
        }

        // For free plan, create subscription directly
        if ($plan->isFree()) {
            $this->createFreeSubscription($user, $plan);
            return redirect()->route('dashboard.subscription')
                ->with('success', 'Successfully subscribed to ' . $plan->name . ' plan!');
        }

        // For paid plans, redirect to Stripe checkout
        return $this->createStripeCheckout($user, $plan, $request->input('billing_cycle', 'monthly'));
    }

    /**
     * Handle successful payment.
     */
    public function success(Request $request)
    {
        return redirect()->route('dashboard.subscription')
            ->with('success', 'Payment successful! Your subscription is now active.');
    }

    /**
     * Handle canceled payment.
     */
    public function cancel()
    {
        return redirect()->route('dashboard.subscription')
            ->with('error', 'Payment was canceled.');
    }

    /**
     * Cancel subscription.
     */
    public function cancelSubscription()
    {
        $user = Auth::user();
        $subscription = $user->subscription;

        if (!$subscription || !$subscription->isActive()) {
            return redirect()->route('dashboard.subscription')
                ->with('error', 'No active subscription found.');
        }

        // If it's a Stripe subscription, cancel it
        if ($subscription->stripe_subscription_id) {
            $user->subscription($subscription->stripe_subscription_id)->cancel();
        }

        // Update local subscription
        $subscription->update([
            'status' => 'canceled',
            'canceled_at' => now(),
            'ends_at' => $subscription->current_period_end ?? now(),
        ]);

        return redirect()->route('dashboard.subscription')
            ->with('success', 'Subscription canceled successfully.');
    }

    /**
     * Resume subscription.
     */
    public function resumeSubscription()
    {
        $user = Auth::user();
        $subscription = $user->subscription;

        if (!$subscription || $subscription->status !== 'canceled') {
            return redirect()->route('dashboard.subscription')
                ->with('error', 'No canceled subscription found.');
        }

        // If it's a Stripe subscription, resume it
        if ($subscription->stripe_subscription_id) {
            $user->subscription($subscription->stripe_subscription_id)->resume();
        }

        // Update local subscription
        $subscription->update([
            'status' => 'active',
            'canceled_at' => null,
            'ends_at' => null,
        ]);

        return redirect()->route('dashboard.subscription')
            ->with('success', 'Subscription resumed successfully.');
    }

    /**
     * Create free subscription.
     */
    private function createFreeSubscription($user, $plan)
    {
        // Cancel existing subscription if any
        if ($user->subscription) {
            $user->subscription->delete();
        }

        UserSubscription::create([
            'user_id' => $user->id,
            'subscription_plan_id' => $plan->id,
            'status' => 'active',
            'billing_cycle' => 'monthly',
            'current_period_start' => now(),
            'current_period_end' => now()->addMonth(),
            'usage_reset_date' => now()->toDateString(),
        ]);
    }

    /**
     * Create Stripe checkout session.
     */
    private function createStripeCheckout($user, $plan, $billingCycle)
    {
        $priceId = $billingCycle === 'yearly' ? $plan->stripe_yearly_price_id : $plan->stripe_price_id;

        if (!$priceId) {
            return redirect()->route('dashboard.subscription')
                ->with('error', 'Stripe price ID not configured for this plan.');
        }

        return $user->newSubscription('default', $priceId)
            ->checkout([
                'success_url' => route('subscription.success'),
                'cancel_url' => route('subscription.cancel'),
            ]);
    }
}
