<?php

namespace App\Http\Middleware;

use App\Models\UserApiKey;
use App\Models\UsageLog;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Symfony\Component\HttpFoundation\Response;

class ApiKeyMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $apiKey = $request->header('Authorization');

        if (!$apiKey || !str_starts_with($apiKey, 'Bearer ')) {
            return response()->json(['error' => 'API key required'], 401);
        }

        $apiKey = substr($apiKey, 7); // Remove 'Bearer ' prefix

        if (!str_starts_with($apiKey, 'goc_')) {
            return response()->json(['error' => 'Invalid API key format'], 401);
        }

        // Find API key by prefix
        $keyPrefix = substr($apiKey, 0, 8);
        $userApiKey = UserApiKey::where('key_prefix', $keyPrefix)
            ->where('is_active', true)
            ->first();

        if (!$userApiKey || !Hash::check($apiKey, $userApiKey->key_hash)) {
            return response()->json(['error' => 'Invalid API key'], 401);
        }

        // Check if user has active subscription
        $user = $userApiKey->user;
        $subscription = $user->subscription;

        if (!$subscription || !$subscription->isActive()) {
            return response()->json(['error' => 'No active subscription'], 403);
        }

        // Check rate limits
        if ($subscription->getRemainingDailyRequests() <= 0) {
            return response()->json(['error' => 'Daily API limit exceeded'], 429);
        }

        if ($subscription->getRemainingMonthlyRequests() <= 0) {
            return response()->json(['error' => 'Monthly API limit exceeded'], 429);
        }

        // Set user and API key in request
        $request->merge([
            'authenticated_user' => $user,
            'api_key' => $userApiKey,
        ]);

        $startTime = microtime(true);
        $response = $next($request);
        $responseTime = (microtime(true) - $startTime) * 1000; // Convert to milliseconds

        // Log usage
        $this->logUsage($request, $response, $user, $userApiKey, $responseTime);

        // Update usage counters
        $this->updateUsageCounters($subscription, $userApiKey);

        return $response;
    }

    /**
     * Log API usage.
     */
    private function logUsage(Request $request, Response $response, $user, $userApiKey, $responseTime)
    {
        UsageLog::create([
            'user_id' => $user->id,
            'api_key_id' => $userApiKey->id,
            'endpoint' => $request->path(),
            'method' => $request->method(),
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'request_data' => $this->sanitizeData($request->all()),
            'response_data' => $this->sanitizeData($response->getContent()),
            'response_status' => $response->getStatusCode(),
            'response_time_ms' => round($responseTime),
            'tokens_used' => 0, // Will be updated by the actual API handler
            'cost' => 0, // Will be calculated based on tokens
            'session_id' => $request->header('X-Session-ID'),
            'created_at' => now(),
        ]);
    }

    /**
     * Update usage counters.
     */
    private function updateUsageCounters($subscription, $userApiKey)
    {
        // Update subscription counters
        $subscription->increment('api_requests_used_today');
        $subscription->increment('api_requests_used_month');

        // Update API key counters
        $userApiKey->increment('requests_today');
        $userApiKey->increment('requests_month');
        $userApiKey->increment('total_requests');
        $userApiKey->update([
            'last_used_at' => now(),
            'last_used_ip' => request()->ip(),
        ]);
    }

    /**
     * Sanitize sensitive data from logs.
     */
    private function sanitizeData($data)
    {
        if (is_string($data)) {
            $data = json_decode($data, true);
        }

        if (!is_array($data)) {
            return null;
        }

        // Remove sensitive fields
        $sensitiveFields = ['password', 'token', 'key', 'secret', 'api_key'];

        foreach ($sensitiveFields as $field) {
            if (isset($data[$field])) {
                $data[$field] = '[REDACTED]';
            }
        }

        return $data;
    }
}
