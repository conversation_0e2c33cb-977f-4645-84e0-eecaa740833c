<?php

/*
 * This file is part of the Predis package.
 *
 * (c) 2009-2020 <PERSON><PERSON> Alessandri
 * (c) 2021-2025 Till <PERSON>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Predis\Transaction\Response;

use Predis\Response\ResponseInterface;

/**
 * Wrapper for the responses that associated with commands executed bypassing transaction logic.
 */
class BypassTransactionResponse implements ResponseInterface
{
    /**
     * @var mixed
     */
    private $response;

    public function __construct($response)
    {
        $this->response = $response;
    }

    /**
     * @return mixed
     */
    public function getResponse()
    {
        return $this->response;
    }
}
