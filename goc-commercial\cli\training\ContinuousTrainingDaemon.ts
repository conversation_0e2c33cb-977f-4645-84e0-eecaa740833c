import { Logger } from '../utils/Logger';
import { SelfTrainingOrchestrator } from './SelfTrainingOrchestrator';
import { TrainingScheduler } from './TrainingScheduler';
import { MultiAspectTrainingCoordinator } from './MultiAspectTrainingCoordinator';
import { TrainingMetrics } from './TrainingMetrics';
import { MemoryManager } from '../agent/MemoryManager';
import { AIProviderManager } from '../ai/AIProviderManager';

export interface ContinuousTrainingConfig {
  enabled: boolean;
  intensity: 'minimal' | 'low' | 'medium' | 'high' | 'maximum';
  aspects: {
    coding: boolean;
    webResearch: boolean;
    performance: boolean;
    adaptiveBehavior: boolean;
    knowledgeExpansion: boolean;
    curiosityDriven: boolean;
  };
  scheduling: {
    quickTraining: number; // minutes
    deepTraining: number; // hours
    comprehensiveTraining: number; // days
  };
  resourceLimits: {
    maxCpuUsage: number; // percentage
    maxMemoryUsage: number; // MB
    maxNetworkRequests: number; // per hour
  };
}

export class ContinuousTrainingDaemon {
  private logger: Logger;
  private orchestrator: SelfTrainingOrchestrator;
  private scheduler: TrainingScheduler;
  private coordinator: MultiAspectTrainingCoordinator;
  private metrics: TrainingMetrics;
  private memoryManager: MemoryManager;
  private aiManager: AIProviderManager;
  
  private isRunning: boolean = false;
  private config: ContinuousTrainingConfig;
  private trainingIntervals: Map<string, NodeJS.Timeout> = new Map();
  private lastTrainingTimes: Map<string, Date> = new Map();

  constructor(
    logger: Logger,
    orchestrator: SelfTrainingOrchestrator,
    memoryManager: MemoryManager,
    aiManager: AIProviderManager
  ) {
    this.logger = logger;
    this.orchestrator = orchestrator;
    this.memoryManager = memoryManager;
    this.aiManager = aiManager;
    
    this.scheduler = new TrainingScheduler(logger);
    this.coordinator = new MultiAspectTrainingCoordinator(logger, orchestrator);
    this.metrics = new TrainingMetrics(logger, memoryManager);
    
    this.config = this.getDefaultConfig();
  }

  private getDefaultConfig(): ContinuousTrainingConfig {
    return {
      enabled: true,
      intensity: 'low',
      aspects: {
        coding: true,
        webResearch: true,
        performance: true,
        adaptiveBehavior: true,
        knowledgeExpansion: true,
        curiosityDriven: true
      },
      scheduling: {
        quickTraining: 30, // Every 30 minutes
        deepTraining: 4,   // Every 4 hours
        comprehensiveTraining: 1 // Every day
      },
      resourceLimits: {
        maxCpuUsage: 25,
        maxMemoryUsage: 512,
        maxNetworkRequests: 100
      }
    };
  }

  public async start(): Promise<void> {
    if (this.isRunning) {
      this.logger.warn('Continuous training daemon is already running');
      return;
    }

    this.logger.info('🚀 Starting continuous training daemon...');
    this.isRunning = true;

    try {
      // Initialize components
      await this.initializeComponents();
      
      // Start training schedules
      this.startTrainingSchedules();
      
      // Start monitoring
      this.startMonitoring();
      
      this.logger.success('✅ Continuous training daemon started successfully');
      this.logger.info(`📊 Training intensity: ${this.config.intensity}`);
      this.logger.info(`🎯 Active aspects: ${Object.entries(this.config.aspects).filter(([_, enabled]) => enabled).map(([aspect]) => aspect).join(', ')}`);
      
    } catch (error) {
      this.logger.error(`❌ Failed to start continuous training daemon: ${error}`);
      this.isRunning = false;
      throw error;
    }
  }

  public async stop(): Promise<void> {
    if (!this.isRunning) {
      this.logger.warn('Continuous training daemon is not running');
      return;
    }

    this.logger.info('🛑 Stopping continuous training daemon...');
    this.isRunning = false;

    // Clear all intervals
    for (const [name, interval] of this.trainingIntervals) {
      clearInterval(interval);
      this.logger.debug(`Cleared training interval: ${name}`);
    }
    this.trainingIntervals.clear();

    this.logger.success('✅ Continuous training daemon stopped');
  }

  public updateConfig(newConfig: Partial<ContinuousTrainingConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.logger.info('🔧 Updated continuous training configuration');
    
    if (this.isRunning) {
      this.logger.info('🔄 Restarting with new configuration...');
      this.stop().then(() => this.start());
    }
  }

  public getStatus(): any {
    return {
      isRunning: this.isRunning,
      config: this.config,
      lastTrainingTimes: Object.fromEntries(this.lastTrainingTimes),
      metrics: this.metrics.getOverallMetrics(),
      activeSchedules: Array.from(this.trainingIntervals.keys())
    };
  }

  private async initializeComponents(): Promise<void> {
    this.logger.debug('Initializing continuous training components...');
    
    await this.scheduler.initialize(this.config);
    await this.coordinator.initialize(this.config.aspects);
    await this.metrics.initialize();
    
    this.logger.debug('✅ Components initialized');
  }

  private startTrainingSchedules(): void {
    this.logger.debug('Starting training schedules...');
    
    // Quick training - frequent, lightweight sessions
    if (this.config.aspects.coding || this.config.aspects.performance) {
      const quickInterval = setInterval(
        () => this.executeQuickTraining(),
        this.config.scheduling.quickTraining * 60 * 1000
      );
      this.trainingIntervals.set('quick', quickInterval);
    }

    // Deep training - comprehensive sessions
    const deepInterval = setInterval(
      () => this.executeDeepTraining(),
      this.config.scheduling.deepTraining * 60 * 60 * 1000
    );
    this.trainingIntervals.set('deep', deepInterval);

    // Comprehensive training - full system training
    const comprehensiveInterval = setInterval(
      () => this.executeComprehensiveTraining(),
      this.config.scheduling.comprehensiveTraining * 24 * 60 * 60 * 1000
    );
    this.trainingIntervals.set('comprehensive', comprehensiveInterval);

    this.logger.debug('✅ Training schedules started');
  }

  private startMonitoring(): void {
    // Monitor system resources and training effectiveness
    const monitorInterval = setInterval(() => {
      this.monitorSystemHealth();
    }, 5 * 60 * 1000); // Every 5 minutes
    
    this.trainingIntervals.set('monitor', monitorInterval);
  }

  private async executeQuickTraining(): Promise<void> {
    if (!this.isRunning) return;
    
    this.logger.info('⚡ Starting quick training session...');
    this.lastTrainingTimes.set('quick', new Date());
    
    try {
      await this.coordinator.executeQuickTraining(this.config.intensity);
      this.metrics.recordTrainingSession('quick', true);
      this.logger.debug('✅ Quick training completed');
    } catch (error) {
      this.logger.error(`❌ Quick training failed: ${error}`);
      this.metrics.recordTrainingSession('quick', false);
    }
  }

  private async executeDeepTraining(): Promise<void> {
    if (!this.isRunning) return;
    
    this.logger.info('🔍 Starting deep training session...');
    this.lastTrainingTimes.set('deep', new Date());
    
    try {
      await this.coordinator.executeDeepTraining(this.config.intensity);
      this.metrics.recordTrainingSession('deep', true);
      this.logger.info('✅ Deep training completed');
    } catch (error) {
      this.logger.error(`❌ Deep training failed: ${error}`);
      this.metrics.recordTrainingSession('deep', false);
    }
  }

  private async executeComprehensiveTraining(): Promise<void> {
    if (!this.isRunning) return;

    this.logger.info('🎯 Starting comprehensive training session...');
    this.lastTrainingTimes.set('comprehensive', new Date());

    try {
      await this.orchestrator.startSelfTraining();
      this.metrics.recordTrainingSession('comprehensive', true);
      this.logger.success('✅ Comprehensive training completed');
    } catch (error) {
      this.logger.error(`❌ Comprehensive training failed: ${error}`);
      this.metrics.recordTrainingSession('comprehensive', false);
    }
  }

  private monitorSystemHealth(): void {
    // Basic system monitoring
    const memoryUsage = process.memoryUsage();
    const memoryMB = memoryUsage.heapUsed / 1024 / 1024;
    
    if (memoryMB > this.config.resourceLimits.maxMemoryUsage) {
      this.logger.warn(`⚠️ High memory usage: ${memoryMB.toFixed(2)}MB`);
      // Reduce training intensity temporarily
      this.temporarilyReduceIntensity();
    }
    
    this.metrics.recordSystemMetrics({
      memoryUsage: memoryMB,
      timestamp: new Date()
    });
  }

  private temporarilyReduceIntensity(): void {
    const originalIntensity = this.config.intensity;
    
    // Reduce intensity for 30 minutes
    this.config.intensity = 'minimal';
    this.logger.info('🔧 Temporarily reduced training intensity due to resource constraints');
    
    setTimeout(() => {
      this.config.intensity = originalIntensity;
      this.logger.info('🔧 Restored original training intensity');
    }, 30 * 60 * 1000);
  }
}
