import { Logger } from '../utils/Logger';
import { MemoryManager, Experience } from '../agent/MemoryManager';
import { KnowledgeRepository } from './KnowledgeRepository';

export interface PerformanceMetrics {
  successRate: number;
  averageResponseTime: number;
  taskCompletionRate: number;
  errorFrequency: number;
  learningVelocity: number;
  adaptabilityScore: number;
  autonomyLevel: number;
  knowledgeUtilization: number;
}

export interface PerformanceOptimization {
  id: string;
  type: 'speed' | 'accuracy' | 'efficiency' | 'learning';
  description: string;
  impact: number;
  effort: number;
  priority: number;
  implementation: string;
}

export interface BehaviorAnalysis {
  actionType: string;
  frequency: number;
  successRate: number;
  averageConfidence: number;
  improvementPotential: number;
  recommendations: string[];
}

export interface PerformanceTrend {
  metric: string;
  timeframe: string;
  trend: 'improving' | 'stable' | 'declining';
  changeRate: number;
  significance: number;
}

export class PerformanceMonitor {
  private logger: Logger;
  private memoryManager: MemoryManager;
  private knowledgeRepository: KnowledgeRepository;

  private performanceHistory: PerformanceMetrics[] = [];
  private optimizationHistory: PerformanceOptimization[] = [];
  private behaviorAnalysisCache: Map<string, BehaviorAnalysis> = new Map();

  constructor(
    logger: Logger,
    memoryManager: MemoryManager,
    knowledgeRepository: KnowledgeRepository
  ) {
    this.logger = logger;
    this.memoryManager = memoryManager;
    this.knowledgeRepository = knowledgeRepository;
  }

  public async evaluateCurrentPerformance(): Promise<PerformanceMetrics> {
    this.logger.info('📊 Evaluating current performance...');

    try {
      const experiences = this.memoryManager.getRecentExperiences(100);
      const knowledgeStats = this.knowledgeRepository.getKnowledgeStats();

      const metrics: PerformanceMetrics = {
        successRate: this.calculateSuccessRate(experiences),
        averageResponseTime: this.calculateAverageResponseTime(experiences),
        taskCompletionRate: this.calculateTaskCompletionRate(experiences),
        errorFrequency: this.calculateErrorFrequency(experiences),
        learningVelocity: this.calculateLearningVelocity(experiences),
        adaptabilityScore: this.calculateAdaptabilityScore(experiences),
        autonomyLevel: this.calculateAutonomyLevel(experiences),
        knowledgeUtilization: this.calculateKnowledgeUtilization(knowledgeStats)
      };

      // Store performance history
      this.performanceHistory.push(metrics);
      
      // Keep only last 50 performance snapshots
      if (this.performanceHistory.length > 50) {
        this.performanceHistory = this.performanceHistory.slice(-50);
      }

      this.logger.success(`✅ Performance evaluation completed - Success rate: ${(metrics.successRate * 100).toFixed(1)}%`);
      return metrics;

    } catch (error) {
      this.logger.error(`❌ Failed to evaluate performance: ${error}`);
      throw error;
    }
  }

  public async identifyOptimizations(): Promise<PerformanceOptimization[]> {
    this.logger.info('🔧 Identifying performance optimizations...');

    const optimizations: PerformanceOptimization[] = [];
    const currentMetrics = await this.evaluateCurrentPerformance();
    const experiences = this.memoryManager.getRecentExperiences(50);

    // Identify speed optimizations
    if (currentMetrics.averageResponseTime > 5000) { // 5 seconds
      optimizations.push({
        id: this.generateId(),
        type: 'speed',
        description: 'Optimize response time by caching frequent operations',
        impact: 0.8,
        effort: 0.4,
        priority: 0.8,
        implementation: 'Implement response caching and optimize AI provider selection'
      });
    }

    // Identify accuracy optimizations
    if (currentMetrics.successRate < 0.8) {
      optimizations.push({
        id: this.generateId(),
        type: 'accuracy',
        description: 'Improve task success rate through better pattern recognition',
        impact: 0.9,
        effort: 0.6,
        priority: 0.9,
        implementation: 'Enhance pattern matching and add more training examples'
      });
    }

    // Identify learning optimizations
    if (currentMetrics.learningVelocity < 0.5) {
      optimizations.push({
        id: this.generateId(),
        type: 'learning',
        description: 'Accelerate learning through improved knowledge integration',
        impact: 0.7,
        effort: 0.5,
        priority: 0.7,
        implementation: 'Optimize knowledge repository and enhance pattern extraction'
      });
    }

    // Identify efficiency optimizations
    const failurePatterns = this.identifyFailurePatterns(experiences);
    if (failurePatterns.length > 0) {
      optimizations.push({
        id: this.generateId(),
        type: 'efficiency',
        description: 'Reduce common failure patterns',
        impact: 0.6,
        effort: 0.3,
        priority: 0.6,
        implementation: `Address failure patterns: ${failurePatterns.join(', ')}`
      });
    }

    // Sort by priority
    optimizations.sort((a, b) => b.priority - a.priority);

    this.optimizationHistory.push(...optimizations);
    this.logger.success(`✅ Identified ${optimizations.length} optimization opportunities`);

    return optimizations;
  }

  public async analyzeBehaviorEffectiveness(): Promise<BehaviorAnalysis[]> {
    this.logger.info('🎯 Analyzing behavior effectiveness...');

    const experiences = this.memoryManager.getRecentExperiences(100);
    const behaviorMap = new Map<string, Experience[]>();

    // Group experiences by action type
    for (const exp of experiences) {
      if (exp.action) {
        const actionType = exp.action.type;
        if (!behaviorMap.has(actionType)) {
          behaviorMap.set(actionType, []);
        }
        behaviorMap.get(actionType)!.push(exp);
      }
    }

    const analyses: BehaviorAnalysis[] = [];

    for (const [actionType, actionExperiences] of behaviorMap) {
      const analysis = this.analyzeBehaviorType(actionType, actionExperiences);
      analyses.push(analysis);
      this.behaviorAnalysisCache.set(actionType, analysis);
    }

    // Sort by improvement potential
    analyses.sort((a, b) => b.improvementPotential - a.improvementPotential);

    this.logger.success(`✅ Analyzed ${analyses.length} behavior types`);
    return analyses;
  }

  public getPerformanceTrends(): PerformanceTrend[] {
    if (this.performanceHistory.length < 3) {
      return [];
    }

    const trends: PerformanceTrend[] = [];
    const recent = this.performanceHistory.slice(-5);
    const older = this.performanceHistory.slice(-10, -5);

    if (older.length === 0) return trends;

    const metrics = ['successRate', 'averageResponseTime', 'taskCompletionRate', 'learningVelocity'];

    for (const metric of metrics) {
      const recentAvg = this.calculateAverage(recent, metric as keyof PerformanceMetrics);
      const olderAvg = this.calculateAverage(older, metric as keyof PerformanceMetrics);
      
      const changeRate = (recentAvg - olderAvg) / olderAvg;
      const significance = Math.abs(changeRate);

      let trend: 'improving' | 'stable' | 'declining';
      if (changeRate > 0.05) {
        trend = metric === 'averageResponseTime' || metric === 'errorFrequency' ? 'declining' : 'improving';
      } else if (changeRate < -0.05) {
        trend = metric === 'averageResponseTime' || metric === 'errorFrequency' ? 'improving' : 'declining';
      } else {
        trend = 'stable';
      }

      trends.push({
        metric,
        timeframe: 'recent',
        trend,
        changeRate,
        significance
      });
    }

    return trends;
  }

  private calculateSuccessRate(experiences: Experience[]): number {
    if (experiences.length === 0) return 0;
    const successes = experiences.filter(exp => exp.outcome === 'success').length;
    return successes / experiences.length;
  }

  private calculateAverageResponseTime(experiences: Experience[]): number {
    // Simulate response time calculation
    // In real implementation, this would track actual response times
    return 2000 + Math.random() * 3000; // 2-5 seconds
  }

  private calculateTaskCompletionRate(experiences: Experience[]): number {
    if (experiences.length === 0) return 0;
    const completed = experiences.filter(exp => exp.action?.type !== 'plan').length;
    return completed / experiences.length;
  }

  private calculateErrorFrequency(experiences: Experience[]): number {
    if (experiences.length === 0) return 0;
    const errors = experiences.filter(exp => exp.outcome === 'failure').length;
    return errors / experiences.length;
  }

  private calculateLearningVelocity(experiences: Experience[]): number {
    // Calculate how quickly the agent is learning from experiences
    const recentExperiences = experiences.slice(-20);
    const olderExperiences = experiences.slice(-40, -20);

    if (olderExperiences.length === 0) return 0.5;

    const recentSuccessRate = this.calculateSuccessRate(recentExperiences);
    const olderSuccessRate = this.calculateSuccessRate(olderExperiences);

    const improvement = recentSuccessRate - olderSuccessRate;
    return Math.max(0, Math.min(1, 0.5 + improvement));
  }

  private calculateAdaptabilityScore(experiences: Experience[]): number {
    // Measure how well the agent adapts to different types of tasks
    const taskTypes = new Set(experiences.map(exp => exp.action?.type).filter(Boolean));
    const diversityScore = Math.min(taskTypes.size / 10, 1); // Normalize to 0-1

    const consistencyScore = this.calculateSuccessRate(experiences);
    
    return (diversityScore + consistencyScore) / 2;
  }

  private calculateAutonomyLevel(experiences: Experience[]): number {
    // Measure how independently the agent operates
    const autonomousActions = experiences.filter(exp => 
      exp.action && exp.action.confidence > 0.7
    ).length;
    
    if (experiences.length === 0) return 0;
    return autonomousActions / experiences.length;
  }

  private calculateKnowledgeUtilization(knowledgeStats: any): number {
    // Measure how effectively the agent uses its knowledge
    const { totalNodes, averageConfidence } = knowledgeStats;
    
    if (totalNodes === 0) return 0;
    
    // Combine knowledge quantity and quality
    const quantityScore = Math.min(totalNodes / 100, 1); // Normalize to 0-1
    const qualityScore = averageConfidence;
    
    return (quantityScore + qualityScore) / 2;
  }

  private identifyFailurePatterns(experiences: Experience[]): string[] {
    const failures = experiences.filter(exp => exp.outcome === 'failure');
    const patterns = new Map<string, number>();

    for (const failure of failures) {
      if (failure.error) {
        const pattern = this.extractErrorPattern(failure.error);
        patterns.set(pattern, (patterns.get(pattern) || 0) + 1);
      }
    }

    return Array.from(patterns.entries())
      .filter(([_, count]) => count >= 2)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 3)
      .map(([pattern, _]) => pattern);
  }

  private extractErrorPattern(error: string): string {
    const errorLower = error.toLowerCase();
    
    if (errorLower.includes('timeout')) return 'timeout';
    if (errorLower.includes('permission')) return 'permission';
    if (errorLower.includes('not found')) return 'not-found';
    if (errorLower.includes('syntax')) return 'syntax';
    if (errorLower.includes('network')) return 'network';
    
    return 'unknown';
  }

  private analyzeBehaviorType(actionType: string, experiences: Experience[]): BehaviorAnalysis {
    const frequency = experiences.length;
    const successRate = this.calculateSuccessRate(experiences);
    
    const confidences = experiences
      .map(exp => exp.action?.confidence || 0)
      .filter(conf => conf > 0);
    
    const averageConfidence = confidences.length > 0 
      ? confidences.reduce((sum, conf) => sum + conf, 0) / confidences.length 
      : 0;

    const improvementPotential = (1 - successRate) * frequency / 100;

    const recommendations: string[] = [];
    
    if (successRate < 0.7) {
      recommendations.push(`Improve ${actionType} success rate through better training`);
    }
    
    if (averageConfidence < 0.6) {
      recommendations.push(`Increase confidence in ${actionType} decisions`);
    }
    
    if (frequency > 20 && successRate < 0.8) {
      recommendations.push(`Focus optimization efforts on ${actionType} due to high frequency`);
    }

    return {
      actionType,
      frequency,
      successRate,
      averageConfidence,
      improvementPotential,
      recommendations
    };
  }

  private calculateAverage(metrics: PerformanceMetrics[], key: keyof PerformanceMetrics): number {
    if (metrics.length === 0) return 0;
    const sum = metrics.reduce((acc, metric) => acc + (metric[key] as number), 0);
    return sum / metrics.length;
  }

  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  public getCurrentMetrics(): PerformanceMetrics | null {
    return this.performanceHistory.length > 0 
      ? this.performanceHistory[this.performanceHistory.length - 1] 
      : null;
  }

  public getOptimizationHistory(): PerformanceOptimization[] {
    return [...this.optimizationHistory];
  }

  public getBehaviorAnalysis(actionType?: string): BehaviorAnalysis[] {
    if (actionType) {
      const analysis = this.behaviorAnalysisCache.get(actionType);
      return analysis ? [analysis] : [];
    }
    
    return Array.from(this.behaviorAnalysisCache.values());
  }
}
