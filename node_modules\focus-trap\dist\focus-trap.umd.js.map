{"version": 3, "file": "focus-trap.umd.js", "sources": ["../index.js"], "sourcesContent": ["import { tabbable, focusable, isFocusable, isTabbable } from 'tabbable';\n\nconst activeFocusTraps = (function () {\n  const trapQueue = [];\n  return {\n    activateTrap(trap) {\n      if (trapQueue.length > 0) {\n        const activeTrap = trapQueue[trapQueue.length - 1];\n        if (activeTrap !== trap) {\n          activeTrap.pause();\n        }\n      }\n\n      const trapIndex = trapQueue.indexOf(trap);\n      if (trapIndex === -1) {\n        trapQueue.push(trap);\n      } else {\n        // move this existing trap to the front of the queue\n        trapQueue.splice(trapIndex, 1);\n        trapQueue.push(trap);\n      }\n    },\n\n    deactivateTrap(trap) {\n      const trapIndex = trapQueue.indexOf(trap);\n      if (trapIndex !== -1) {\n        trapQueue.splice(trapIndex, 1);\n      }\n\n      if (trapQueue.length > 0) {\n        trapQueue[trapQueue.length - 1].unpause();\n      }\n    },\n  };\n})();\n\nconst isSelectableInput = function (node) {\n  return (\n    node.tagName &&\n    node.tagName.toLowerCase() === 'input' &&\n    typeof node.select === 'function'\n  );\n};\n\nconst isEscapeEvent = function (e) {\n  return e.key === 'Escape' || e.key === 'Esc' || e.keyCode === 27;\n};\n\nconst isTabEvent = function (e) {\n  return e.key === 'Tab' || e.keyCode === 9;\n};\n\nconst delay = function (fn) {\n  return setTimeout(fn, 0);\n};\n\n// Array.find/findIndex() are not supported on IE; this replicates enough\n//  of Array.findIndex() for our needs\nconst findIndex = function (arr, fn) {\n  let idx = -1;\n\n  arr.every(function (value, i) {\n    if (fn(value)) {\n      idx = i;\n      return false; // break\n    }\n\n    return true; // next\n  });\n\n  return idx;\n};\n\n/**\n * Get an option's value when it could be a plain value, or a handler that provides\n *  the value.\n * @param {*} value Option's value to check.\n * @param {...*} [params] Any parameters to pass to the handler, if `value` is a function.\n * @returns {*} The `value`, or the handler's returned value.\n */\nconst valueOrHandler = function (value, ...params) {\n  return typeof value === 'function' ? value(...params) : value;\n};\n\nconst getActualTarget = function (event) {\n  // NOTE: If the trap is _inside_ a shadow DOM, event.target will always be the\n  //  shadow host. However, event.target.composedPath() will be an array of\n  //  nodes \"clicked\" from inner-most (the actual element inside the shadow) to\n  //  outer-most (the host HTML document). If we have access to composedPath(),\n  //  then use its first element; otherwise, fall back to event.target (and\n  //  this only works for an _open_ shadow DOM; otherwise,\n  //  composedPath()[0] === event.target always).\n  return event.target.shadowRoot && typeof event.composedPath === 'function'\n    ? event.composedPath()[0]\n    : event.target;\n};\n\nconst createFocusTrap = function (elements, userOptions) {\n  // SSR: a live trap shouldn't be created in this type of environment so this\n  //  should be safe code to execute if the `document` option isn't specified\n  const doc = userOptions?.document || document;\n\n  const config = {\n    returnFocusOnDeactivate: true,\n    escapeDeactivates: true,\n    delayInitialFocus: true,\n    ...userOptions,\n  };\n\n  const state = {\n    // containers given to createFocusTrap()\n    // @type {Array<HTMLElement>}\n    containers: [],\n\n    // list of objects identifying tabbable nodes in `containers` in the trap\n    // NOTE: it's possible that a group has no tabbable nodes if nodes get removed while the trap\n    //  is active, but the trap should never get to a state where there isn't at least one group\n    //  with at least one tabbable node in it (that would lead to an error condition that would\n    //  result in an error being thrown)\n    // @type {Array<{\n    //   container: HTMLElement,\n    //   tabbableNodes: Array<HTMLElement>, // empty if none\n    //   focusableNodes: Array<HTMLElement>, // empty if none\n    //   firstTabbableNode: HTMLElement|null,\n    //   lastTabbableNode: HTMLElement|null,\n    //   nextTabbableNode: (node: HTMLElement, forward: boolean) => HTMLElement|undefined\n    // }>}\n    containerGroups: [], // same order/length as `containers` list\n\n    // references to objects in `containerGroups`, but only those that actually have\n    //  tabbable nodes in them\n    // NOTE: same order as `containers` and `containerGroups`, but __not necessarily__\n    //  the same length\n    tabbableGroups: [],\n\n    nodeFocusedBeforeActivation: null,\n    mostRecentlyFocusedNode: null,\n    active: false,\n    paused: false,\n\n    // timer ID for when delayInitialFocus is true and initial focus in this trap\n    //  has been delayed during activation\n    delayInitialFocusTimer: undefined,\n  };\n\n  let trap; // eslint-disable-line prefer-const -- some private functions reference it, and its methods reference private functions, so we must declare here and define later\n\n  /**\n   * Gets a configuration option value.\n   * @param {Object|undefined} configOverrideOptions If true, and option is defined in this set,\n   *  value will be taken from this object. Otherwise, value will be taken from base configuration.\n   * @param {string} optionName Name of the option whose value is sought.\n   * @param {string|undefined} [configOptionName] Name of option to use __instead of__ `optionName`\n   *  IIF `configOverrideOptions` is not defined. Otherwise, `optionName` is used.\n   */\n  const getOption = (configOverrideOptions, optionName, configOptionName) => {\n    return configOverrideOptions &&\n      configOverrideOptions[optionName] !== undefined\n      ? configOverrideOptions[optionName]\n      : config[configOptionName || optionName];\n  };\n\n  /**\n   * Finds the index of the container that contains the element.\n   * @param {HTMLElement} element\n   * @returns {number} Index of the container in either `state.containers` or\n   *  `state.containerGroups` (the order/length of these lists are the same); -1\n   *  if the element isn't found.\n   */\n  const findContainerIndex = function (element) {\n    // NOTE: search `containerGroups` because it's possible a group contains no tabbable\n    //  nodes, but still contains focusable nodes (e.g. if they all have `tabindex=-1`)\n    //  and we still need to find the element in there\n    return state.containerGroups.findIndex(\n      ({ container, tabbableNodes }) =>\n        container.contains(element) ||\n        // fall back to explicit tabbable search which will take into consideration any\n        //  web components if the `tabbableOptions.getShadowRoot` option was used for\n        //  the trap, enabling shadow DOM support in tabbable (`Node.contains()` doesn't\n        //  look inside web components even if open)\n        tabbableNodes.find((node) => node === element)\n    );\n  };\n\n  /**\n   * Gets the node for the given option, which is expected to be an option that\n   *  can be either a DOM node, a string that is a selector to get a node, `false`\n   *  (if a node is explicitly NOT given), or a function that returns any of these\n   *  values.\n   * @param {string} optionName\n   * @returns {undefined | false | HTMLElement | SVGElement} Returns\n   *  `undefined` if the option is not specified; `false` if the option\n   *  resolved to `false` (node explicitly not given); otherwise, the resolved\n   *  DOM node.\n   * @throws {Error} If the option is set, not `false`, and is not, or does not\n   *  resolve to a node.\n   */\n  const getNodeForOption = function (optionName, ...params) {\n    let optionValue = config[optionName];\n\n    if (typeof optionValue === 'function') {\n      optionValue = optionValue(...params);\n    }\n\n    if (optionValue === true) {\n      optionValue = undefined; // use default value\n    }\n\n    if (!optionValue) {\n      if (optionValue === undefined || optionValue === false) {\n        return optionValue;\n      }\n      // else, empty string (invalid), null (invalid), 0 (invalid)\n\n      throw new Error(\n        `\\`${optionName}\\` was specified but was not a node, or did not return a node`\n      );\n    }\n\n    let node = optionValue; // could be HTMLElement, SVGElement, or non-empty string at this point\n\n    if (typeof optionValue === 'string') {\n      node = doc.querySelector(optionValue); // resolve to node, or null if fails\n      if (!node) {\n        throw new Error(\n          `\\`${optionName}\\` as selector refers to no known node`\n        );\n      }\n    }\n\n    return node;\n  };\n\n  const getInitialFocusNode = function () {\n    let node = getNodeForOption('initialFocus');\n\n    // false explicitly indicates we want no initialFocus at all\n    if (node === false) {\n      return false;\n    }\n\n    if (node === undefined) {\n      // option not specified: use fallback options\n      if (findContainerIndex(doc.activeElement) >= 0) {\n        node = doc.activeElement;\n      } else {\n        const firstTabbableGroup = state.tabbableGroups[0];\n        const firstTabbableNode =\n          firstTabbableGroup && firstTabbableGroup.firstTabbableNode;\n\n        // NOTE: `fallbackFocus` option function cannot return `false` (not supported)\n        node = firstTabbableNode || getNodeForOption('fallbackFocus');\n      }\n    }\n\n    if (!node) {\n      throw new Error(\n        'Your focus-trap needs to have at least one focusable element'\n      );\n    }\n\n    return node;\n  };\n\n  const updateTabbableNodes = function () {\n    state.containerGroups = state.containers.map((container) => {\n      const tabbableNodes = tabbable(container, config.tabbableOptions);\n\n      // NOTE: if we have tabbable nodes, we must have focusable nodes; focusable nodes\n      //  are a superset of tabbable nodes\n      const focusableNodes = focusable(container, config.tabbableOptions);\n\n      return {\n        container,\n        tabbableNodes,\n        focusableNodes,\n        firstTabbableNode: tabbableNodes.length > 0 ? tabbableNodes[0] : null,\n        lastTabbableNode:\n          tabbableNodes.length > 0\n            ? tabbableNodes[tabbableNodes.length - 1]\n            : null,\n\n        /**\n         * Finds the __tabbable__ node that follows the given node in the specified direction,\n         *  in this container, if any.\n         * @param {HTMLElement} node\n         * @param {boolean} [forward] True if going in forward tab order; false if going\n         *  in reverse.\n         * @returns {HTMLElement|undefined} The next tabbable node, if any.\n         */\n        nextTabbableNode(node, forward = true) {\n          // NOTE: If tabindex is positive (in order to manipulate the tab order separate\n          //  from the DOM order), this __will not work__ because the list of focusableNodes,\n          //  while it contains tabbable nodes, does not sort its nodes in any order other\n          //  than DOM order, because it can't: Where would you place focusable (but not\n          //  tabbable) nodes in that order? They have no order, because they aren't tabbale...\n          // Support for positive tabindex is already broken and hard to manage (possibly\n          //  not supportable, TBD), so this isn't going to make things worse than they\n          //  already are, and at least makes things better for the majority of cases where\n          //  tabindex is either 0/unset or negative.\n          // FYI, positive tabindex issue: https://github.com/focus-trap/focus-trap/issues/375\n          const nodeIdx = focusableNodes.findIndex((n) => n === node);\n          if (nodeIdx < 0) {\n            return undefined;\n          }\n\n          if (forward) {\n            return focusableNodes\n              .slice(nodeIdx + 1)\n              .find((n) => isTabbable(n, config.tabbableOptions));\n          }\n\n          return focusableNodes\n            .slice(0, nodeIdx)\n            .reverse()\n            .find((n) => isTabbable(n, config.tabbableOptions));\n        },\n      };\n    });\n\n    state.tabbableGroups = state.containerGroups.filter(\n      (group) => group.tabbableNodes.length > 0\n    );\n\n    // throw if no groups have tabbable nodes and we don't have a fallback focus node either\n    if (\n      state.tabbableGroups.length <= 0 &&\n      !getNodeForOption('fallbackFocus') // returning false not supported for this option\n    ) {\n      throw new Error(\n        'Your focus-trap must have at least one container with at least one tabbable node in it at all times'\n      );\n    }\n  };\n\n  const tryFocus = function (node) {\n    if (node === false) {\n      return;\n    }\n\n    if (node === doc.activeElement) {\n      return;\n    }\n\n    if (!node || !node.focus) {\n      tryFocus(getInitialFocusNode());\n      return;\n    }\n\n    node.focus({ preventScroll: !!config.preventScroll });\n    state.mostRecentlyFocusedNode = node;\n\n    if (isSelectableInput(node)) {\n      node.select();\n    }\n  };\n\n  const getReturnFocusNode = function (previousActiveElement) {\n    const node = getNodeForOption('setReturnFocus', previousActiveElement);\n    return node ? node : node === false ? false : previousActiveElement;\n  };\n\n  // This needs to be done on mousedown and touchstart instead of click\n  // so that it precedes the focus event.\n  const checkPointerDown = function (e) {\n    const target = getActualTarget(e);\n\n    if (findContainerIndex(target) >= 0) {\n      // allow the click since it ocurred inside the trap\n      return;\n    }\n\n    if (valueOrHandler(config.clickOutsideDeactivates, e)) {\n      // immediately deactivate the trap\n      trap.deactivate({\n        // if, on deactivation, we should return focus to the node originally-focused\n        //  when the trap was activated (or the configured `setReturnFocus` node),\n        //  then assume it's also OK to return focus to the outside node that was\n        //  just clicked, causing deactivation, as long as that node is focusable;\n        //  if it isn't focusable, then return focus to the original node focused\n        //  on activation (or the configured `setReturnFocus` node)\n        // NOTE: by setting `returnFocus: false`, deactivate() will do nothing,\n        //  which will result in the outside click setting focus to the node\n        //  that was clicked, whether it's focusable or not; by setting\n        //  `returnFocus: true`, we'll attempt to re-focus the node originally-focused\n        //  on activation (or the configured `setReturnFocus` node)\n        returnFocus:\n          config.returnFocusOnDeactivate &&\n          !isFocusable(target, config.tabbableOptions),\n      });\n      return;\n    }\n\n    // This is needed for mobile devices.\n    // (If we'll only let `click` events through,\n    // then on mobile they will be blocked anyways if `touchstart` is blocked.)\n    if (valueOrHandler(config.allowOutsideClick, e)) {\n      // allow the click outside the trap to take place\n      return;\n    }\n\n    // otherwise, prevent the click\n    e.preventDefault();\n  };\n\n  // In case focus escapes the trap for some strange reason, pull it back in.\n  const checkFocusIn = function (e) {\n    const target = getActualTarget(e);\n    const targetContained = findContainerIndex(target) >= 0;\n\n    // In Firefox when you Tab out of an iframe the Document is briefly focused.\n    if (targetContained || target instanceof Document) {\n      if (targetContained) {\n        state.mostRecentlyFocusedNode = target;\n      }\n    } else {\n      // escaped! pull it back in to where it just left\n      e.stopImmediatePropagation();\n      tryFocus(state.mostRecentlyFocusedNode || getInitialFocusNode());\n    }\n  };\n\n  // Hijack Tab events on the first and last focusable nodes of the trap,\n  // in order to prevent focus from escaping. If it escapes for even a\n  // moment it can end up scrolling the page and causing confusion so we\n  // kind of need to capture the action at the keydown phase.\n  const checkTab = function (e) {\n    const target = getActualTarget(e);\n    updateTabbableNodes();\n\n    let destinationNode = null;\n\n    if (state.tabbableGroups.length > 0) {\n      // make sure the target is actually contained in a group\n      // NOTE: the target may also be the container itself if it's focusable\n      //  with tabIndex='-1' and was given initial focus\n      const containerIndex = findContainerIndex(target);\n      const containerGroup =\n        containerIndex >= 0 ? state.containerGroups[containerIndex] : undefined;\n\n      if (containerIndex < 0) {\n        // target not found in any group: quite possible focus has escaped the trap,\n        //  so bring it back in to...\n        if (e.shiftKey) {\n          // ...the last node in the last group\n          destinationNode =\n            state.tabbableGroups[state.tabbableGroups.length - 1]\n              .lastTabbableNode;\n        } else {\n          // ...the first node in the first group\n          destinationNode = state.tabbableGroups[0].firstTabbableNode;\n        }\n      } else if (e.shiftKey) {\n        // REVERSE\n\n        // is the target the first tabbable node in a group?\n        let startOfGroupIndex = findIndex(\n          state.tabbableGroups,\n          ({ firstTabbableNode }) => target === firstTabbableNode\n        );\n\n        if (\n          startOfGroupIndex < 0 &&\n          (containerGroup.container === target ||\n            (isFocusable(target, config.tabbableOptions) &&\n              !isTabbable(target, config.tabbableOptions) &&\n              !containerGroup.nextTabbableNode(target, false)))\n        ) {\n          // an exception case where the target is either the container itself, or\n          //  a non-tabbable node that was given focus (i.e. tabindex is negative\n          //  and user clicked on it or node was programmatically given focus)\n          //  and is not followed by any other tabbable node, in which\n          //  case, we should handle shift+tab as if focus were on the container's\n          //  first tabbable node, and go to the last tabbable node of the LAST group\n          startOfGroupIndex = containerIndex;\n        }\n\n        if (startOfGroupIndex >= 0) {\n          // YES: then shift+tab should go to the last tabbable node in the\n          //  previous group (and wrap around to the last tabbable node of\n          //  the LAST group if it's the first tabbable node of the FIRST group)\n          const destinationGroupIndex =\n            startOfGroupIndex === 0\n              ? state.tabbableGroups.length - 1\n              : startOfGroupIndex - 1;\n\n          const destinationGroup = state.tabbableGroups[destinationGroupIndex];\n          destinationNode = destinationGroup.lastTabbableNode;\n        }\n      } else {\n        // FORWARD\n\n        // is the target the last tabbable node in a group?\n        let lastOfGroupIndex = findIndex(\n          state.tabbableGroups,\n          ({ lastTabbableNode }) => target === lastTabbableNode\n        );\n\n        if (\n          lastOfGroupIndex < 0 &&\n          (containerGroup.container === target ||\n            (isFocusable(target, config.tabbableOptions) &&\n              !isTabbable(target, config.tabbableOptions) &&\n              !containerGroup.nextTabbableNode(target)))\n        ) {\n          // an exception case where the target is the container itself, or\n          //  a non-tabbable node that was given focus (i.e. tabindex is negative\n          //  and user clicked on it or node was programmatically given focus)\n          //  and is not followed by any other tabbable node, in which\n          //  case, we should handle tab as if focus were on the container's\n          //  last tabbable node, and go to the first tabbable node of the FIRST group\n          lastOfGroupIndex = containerIndex;\n        }\n\n        if (lastOfGroupIndex >= 0) {\n          // YES: then tab should go to the first tabbable node in the next\n          //  group (and wrap around to the first tabbable node of the FIRST\n          //  group if it's the last tabbable node of the LAST group)\n          const destinationGroupIndex =\n            lastOfGroupIndex === state.tabbableGroups.length - 1\n              ? 0\n              : lastOfGroupIndex + 1;\n\n          const destinationGroup = state.tabbableGroups[destinationGroupIndex];\n          destinationNode = destinationGroup.firstTabbableNode;\n        }\n      }\n    } else {\n      // NOTE: the fallbackFocus option does not support returning false to opt-out\n      destinationNode = getNodeForOption('fallbackFocus');\n    }\n\n    if (destinationNode) {\n      e.preventDefault();\n      tryFocus(destinationNode);\n    }\n    // else, let the browser take care of [shift+]tab and move the focus\n  };\n\n  const checkKey = function (e) {\n    if (\n      isEscapeEvent(e) &&\n      valueOrHandler(config.escapeDeactivates, e) !== false\n    ) {\n      e.preventDefault();\n      trap.deactivate();\n      return;\n    }\n\n    if (isTabEvent(e)) {\n      checkTab(e);\n      return;\n    }\n  };\n\n  const checkClick = function (e) {\n    const target = getActualTarget(e);\n\n    if (findContainerIndex(target) >= 0) {\n      return;\n    }\n\n    if (valueOrHandler(config.clickOutsideDeactivates, e)) {\n      return;\n    }\n\n    if (valueOrHandler(config.allowOutsideClick, e)) {\n      return;\n    }\n\n    e.preventDefault();\n    e.stopImmediatePropagation();\n  };\n\n  //\n  // EVENT LISTENERS\n  //\n\n  const addListeners = function () {\n    if (!state.active) {\n      return;\n    }\n\n    // There can be only one listening focus trap at a time\n    activeFocusTraps.activateTrap(trap);\n\n    // Delay ensures that the focused element doesn't capture the event\n    // that caused the focus trap activation.\n    state.delayInitialFocusTimer = config.delayInitialFocus\n      ? delay(function () {\n          tryFocus(getInitialFocusNode());\n        })\n      : tryFocus(getInitialFocusNode());\n\n    doc.addEventListener('focusin', checkFocusIn, true);\n    doc.addEventListener('mousedown', checkPointerDown, {\n      capture: true,\n      passive: false,\n    });\n    doc.addEventListener('touchstart', checkPointerDown, {\n      capture: true,\n      passive: false,\n    });\n    doc.addEventListener('click', checkClick, {\n      capture: true,\n      passive: false,\n    });\n    doc.addEventListener('keydown', checkKey, {\n      capture: true,\n      passive: false,\n    });\n\n    return trap;\n  };\n\n  const removeListeners = function () {\n    if (!state.active) {\n      return;\n    }\n\n    doc.removeEventListener('focusin', checkFocusIn, true);\n    doc.removeEventListener('mousedown', checkPointerDown, true);\n    doc.removeEventListener('touchstart', checkPointerDown, true);\n    doc.removeEventListener('click', checkClick, true);\n    doc.removeEventListener('keydown', checkKey, true);\n\n    return trap;\n  };\n\n  //\n  // TRAP DEFINITION\n  //\n\n  trap = {\n    get active() {\n      return state.active;\n    },\n\n    get paused() {\n      return state.paused;\n    },\n\n    activate(activateOptions) {\n      if (state.active) {\n        return this;\n      }\n\n      const onActivate = getOption(activateOptions, 'onActivate');\n      const onPostActivate = getOption(activateOptions, 'onPostActivate');\n      const checkCanFocusTrap = getOption(activateOptions, 'checkCanFocusTrap');\n\n      if (!checkCanFocusTrap) {\n        updateTabbableNodes();\n      }\n\n      state.active = true;\n      state.paused = false;\n      state.nodeFocusedBeforeActivation = doc.activeElement;\n\n      if (onActivate) {\n        onActivate();\n      }\n\n      const finishActivation = () => {\n        if (checkCanFocusTrap) {\n          updateTabbableNodes();\n        }\n        addListeners();\n        if (onPostActivate) {\n          onPostActivate();\n        }\n      };\n\n      if (checkCanFocusTrap) {\n        checkCanFocusTrap(state.containers.concat()).then(\n          finishActivation,\n          finishActivation\n        );\n        return this;\n      }\n\n      finishActivation();\n      return this;\n    },\n\n    deactivate(deactivateOptions) {\n      if (!state.active) {\n        return this;\n      }\n\n      const options = {\n        onDeactivate: config.onDeactivate,\n        onPostDeactivate: config.onPostDeactivate,\n        checkCanReturnFocus: config.checkCanReturnFocus,\n        ...deactivateOptions,\n      };\n\n      clearTimeout(state.delayInitialFocusTimer); // noop if undefined\n      state.delayInitialFocusTimer = undefined;\n\n      removeListeners();\n      state.active = false;\n      state.paused = false;\n\n      activeFocusTraps.deactivateTrap(trap);\n\n      const onDeactivate = getOption(options, 'onDeactivate');\n      const onPostDeactivate = getOption(options, 'onPostDeactivate');\n      const checkCanReturnFocus = getOption(options, 'checkCanReturnFocus');\n      const returnFocus = getOption(\n        options,\n        'returnFocus',\n        'returnFocusOnDeactivate'\n      );\n\n      if (onDeactivate) {\n        onDeactivate();\n      }\n\n      const finishDeactivation = () => {\n        delay(() => {\n          if (returnFocus) {\n            tryFocus(getReturnFocusNode(state.nodeFocusedBeforeActivation));\n          }\n          if (onPostDeactivate) {\n            onPostDeactivate();\n          }\n        });\n      };\n\n      if (returnFocus && checkCanReturnFocus) {\n        checkCanReturnFocus(\n          getReturnFocusNode(state.nodeFocusedBeforeActivation)\n        ).then(finishDeactivation, finishDeactivation);\n        return this;\n      }\n\n      finishDeactivation();\n      return this;\n    },\n\n    pause() {\n      if (state.paused || !state.active) {\n        return this;\n      }\n\n      state.paused = true;\n      removeListeners();\n\n      return this;\n    },\n\n    unpause() {\n      if (!state.paused || !state.active) {\n        return this;\n      }\n\n      state.paused = false;\n      updateTabbableNodes();\n      addListeners();\n\n      return this;\n    },\n\n    updateContainerElements(containerElements) {\n      const elementsAsArray = [].concat(containerElements).filter(Boolean);\n\n      state.containers = elementsAsArray.map((element) =>\n        typeof element === 'string' ? doc.querySelector(element) : element\n      );\n\n      if (state.active) {\n        updateTabbableNodes();\n      }\n\n      return this;\n    },\n  };\n\n  // initialize container elements\n  trap.updateContainerElements(elements);\n\n  return trap;\n};\n\nexport { createFocusTrap };\n"], "names": ["activeFocusTraps", "trapQueue", "activateTrap", "trap", "length", "activeTrap", "pause", "trapIndex", "indexOf", "push", "splice", "deactivateTrap", "unpause", "isSelectableInput", "node", "tagName", "toLowerCase", "select", "isEscapeEvent", "e", "key", "keyCode", "isTabEvent", "delay", "fn", "setTimeout", "findIndex", "arr", "idx", "every", "value", "i", "valueOrHandler", "params", "getActualTarget", "event", "target", "shadowRoot", "<PERSON><PERSON><PERSON>", "createFocusTrap", "elements", "userOptions", "doc", "document", "config", "_objectSpread", "returnFocusOnDeactivate", "escapeDeactivates", "delayInitialFocus", "state", "containers", "containerGroups", "tabbableGroups", "nodeFocusedBeforeActivation", "mostRecentlyFocusedNode", "active", "paused", "delayInitialFocusTimer", "undefined", "getOption", "configOverrideOptions", "optionName", "configOptionName", "findContainerIndex", "element", "container", "tabbableNodes", "contains", "find", "getNodeForOption", "optionValue", "Error", "querySelector", "getInitialFocusNode", "activeElement", "firstTabbableGroup", "firstTabbableNode", "updateTabbableNodes", "map", "tabbable", "tabbableOptions", "focusableNodes", "focusable", "lastTabbableNode", "nextTabbableNode", "forward", "nodeIdx", "n", "slice", "isTabbable", "reverse", "filter", "group", "tryFocus", "focus", "preventScroll", "getReturnFocusNode", "previousActiveElement", "checkPointerDown", "clickOutsideDeactivates", "deactivate", "returnFocus", "isFocusable", "allowOutsideClick", "preventDefault", "checkFocusIn", "targetContained", "Document", "stopImmediatePropagation", "checkTab", "destinationNode", "containerIndex", "containerGroup", "shift<PERSON>ey", "startOfGroupIndex", "destinationGroupIndex", "destinationGroup", "lastOfGroupIndex", "<PERSON><PERSON><PERSON>", "checkClick", "addListeners", "addEventListener", "capture", "passive", "removeListeners", "removeEventListener", "activate", "activateOptions", "onActivate", "onPostActivate", "checkCanFocusTrap", "finishActivation", "concat", "then", "deactivateOptions", "options", "onDeactivate", "onPostDeactivate", "checkCanReturnFocus", "clearTimeout", "finishDeactivation", "updateContainerElements", "containerElements", "elementsAsArray", "Boolean"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAEA,IAAMA,gBAAgB,GAAI,YAAY;IACpC,IAAMC,SAAS,GAAG,EAAlB,CAAA;IACA,OAAO;MACLC,YADK,EAAA,SAAA,YAAA,CACQC,IADR,EACc;EACjB,MAAA,IAAIF,SAAS,CAACG,MAAV,GAAmB,CAAvB,EAA0B;UACxB,IAAMC,UAAU,GAAGJ,SAAS,CAACA,SAAS,CAACG,MAAV,GAAmB,CAApB,CAA5B,CAAA;;UACA,IAAIC,UAAU,KAAKF,IAAnB,EAAyB;EACvBE,UAAAA,UAAU,CAACC,KAAX,EAAA,CAAA;EACD,SAAA;EACF,OAAA;;EAED,MAAA,IAAMC,SAAS,GAAGN,SAAS,CAACO,OAAV,CAAkBL,IAAlB,CAAlB,CAAA;;EACA,MAAA,IAAII,SAAS,KAAK,CAAC,CAAnB,EAAsB;UACpBN,SAAS,CAACQ,IAAV,CAAeN,IAAf,CAAA,CAAA;EACD,OAFD,MAEO;EACL;EACAF,QAAAA,SAAS,CAACS,MAAV,CAAiBH,SAAjB,EAA4B,CAA5B,CAAA,CAAA;UACAN,SAAS,CAACQ,IAAV,CAAeN,IAAf,CAAA,CAAA;EACD,OAAA;OAhBE;MAmBLQ,cAnBK,EAAA,SAAA,cAAA,CAmBUR,IAnBV,EAmBgB;EACnB,MAAA,IAAMI,SAAS,GAAGN,SAAS,CAACO,OAAV,CAAkBL,IAAlB,CAAlB,CAAA;;EACA,MAAA,IAAII,SAAS,KAAK,CAAC,CAAnB,EAAsB;EACpBN,QAAAA,SAAS,CAACS,MAAV,CAAiBH,SAAjB,EAA4B,CAA5B,CAAA,CAAA;EACD,OAAA;;EAED,MAAA,IAAIN,SAAS,CAACG,MAAV,GAAmB,CAAvB,EAA0B;UACxBH,SAAS,CAACA,SAAS,CAACG,MAAV,GAAmB,CAApB,CAAT,CAAgCQ,OAAhC,EAAA,CAAA;EACD,OAAA;EACF,KAAA;KA5BH,CAAA;EA8BD,CAhCwB,EAAzB,CAAA;;EAkCA,IAAMC,iBAAiB,GAAG,SAApBA,iBAAoB,CAAUC,IAAV,EAAgB;EACxC,EAAA,OACEA,IAAI,CAACC,OAAL,IACAD,IAAI,CAACC,OAAL,CAAaC,WAAb,EAAA,KAA+B,OAD/B,IAEA,OAAOF,IAAI,CAACG,MAAZ,KAAuB,UAHzB,CAAA;EAKD,CAND,CAAA;;EAQA,IAAMC,aAAa,GAAG,SAAhBA,aAAgB,CAAUC,CAAV,EAAa;EACjC,EAAA,OAAOA,CAAC,CAACC,GAAF,KAAU,QAAV,IAAsBD,CAAC,CAACC,GAAF,KAAU,KAAhC,IAAyCD,CAAC,CAACE,OAAF,KAAc,EAA9D,CAAA;EACD,CAFD,CAAA;;EAIA,IAAMC,UAAU,GAAG,SAAbA,UAAa,CAAUH,CAAV,EAAa;IAC9B,OAAOA,CAAC,CAACC,GAAF,KAAU,KAAV,IAAmBD,CAAC,CAACE,OAAF,KAAc,CAAxC,CAAA;EACD,CAFD,CAAA;;EAIA,IAAME,KAAK,GAAG,SAARA,KAAQ,CAAUC,EAAV,EAAc;EAC1B,EAAA,OAAOC,UAAU,CAACD,EAAD,EAAK,CAAL,CAAjB,CAAA;EACD,CAFD;EAKA;;;EACA,IAAME,SAAS,GAAG,SAAZA,SAAY,CAAUC,GAAV,EAAeH,EAAf,EAAmB;IACnC,IAAII,GAAG,GAAG,CAAC,CAAX,CAAA;EAEAD,EAAAA,GAAG,CAACE,KAAJ,CAAU,UAAUC,KAAV,EAAiBC,CAAjB,EAAoB;EAC5B,IAAA,IAAIP,EAAE,CAACM,KAAD,CAAN,EAAe;EACbF,MAAAA,GAAG,GAAGG,CAAN,CAAA;QACA,OAAO,KAAP,CAFa;EAGd,KAAA;;MAED,OAAO,IAAP,CAN4B;KAA9B,CAAA,CAAA;EASA,EAAA,OAAOH,GAAP,CAAA;EACD,CAbD,CAAA;EAeA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACA,IAAMI,cAAc,GAAG,SAAjBA,cAAiB,CAAUF,KAAV,EAA4B;EAAA,EAAA,KAAA,IAAA,IAAA,GAAA,SAAA,CAAA,MAAA,EAARG,MAAQ,GAAA,IAAA,KAAA,CAAA,IAAA,GAAA,CAAA,GAAA,IAAA,GAAA,CAAA,GAAA,CAAA,CAAA,EAAA,IAAA,GAAA,CAAA,EAAA,IAAA,GAAA,IAAA,EAAA,IAAA,EAAA,EAAA;MAARA,MAAQ,CAAA,IAAA,GAAA,CAAA,CAAA,GAAA,SAAA,CAAA,IAAA,CAAA,CAAA;EAAA,GAAA;;IACjD,OAAO,OAAOH,KAAP,KAAiB,UAAjB,GAA8BA,KAAK,CAAA,KAAL,CAASG,KAAAA,CAAAA,EAAAA,MAAT,CAA9B,GAAiDH,KAAxD,CAAA;EACD,CAFD,CAAA;;EAIA,IAAMI,eAAe,GAAG,SAAlBA,eAAkB,CAAUC,KAAV,EAAiB;EACvC;EACA;EACA;EACA;EACA;EACA;EACA;IACA,OAAOA,KAAK,CAACC,MAAN,CAAaC,UAAb,IAA2B,OAAOF,KAAK,CAACG,YAAb,KAA8B,UAAzD,GACHH,KAAK,CAACG,YAAN,EAAA,CAAqB,CAArB,CADG,GAEHH,KAAK,CAACC,MAFV,CAAA;EAGD,CAXD,CAAA;;AAaMG,MAAAA,eAAe,GAAG,SAAlBA,eAAkB,CAAUC,QAAV,EAAoBC,WAApB,EAAiC;EACvD;EACA;EACA,EAAA,IAAMC,GAAG,GAAG,CAAAD,WAAW,KAAX,IAAA,IAAAA,WAAW,KAAA,KAAA,CAAX,GAAAA,KAAAA,CAAAA,GAAAA,WAAW,CAAEE,QAAb,KAAyBA,QAArC,CAAA;;EAEA,EAAA,IAAMC,MAAM,GAAAC,cAAA,CAAA;EACVC,IAAAA,uBAAuB,EAAE,IADf;EAEVC,IAAAA,iBAAiB,EAAE,IAFT;EAGVC,IAAAA,iBAAiB,EAAE,IAAA;EAHT,GAAA,EAIPP,WAJO,CAAZ,CAAA;;EAOA,EAAA,IAAMQ,KAAK,GAAG;EACZ;EACA;EACAC,IAAAA,UAAU,EAAE,EAHA;EAKZ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAC,IAAAA,eAAe,EAAE,EAlBL;EAkBS;EAErB;EACA;EACA;EACA;EACAC,IAAAA,cAAc,EAAE,EAxBJ;EA0BZC,IAAAA,2BAA2B,EAAE,IA1BjB;EA2BZC,IAAAA,uBAAuB,EAAE,IA3Bb;EA4BZC,IAAAA,MAAM,EAAE,KA5BI;EA6BZC,IAAAA,MAAM,EAAE,KA7BI;EA+BZ;EACA;EACAC,IAAAA,sBAAsB,EAAEC,SAAAA;KAjC1B,CAAA;IAoCA,IAAIvD,IAAJ,CAhDuD;;EAkDvD;EACF;EACA;EACA;EACA;EACA;EACA;EACA;;IACE,IAAMwD,SAAS,GAAG,SAAZA,SAAY,CAACC,qBAAD,EAAwBC,UAAxB,EAAoCC,gBAApC,EAAyD;EACzE,IAAA,OAAOF,qBAAqB,IAC1BA,qBAAqB,CAACC,UAAD,CAArB,KAAsCH,SADjC,GAEHE,qBAAqB,CAACC,UAAD,CAFlB,GAGHjB,MAAM,CAACkB,gBAAgB,IAAID,UAArB,CAHV,CAAA;KADF,CAAA;EAOA;EACF;EACA;EACA;EACA;EACA;EACA;;;EACE,EAAA,IAAME,kBAAkB,GAAG,SAArBA,kBAAqB,CAAUC,OAAV,EAAmB;EAC5C;EACA;EACA;EACA,IAAA,OAAOf,KAAK,CAACE,eAAN,CAAsBzB,SAAtB,CACL,UAAA,IAAA,EAAA;QAAA,IAAGuC,SAAH,QAAGA,SAAH;YAAcC,aAAd,QAAcA,aAAd,CAAA;EAAA,MAAA,OACED,SAAS,CAACE,QAAV,CAAmBH,OAAnB,CACA;EACA;EACA;EACA;EACAE,MAAAA,aAAa,CAACE,IAAd,CAAmB,UAACtD,IAAD,EAAA;UAAA,OAAUA,IAAI,KAAKkD,OAAnB,CAAA;EAAA,OAAnB,CANF,CAAA;EAAA,KADK,CAAP,CAAA;KAJF,CAAA;EAeA;EACF;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACE,EAAA,IAAMK,gBAAgB,GAAG,SAAnBA,gBAAmB,CAAUR,UAAV,EAAiC;EACxD,IAAA,IAAIS,WAAW,GAAG1B,MAAM,CAACiB,UAAD,CAAxB,CAAA;;EAEA,IAAA,IAAI,OAAOS,WAAP,KAAuB,UAA3B,EAAuC;EAAA,MAAA,KAAA,IAAA,KAAA,GAAA,SAAA,CAAA,MAAA,EAHSrC,MAGT,GAAA,IAAA,KAAA,CAAA,KAAA,GAAA,CAAA,GAAA,KAAA,GAAA,CAAA,GAAA,CAAA,CAAA,EAAA,KAAA,GAAA,CAAA,EAAA,KAAA,GAAA,KAAA,EAAA,KAAA,EAAA,EAAA;UAHSA,MAGT,CAAA,KAAA,GAAA,CAAA,CAAA,GAAA,SAAA,CAAA,KAAA,CAAA,CAAA;EAAA,OAAA;;EACrCqC,MAAAA,WAAW,GAAGA,WAAW,CAAX,KAAA,CAAA,KAAA,CAAA,EAAerC,MAAf,CAAd,CAAA;EACD,KAAA;;MAED,IAAIqC,WAAW,KAAK,IAApB,EAA0B;QACxBA,WAAW,GAAGZ,SAAd,CADwB;EAEzB,KAAA;;MAED,IAAI,CAACY,WAAL,EAAkB;EAChB,MAAA,IAAIA,WAAW,KAAKZ,SAAhB,IAA6BY,WAAW,KAAK,KAAjD,EAAwD;EACtD,QAAA,OAAOA,WAAP,CAAA;EACD,OAHe;;;EAMhB,MAAA,MAAM,IAAIC,KAAJ,CACCV,GAAAA,CAAAA,MAAAA,CAAAA,UADD,EAAN,8DAAA,CAAA,CAAA,CAAA;EAGD,KAAA;;EAED,IAAA,IAAI/C,IAAI,GAAGwD,WAAX,CAtBwD;;EAwBxD,IAAA,IAAI,OAAOA,WAAP,KAAuB,QAA3B,EAAqC;QACnCxD,IAAI,GAAG4B,GAAG,CAAC8B,aAAJ,CAAkBF,WAAlB,CAAP,CADmC;;QAEnC,IAAI,CAACxD,IAAL,EAAW;EACT,QAAA,MAAM,IAAIyD,KAAJ,CACCV,GAAAA,CAAAA,MAAAA,CAAAA,UADD,EAAN,uCAAA,CAAA,CAAA,CAAA;EAGD,OAAA;EACF,KAAA;;EAED,IAAA,OAAO/C,IAAP,CAAA;KAjCF,CAAA;;EAoCA,EAAA,IAAM2D,mBAAmB,GAAG,SAAtBA,mBAAsB,GAAY;EACtC,IAAA,IAAI3D,IAAI,GAAGuD,gBAAgB,CAAC,cAAD,CAA3B,CADsC;;MAItC,IAAIvD,IAAI,KAAK,KAAb,EAAoB;EAClB,MAAA,OAAO,KAAP,CAAA;EACD,KAAA;;MAED,IAAIA,IAAI,KAAK4C,SAAb,EAAwB;EACtB;QACA,IAAIK,kBAAkB,CAACrB,GAAG,CAACgC,aAAL,CAAlB,IAAyC,CAA7C,EAAgD;UAC9C5D,IAAI,GAAG4B,GAAG,CAACgC,aAAX,CAAA;EACD,OAFD,MAEO;EACL,QAAA,IAAMC,kBAAkB,GAAG1B,KAAK,CAACG,cAAN,CAAqB,CAArB,CAA3B,CAAA;UACA,IAAMwB,iBAAiB,GACrBD,kBAAkB,IAAIA,kBAAkB,CAACC,iBAD3C,CAFK;;EAML9D,QAAAA,IAAI,GAAG8D,iBAAiB,IAAIP,gBAAgB,CAAC,eAAD,CAA5C,CAAA;EACD,OAAA;EACF,KAAA;;MAED,IAAI,CAACvD,IAAL,EAAW;EACT,MAAA,MAAM,IAAIyD,KAAJ,CACJ,8DADI,CAAN,CAAA;EAGD,KAAA;;EAED,IAAA,OAAOzD,IAAP,CAAA;KA5BF,CAAA;;EA+BA,EAAA,IAAM+D,mBAAmB,GAAG,SAAtBA,mBAAsB,GAAY;MACtC5B,KAAK,CAACE,eAAN,GAAwBF,KAAK,CAACC,UAAN,CAAiB4B,GAAjB,CAAqB,UAACb,SAAD,EAAe;QAC1D,IAAMC,aAAa,GAAGa,iBAAQ,CAACd,SAAD,EAAYrB,MAAM,CAACoC,eAAnB,CAA9B,CAD0D;EAI1D;;QACA,IAAMC,cAAc,GAAGC,kBAAS,CAACjB,SAAD,EAAYrB,MAAM,CAACoC,eAAnB,CAAhC,CAAA;QAEA,OAAO;EACLf,QAAAA,SAAS,EAATA,SADK;EAELC,QAAAA,aAAa,EAAbA,aAFK;EAGLe,QAAAA,cAAc,EAAdA,cAHK;EAILL,QAAAA,iBAAiB,EAAEV,aAAa,CAAC9D,MAAd,GAAuB,CAAvB,GAA2B8D,aAAa,CAAC,CAAD,CAAxC,GAA8C,IAJ5D;EAKLiB,QAAAA,gBAAgB,EACdjB,aAAa,CAAC9D,MAAd,GAAuB,CAAvB,GACI8D,aAAa,CAACA,aAAa,CAAC9D,MAAd,GAAuB,CAAxB,CADjB,GAEI,IARD;;EAUL;EACR;EACA;EACA;EACA;EACA;EACA;EACA;UACQgF,gBAlBK,EAAA,SAAA,gBAAA,CAkBYtE,IAlBZ,EAkBkC;YAAA,IAAhBuE,OAAgB,uEAAN,IAAM,CAAA;EACrC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,UAAA,IAAMC,OAAO,GAAGL,cAAc,CAACvD,SAAf,CAAyB,UAAC6D,CAAD,EAAA;cAAA,OAAOA,CAAC,KAAKzE,IAAb,CAAA;EAAA,WAAzB,CAAhB,CAAA;;YACA,IAAIwE,OAAO,GAAG,CAAd,EAAiB;EACf,YAAA,OAAO5B,SAAP,CAAA;EACD,WAAA;;EAED,UAAA,IAAI2B,OAAJ,EAAa;cACX,OAAOJ,cAAc,CAClBO,KADI,CACEF,OAAO,GAAG,CADZ,CAEJlB,CAAAA,IAFI,CAEC,UAACmB,CAAD,EAAA;EAAA,cAAA,OAAOE,mBAAU,CAACF,CAAD,EAAI3C,MAAM,CAACoC,eAAX,CAAjB,CAAA;EAAA,aAFD,CAAP,CAAA;EAGD,WAAA;;EAED,UAAA,OAAOC,cAAc,CAClBO,KADI,CACE,CADF,EACKF,OADL,CAAA,CAEJI,OAFI,EAAA,CAGJtB,IAHI,CAGC,UAACmB,CAAD,EAAA;EAAA,YAAA,OAAOE,mBAAU,CAACF,CAAD,EAAI3C,MAAM,CAACoC,eAAX,CAAjB,CAAA;EAAA,WAHD,CAAP,CAAA;EAID,SAAA;SA5CH,CAAA;EA8CD,KArDuB,CAAxB,CAAA;MAuDA/B,KAAK,CAACG,cAAN,GAAuBH,KAAK,CAACE,eAAN,CAAsBwC,MAAtB,CACrB,UAACC,KAAD,EAAA;EAAA,MAAA,OAAWA,KAAK,CAAC1B,aAAN,CAAoB9D,MAApB,GAA6B,CAAxC,CAAA;OADqB,CAAvB,CAxDsC;;EA6DtC,IAAA,IACE6C,KAAK,CAACG,cAAN,CAAqBhD,MAArB,IAA+B,CAA/B,IACA,CAACiE,gBAAgB,CAAC,eAAD,CAFnB;QAGE;EACA,MAAA,MAAM,IAAIE,KAAJ,CACJ,qGADI,CAAN,CAAA;EAGD,KAAA;KApEH,CAAA;;EAuEA,EAAA,IAAMsB,QAAQ,GAAG,SAAXA,QAAW,CAAU/E,IAAV,EAAgB;MAC/B,IAAIA,IAAI,KAAK,KAAb,EAAoB;EAClB,MAAA,OAAA;EACD,KAAA;;EAED,IAAA,IAAIA,IAAI,KAAK4B,GAAG,CAACgC,aAAjB,EAAgC;EAC9B,MAAA,OAAA;EACD,KAAA;;EAED,IAAA,IAAI,CAAC5D,IAAD,IAAS,CAACA,IAAI,CAACgF,KAAnB,EAA0B;QACxBD,QAAQ,CAACpB,mBAAmB,EAApB,CAAR,CAAA;EACA,MAAA,OAAA;EACD,KAAA;;MAED3D,IAAI,CAACgF,KAAL,CAAW;EAAEC,MAAAA,aAAa,EAAE,CAAC,CAACnD,MAAM,CAACmD,aAAAA;OAArC,CAAA,CAAA;MACA9C,KAAK,CAACK,uBAAN,GAAgCxC,IAAhC,CAAA;;EAEA,IAAA,IAAID,iBAAiB,CAACC,IAAD,CAArB,EAA6B;EAC3BA,MAAAA,IAAI,CAACG,MAAL,EAAA,CAAA;EACD,KAAA;KAnBH,CAAA;;EAsBA,EAAA,IAAM+E,kBAAkB,GAAG,SAArBA,kBAAqB,CAAUC,qBAAV,EAAiC;EAC1D,IAAA,IAAMnF,IAAI,GAAGuD,gBAAgB,CAAC,gBAAD,EAAmB4B,qBAAnB,CAA7B,CAAA;MACA,OAAOnF,IAAI,GAAGA,IAAH,GAAUA,IAAI,KAAK,KAAT,GAAiB,KAAjB,GAAyBmF,qBAA9C,CAAA;EACD,GAHD,CApQuD;EA0QvD;;;EACA,EAAA,IAAMC,gBAAgB,GAAG,SAAnBA,gBAAmB,CAAU/E,CAAV,EAAa;EACpC,IAAA,IAAMiB,MAAM,GAAGF,eAAe,CAACf,CAAD,CAA9B,CAAA;;EAEA,IAAA,IAAI4C,kBAAkB,CAAC3B,MAAD,CAAlB,IAA8B,CAAlC,EAAqC;EACnC;EACA,MAAA,OAAA;EACD,KAAA;;MAED,IAAIJ,cAAc,CAACY,MAAM,CAACuD,uBAAR,EAAiChF,CAAjC,CAAlB,EAAuD;EACrD;QACAhB,IAAI,CAACiG,UAAL,CAAgB;EACd;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAC,QAAAA,WAAW,EACTzD,MAAM,CAACE,uBAAP,IACA,CAACwD,oBAAW,CAAClE,MAAD,EAASQ,MAAM,CAACoC,eAAhB,CAAA;SAdhB,CAAA,CAAA;EAgBA,MAAA,OAAA;EACD,KA3BmC;EA8BpC;EACA;;;MACA,IAAIhD,cAAc,CAACY,MAAM,CAAC2D,iBAAR,EAA2BpF,CAA3B,CAAlB,EAAiD;EAC/C;EACA,MAAA,OAAA;EACD,KAnCmC;;;EAsCpCA,IAAAA,CAAC,CAACqF,cAAF,EAAA,CAAA;EACD,GAvCD,CA3QuD;;;EAqTvD,EAAA,IAAMC,YAAY,GAAG,SAAfA,YAAe,CAAUtF,CAAV,EAAa;EAChC,IAAA,IAAMiB,MAAM,GAAGF,eAAe,CAACf,CAAD,CAA9B,CAAA;MACA,IAAMuF,eAAe,GAAG3C,kBAAkB,CAAC3B,MAAD,CAAlB,IAA8B,CAAtD,CAFgC;;EAKhC,IAAA,IAAIsE,eAAe,IAAItE,MAAM,YAAYuE,QAAzC,EAAmD;EACjD,MAAA,IAAID,eAAJ,EAAqB;UACnBzD,KAAK,CAACK,uBAAN,GAAgClB,MAAhC,CAAA;EACD,OAAA;EACF,KAJD,MAIO;EACL;EACAjB,MAAAA,CAAC,CAACyF,wBAAF,EAAA,CAAA;EACAf,MAAAA,QAAQ,CAAC5C,KAAK,CAACK,uBAAN,IAAiCmB,mBAAmB,EAArD,CAAR,CAAA;EACD,KAAA;EACF,GAdD,CArTuD;EAsUvD;EACA;EACA;;;EACA,EAAA,IAAMoC,QAAQ,GAAG,SAAXA,QAAW,CAAU1F,CAAV,EAAa;EAC5B,IAAA,IAAMiB,MAAM,GAAGF,eAAe,CAACf,CAAD,CAA9B,CAAA;MACA0D,mBAAmB,EAAA,CAAA;MAEnB,IAAIiC,eAAe,GAAG,IAAtB,CAAA;;EAEA,IAAA,IAAI7D,KAAK,CAACG,cAAN,CAAqBhD,MAArB,GAA8B,CAAlC,EAAqC;EACnC;EACA;EACA;EACA,MAAA,IAAM2G,cAAc,GAAGhD,kBAAkB,CAAC3B,MAAD,CAAzC,CAAA;EACA,MAAA,IAAM4E,cAAc,GAClBD,cAAc,IAAI,CAAlB,GAAsB9D,KAAK,CAACE,eAAN,CAAsB4D,cAAtB,CAAtB,GAA8DrD,SADhE,CAAA;;QAGA,IAAIqD,cAAc,GAAG,CAArB,EAAwB;EACtB;EACA;UACA,IAAI5F,CAAC,CAAC8F,QAAN,EAAgB;EACd;EACAH,UAAAA,eAAe,GACb7D,KAAK,CAACG,cAAN,CAAqBH,KAAK,CAACG,cAAN,CAAqBhD,MAArB,GAA8B,CAAnD,EACG+E,gBAFL,CAAA;EAGD,SALD,MAKO;EACL;EACA2B,UAAAA,eAAe,GAAG7D,KAAK,CAACG,cAAN,CAAqB,CAArB,EAAwBwB,iBAA1C,CAAA;EACD,SAAA;EACF,OAZD,MAYO,IAAIzD,CAAC,CAAC8F,QAAN,EAAgB;EACrB;EAEA;EACA,QAAA,IAAIC,iBAAiB,GAAGxF,SAAS,CAC/BuB,KAAK,CAACG,cADyB,EAE/B,UAAA,KAAA,EAAA;YAAA,IAAGwB,iBAAH,SAAGA,iBAAH,CAAA;YAAA,OAA2BxC,MAAM,KAAKwC,iBAAtC,CAAA;EAAA,SAF+B,CAAjC,CAAA;;EAKA,QAAA,IACEsC,iBAAiB,GAAG,CAApB,KACCF,cAAc,CAAC/C,SAAf,KAA6B7B,MAA7B,IACEkE,oBAAW,CAAClE,MAAD,EAASQ,MAAM,CAACoC,eAAhB,CAAX,IACC,CAACS,mBAAU,CAACrD,MAAD,EAASQ,MAAM,CAACoC,eAAhB,CADZ,IAEC,CAACgC,cAAc,CAAC5B,gBAAf,CAAgChD,MAAhC,EAAwC,KAAxC,CAJL,CADF,EAME;EACA;EACA;EACA;EACA;EACA;EACA;EACA8E,UAAAA,iBAAiB,GAAGH,cAApB,CAAA;EACD,SAAA;;UAED,IAAIG,iBAAiB,IAAI,CAAzB,EAA4B;EAC1B;EACA;EACA;EACA,UAAA,IAAMC,qBAAqB,GACzBD,iBAAiB,KAAK,CAAtB,GACIjE,KAAK,CAACG,cAAN,CAAqBhD,MAArB,GAA8B,CADlC,GAEI8G,iBAAiB,GAAG,CAH1B,CAAA;EAKA,UAAA,IAAME,gBAAgB,GAAGnE,KAAK,CAACG,cAAN,CAAqB+D,qBAArB,CAAzB,CAAA;YACAL,eAAe,GAAGM,gBAAgB,CAACjC,gBAAnC,CAAA;EACD,SAAA;EACF,OArCM,MAqCA;EACL;EAEA;EACA,QAAA,IAAIkC,gBAAgB,GAAG3F,SAAS,CAC9BuB,KAAK,CAACG,cADwB,EAE9B,UAAA,KAAA,EAAA;YAAA,IAAG+B,gBAAH,SAAGA,gBAAH,CAAA;YAAA,OAA0B/C,MAAM,KAAK+C,gBAArC,CAAA;EAAA,SAF8B,CAAhC,CAAA;;EAKA,QAAA,IACEkC,gBAAgB,GAAG,CAAnB,KACCL,cAAc,CAAC/C,SAAf,KAA6B7B,MAA7B,IACEkE,oBAAW,CAAClE,MAAD,EAASQ,MAAM,CAACoC,eAAhB,CAAX,IACC,CAACS,mBAAU,CAACrD,MAAD,EAASQ,MAAM,CAACoC,eAAhB,CADZ,IAEC,CAACgC,cAAc,CAAC5B,gBAAf,CAAgChD,MAAhC,CAJL,CADF,EAME;EACA;EACA;EACA;EACA;EACA;EACA;EACAiF,UAAAA,gBAAgB,GAAGN,cAAnB,CAAA;EACD,SAAA;;UAED,IAAIM,gBAAgB,IAAI,CAAxB,EAA2B;EACzB;EACA;EACA;EACA,UAAA,IAAMF,sBAAqB,GACzBE,gBAAgB,KAAKpE,KAAK,CAACG,cAAN,CAAqBhD,MAArB,GAA8B,CAAnD,GACI,CADJ,GAEIiH,gBAAgB,GAAG,CAHzB,CAAA;;EAKA,UAAA,IAAMD,iBAAgB,GAAGnE,KAAK,CAACG,cAAN,CAAqB+D,sBAArB,CAAzB,CAAA;YACAL,eAAe,GAAGM,iBAAgB,CAACxC,iBAAnC,CAAA;EACD,SAAA;EACF,OAAA;EACF,KA/FD,MA+FO;EACL;EACAkC,MAAAA,eAAe,GAAGzC,gBAAgB,CAAC,eAAD,CAAlC,CAAA;EACD,KAAA;;EAED,IAAA,IAAIyC,eAAJ,EAAqB;EACnB3F,MAAAA,CAAC,CAACqF,cAAF,EAAA,CAAA;QACAX,QAAQ,CAACiB,eAAD,CAAR,CAAA;EACD,KA7G2B;;KAA9B,CAAA;;EAiHA,EAAA,IAAMQ,QAAQ,GAAG,SAAXA,QAAW,CAAUnG,CAAV,EAAa;EAC5B,IAAA,IACED,aAAa,CAACC,CAAD,CAAb,IACAa,cAAc,CAACY,MAAM,CAACG,iBAAR,EAA2B5B,CAA3B,CAAd,KAAgD,KAFlD,EAGE;EACAA,MAAAA,CAAC,CAACqF,cAAF,EAAA,CAAA;EACArG,MAAAA,IAAI,CAACiG,UAAL,EAAA,CAAA;EACA,MAAA,OAAA;EACD,KAAA;;EAED,IAAA,IAAI9E,UAAU,CAACH,CAAD,CAAd,EAAmB;QACjB0F,QAAQ,CAAC1F,CAAD,CAAR,CAAA;EACA,MAAA,OAAA;EACD,KAAA;KAbH,CAAA;;EAgBA,EAAA,IAAMoG,UAAU,GAAG,SAAbA,UAAa,CAAUpG,CAAV,EAAa;EAC9B,IAAA,IAAMiB,MAAM,GAAGF,eAAe,CAACf,CAAD,CAA9B,CAAA;;EAEA,IAAA,IAAI4C,kBAAkB,CAAC3B,MAAD,CAAlB,IAA8B,CAAlC,EAAqC;EACnC,MAAA,OAAA;EACD,KAAA;;MAED,IAAIJ,cAAc,CAACY,MAAM,CAACuD,uBAAR,EAAiChF,CAAjC,CAAlB,EAAuD;EACrD,MAAA,OAAA;EACD,KAAA;;MAED,IAAIa,cAAc,CAACY,MAAM,CAAC2D,iBAAR,EAA2BpF,CAA3B,CAAlB,EAAiD;EAC/C,MAAA,OAAA;EACD,KAAA;;EAEDA,IAAAA,CAAC,CAACqF,cAAF,EAAA,CAAA;EACArF,IAAAA,CAAC,CAACyF,wBAAF,EAAA,CAAA;EACD,GAjBD,CA1cuD;EA8dvD;EACA;;;EAEA,EAAA,IAAMY,YAAY,GAAG,SAAfA,YAAe,GAAY;EAC/B,IAAA,IAAI,CAACvE,KAAK,CAACM,MAAX,EAAmB;EACjB,MAAA,OAAA;EACD,KAH8B;;;EAM/BvD,IAAAA,gBAAgB,CAACE,YAAjB,CAA8BC,IAA9B,EAN+B;EAS/B;;MACA8C,KAAK,CAACQ,sBAAN,GAA+Bb,MAAM,CAACI,iBAAP,GAC3BzB,KAAK,CAAC,YAAY;QAChBsE,QAAQ,CAACpB,mBAAmB,EAApB,CAAR,CAAA;EACD,KAFI,CADsB,GAI3BoB,QAAQ,CAACpB,mBAAmB,EAApB,CAJZ,CAAA;EAMA/B,IAAAA,GAAG,CAAC+E,gBAAJ,CAAqB,SAArB,EAAgChB,YAAhC,EAA8C,IAA9C,CAAA,CAAA;EACA/D,IAAAA,GAAG,CAAC+E,gBAAJ,CAAqB,WAArB,EAAkCvB,gBAAlC,EAAoD;EAClDwB,MAAAA,OAAO,EAAE,IADyC;EAElDC,MAAAA,OAAO,EAAE,KAAA;OAFX,CAAA,CAAA;EAIAjF,IAAAA,GAAG,CAAC+E,gBAAJ,CAAqB,YAArB,EAAmCvB,gBAAnC,EAAqD;EACnDwB,MAAAA,OAAO,EAAE,IAD0C;EAEnDC,MAAAA,OAAO,EAAE,KAAA;OAFX,CAAA,CAAA;EAIAjF,IAAAA,GAAG,CAAC+E,gBAAJ,CAAqB,OAArB,EAA8BF,UAA9B,EAA0C;EACxCG,MAAAA,OAAO,EAAE,IAD+B;EAExCC,MAAAA,OAAO,EAAE,KAAA;OAFX,CAAA,CAAA;EAIAjF,IAAAA,GAAG,CAAC+E,gBAAJ,CAAqB,SAArB,EAAgCH,QAAhC,EAA0C;EACxCI,MAAAA,OAAO,EAAE,IAD+B;EAExCC,MAAAA,OAAO,EAAE,KAAA;OAFX,CAAA,CAAA;EAKA,IAAA,OAAOxH,IAAP,CAAA;KAlCF,CAAA;;EAqCA,EAAA,IAAMyH,eAAe,GAAG,SAAlBA,eAAkB,GAAY;EAClC,IAAA,IAAI,CAAC3E,KAAK,CAACM,MAAX,EAAmB;EACjB,MAAA,OAAA;EACD,KAAA;;EAEDb,IAAAA,GAAG,CAACmF,mBAAJ,CAAwB,SAAxB,EAAmCpB,YAAnC,EAAiD,IAAjD,CAAA,CAAA;EACA/D,IAAAA,GAAG,CAACmF,mBAAJ,CAAwB,WAAxB,EAAqC3B,gBAArC,EAAuD,IAAvD,CAAA,CAAA;EACAxD,IAAAA,GAAG,CAACmF,mBAAJ,CAAwB,YAAxB,EAAsC3B,gBAAtC,EAAwD,IAAxD,CAAA,CAAA;EACAxD,IAAAA,GAAG,CAACmF,mBAAJ,CAAwB,OAAxB,EAAiCN,UAAjC,EAA6C,IAA7C,CAAA,CAAA;EACA7E,IAAAA,GAAG,CAACmF,mBAAJ,CAAwB,SAAxB,EAAmCP,QAAnC,EAA6C,IAA7C,CAAA,CAAA;EAEA,IAAA,OAAOnH,IAAP,CAAA;EACD,GAZD,CAtgBuD;EAqhBvD;EACA;;;EAEAA,EAAAA,IAAI,GAAG;EACL,IAAA,IAAIoD,MAAJ,GAAa;QACX,OAAON,KAAK,CAACM,MAAb,CAAA;OAFG;;EAKL,IAAA,IAAIC,MAAJ,GAAa;QACX,OAAOP,KAAK,CAACO,MAAb,CAAA;OANG;;MASLsE,QATK,EAAA,SAAA,QAAA,CASIC,eATJ,EASqB;QACxB,IAAI9E,KAAK,CAACM,MAAV,EAAkB;EAChB,QAAA,OAAO,IAAP,CAAA;EACD,OAAA;;EAED,MAAA,IAAMyE,UAAU,GAAGrE,SAAS,CAACoE,eAAD,EAAkB,YAAlB,CAA5B,CAAA;EACA,MAAA,IAAME,cAAc,GAAGtE,SAAS,CAACoE,eAAD,EAAkB,gBAAlB,CAAhC,CAAA;EACA,MAAA,IAAMG,iBAAiB,GAAGvE,SAAS,CAACoE,eAAD,EAAkB,mBAAlB,CAAnC,CAAA;;QAEA,IAAI,CAACG,iBAAL,EAAwB;UACtBrD,mBAAmB,EAAA,CAAA;EACpB,OAAA;;QAED5B,KAAK,CAACM,MAAN,GAAe,IAAf,CAAA;QACAN,KAAK,CAACO,MAAN,GAAe,KAAf,CAAA;EACAP,MAAAA,KAAK,CAACI,2BAAN,GAAoCX,GAAG,CAACgC,aAAxC,CAAA;;EAEA,MAAA,IAAIsD,UAAJ,EAAgB;UACdA,UAAU,EAAA,CAAA;EACX,OAAA;;EAED,MAAA,IAAMG,gBAAgB,GAAG,SAAnBA,gBAAmB,GAAM;EAC7B,QAAA,IAAID,iBAAJ,EAAuB;YACrBrD,mBAAmB,EAAA,CAAA;EACpB,SAAA;;UACD2C,YAAY,EAAA,CAAA;;EACZ,QAAA,IAAIS,cAAJ,EAAoB;YAClBA,cAAc,EAAA,CAAA;EACf,SAAA;SAPH,CAAA;;EAUA,MAAA,IAAIC,iBAAJ,EAAuB;EACrBA,QAAAA,iBAAiB,CAACjF,KAAK,CAACC,UAAN,CAAiBkF,MAAjB,EAAD,CAAjB,CAA6CC,IAA7C,CACEF,gBADF,EAEEA,gBAFF,CAAA,CAAA;EAIA,QAAA,OAAO,IAAP,CAAA;EACD,OAAA;;QAEDA,gBAAgB,EAAA,CAAA;EAChB,MAAA,OAAO,IAAP,CAAA;OAjDG;MAoDL/B,UApDK,EAAA,SAAA,UAAA,CAoDMkC,iBApDN,EAoDyB;EAC5B,MAAA,IAAI,CAACrF,KAAK,CAACM,MAAX,EAAmB;EACjB,QAAA,OAAO,IAAP,CAAA;EACD,OAAA;;EAED,MAAA,IAAMgF,OAAO,GAAA1F,cAAA,CAAA;UACX2F,YAAY,EAAE5F,MAAM,CAAC4F,YADV;UAEXC,gBAAgB,EAAE7F,MAAM,CAAC6F,gBAFd;UAGXC,mBAAmB,EAAE9F,MAAM,CAAC8F,mBAAAA;EAHjB,OAAA,EAIRJ,iBAJQ,CAAb,CAAA;;EAOAK,MAAAA,YAAY,CAAC1F,KAAK,CAACQ,sBAAP,CAAZ,CAZ4B;;QAa5BR,KAAK,CAACQ,sBAAN,GAA+BC,SAA/B,CAAA;QAEAkE,eAAe,EAAA,CAAA;QACf3E,KAAK,CAACM,MAAN,GAAe,KAAf,CAAA;QACAN,KAAK,CAACO,MAAN,GAAe,KAAf,CAAA;QAEAxD,gBAAgB,CAACW,cAAjB,CAAgCR,IAAhC,CAAA,CAAA;EAEA,MAAA,IAAMqI,YAAY,GAAG7E,SAAS,CAAC4E,OAAD,EAAU,cAAV,CAA9B,CAAA;EACA,MAAA,IAAME,gBAAgB,GAAG9E,SAAS,CAAC4E,OAAD,EAAU,kBAAV,CAAlC,CAAA;EACA,MAAA,IAAMG,mBAAmB,GAAG/E,SAAS,CAAC4E,OAAD,EAAU,qBAAV,CAArC,CAAA;QACA,IAAMlC,WAAW,GAAG1C,SAAS,CAC3B4E,OAD2B,EAE3B,aAF2B,EAG3B,yBAH2B,CAA7B,CAAA;;EAMA,MAAA,IAAIC,YAAJ,EAAkB;UAChBA,YAAY,EAAA,CAAA;EACb,OAAA;;EAED,MAAA,IAAMI,kBAAkB,GAAG,SAArBA,kBAAqB,GAAM;EAC/BrH,QAAAA,KAAK,CAAC,YAAM;EACV,UAAA,IAAI8E,WAAJ,EAAiB;EACfR,YAAAA,QAAQ,CAACG,kBAAkB,CAAC/C,KAAK,CAACI,2BAAP,CAAnB,CAAR,CAAA;EACD,WAAA;;EACD,UAAA,IAAIoF,gBAAJ,EAAsB;cACpBA,gBAAgB,EAAA,CAAA;EACjB,WAAA;EACF,SAPI,CAAL,CAAA;SADF,CAAA;;QAWA,IAAIpC,WAAW,IAAIqC,mBAAnB,EAAwC;EACtCA,QAAAA,mBAAmB,CACjB1C,kBAAkB,CAAC/C,KAAK,CAACI,2BAAP,CADD,CAAnB,CAEEgF,IAFF,CAEOO,kBAFP,EAE2BA,kBAF3B,CAAA,CAAA;EAGA,QAAA,OAAO,IAAP,CAAA;EACD,OAAA;;QAEDA,kBAAkB,EAAA,CAAA;EAClB,MAAA,OAAO,IAAP,CAAA;OAzGG;EA4GLtI,IAAAA,KA5GK,EA4GG,SAAA,KAAA,GAAA;QACN,IAAI2C,KAAK,CAACO,MAAN,IAAgB,CAACP,KAAK,CAACM,MAA3B,EAAmC;EACjC,QAAA,OAAO,IAAP,CAAA;EACD,OAAA;;QAEDN,KAAK,CAACO,MAAN,GAAe,IAAf,CAAA;QACAoE,eAAe,EAAA,CAAA;EAEf,MAAA,OAAO,IAAP,CAAA;OApHG;EAuHLhH,IAAAA,OAvHK,EAuHK,SAAA,OAAA,GAAA;QACR,IAAI,CAACqC,KAAK,CAACO,MAAP,IAAiB,CAACP,KAAK,CAACM,MAA5B,EAAoC;EAClC,QAAA,OAAO,IAAP,CAAA;EACD,OAAA;;QAEDN,KAAK,CAACO,MAAN,GAAe,KAAf,CAAA;QACAqB,mBAAmB,EAAA,CAAA;QACnB2C,YAAY,EAAA,CAAA;EAEZ,MAAA,OAAO,IAAP,CAAA;OAhIG;MAmILqB,uBAnIK,EAAA,SAAA,uBAAA,CAmImBC,iBAnInB,EAmIsC;QACzC,IAAMC,eAAe,GAAG,EAAA,CAAGX,MAAH,CAAUU,iBAAV,CAA6BnD,CAAAA,MAA7B,CAAoCqD,OAApC,CAAxB,CAAA;QAEA/F,KAAK,CAACC,UAAN,GAAmB6F,eAAe,CAACjE,GAAhB,CAAoB,UAACd,OAAD,EAAA;EAAA,QAAA,OACrC,OAAOA,OAAP,KAAmB,QAAnB,GAA8BtB,GAAG,CAAC8B,aAAJ,CAAkBR,OAAlB,CAA9B,GAA2DA,OADtB,CAAA;EAAA,OAApB,CAAnB,CAAA;;QAIA,IAAIf,KAAK,CAACM,MAAV,EAAkB;UAChBsB,mBAAmB,EAAA,CAAA;EACpB,OAAA;;EAED,MAAA,OAAO,IAAP,CAAA;EACD,KAAA;EA/II,GAAP,CAxhBuD;;IA2qBvD1E,IAAI,CAAC0I,uBAAL,CAA6BrG,QAA7B,CAAA,CAAA;EAEA,EAAA,OAAOrC,IAAP,CAAA;EACD;;;;;;;;;;"}