// External Context Engine Interface
// This defines the contract for integrating external context engines

export interface SemanticNode {
  id: string;
  type: 'file' | 'function' | 'class' | 'variable' | 'import' | 'export';
  name: string;
  path: string;
  startLine?: number;
  endLine?: number;
  content?: string;
  metadata: Record<string, any>;
}

export interface SemanticRelationship {
  from: string; // Node ID
  to: string;   // Node ID
  type: 'calls' | 'imports' | 'extends' | 'implements' | 'references' | 'contains';
  weight: number; // Relevance score 0-1
  metadata?: Record<string, any>;
}

export interface SemanticGraph {
  nodes: Map<string, SemanticNode>;
  relationships: SemanticRelationship[];
  metadata: {
    generatedAt: Date;
    version: string;
    rootPath: string;
    totalNodes: number;
    totalRelationships: number;
  };
}

export interface RelevanceScore {
  fileId: string;
  filePath: string;
  score: number; // 0-1
  reasons: string[];
  semanticMatches: SemanticMatch[];
}

export interface SemanticMatch {
  nodeId: string;
  matchType: 'exact' | 'semantic' | 'structural' | 'contextual';
  confidence: number;
  snippet?: string;
}

export interface ContextQuery {
  type: 'general' | 'file-specific' | 'task-specific' | 'semantic-search';
  query: string;
  filePath?: string;
  taskContext?: string;
  maxResults?: number;
  includeRelated?: boolean;
  semanticDepth?: number; // How deep to traverse relationships
}

export interface EnhancedContext {
  primaryContent: string;
  semanticContext: SemanticContextData;
  relevanceScores: RelevanceScore[];
  metadata: {
    generatedAt: Date;
    queryType: string;
    processingTime: number;
    contextLength: number;
    semanticNodesIncluded: number;
  };
}

export interface SemanticContextData {
  relatedFunctions: SemanticNode[];
  relatedClasses: SemanticNode[];
  dependencies: SemanticNode[];
  dependents: SemanticNode[];
  architecturalPatterns: string[];
  codePatterns: string[];
}

export interface ContextEngineConfig {
  provider: 'augment' | 'intelligent' | 'tree-sitter' | 'codebert' | 'basic' | 'custom';
  enabled: boolean;
  fallbackToBasic: boolean;
  cacheEnabled: boolean;
  cacheTTL: number; // seconds
  maxSemanticDepth: number;
  maxContextNodes: number;
  apiConfig?: {
    baseUrl?: string;
    apiKey?: string;
    timeout?: number;
  };
  localConfig?: {
    modelPath?: string;
    embeddingDimensions?: number;
  };
}

export interface IContextEngine {
  readonly name: string;
  readonly version: string;
  
  // Initialization
  initialize(config: ContextEngineConfig): Promise<void>;
  isAvailable(): Promise<boolean>;
  
  // Core semantic analysis
  analyzeCodebase(rootPath: string): Promise<SemanticGraph>;
  updateSemanticGraph(changedFiles: string[]): Promise<void>;
  
  // Context building
  buildEnhancedContext(query: ContextQuery): Promise<EnhancedContext>;
  scoreRelevance(query: string, files: string[]): Promise<RelevanceScore[]>;
  
  // Semantic search
  findSimilarCode(codeSnippet: string, language?: string): Promise<SemanticMatch[]>;
  findRelatedNodes(nodeId: string, relationshipTypes?: string[]): Promise<SemanticNode[]>;
  
  // Pattern recognition
  detectArchitecturalPatterns(graph: SemanticGraph): Promise<string[]>;
  detectCodePatterns(nodes: SemanticNode[]): Promise<string[]>;
  
  // Caching and optimization
  clearCache(): Promise<void>;
  getStats(): Promise<ContextEngineStats>;
}

export interface ContextEngineStats {
  totalNodes: number;
  totalRelationships: number;
  cacheHitRate: number;
  averageQueryTime: number;
  lastUpdated: Date;
  memoryUsage: number;
}

// Factory for creating context engines
export interface IContextEngineFactory {
  createEngine(provider: string, config: ContextEngineConfig): Promise<IContextEngine>;
  getSupportedProviders(): string[];
}
