// Basic Semantic Engine - Fallback implementation using simple heuristics

import * as fs from 'fs/promises';
import * as path from 'path';
import { Logger } from '../../utils/Logger';
import { 
  IContextEngine, 
  ContextEngineConfig, 
  SemanticGraph, 
  ContextQuery, 
  EnhancedContext,
  RelevanceScore,
  SemanticMatch,
  ContextEngineStats,
  SemanticNode,
  SemanticRelationship
} from '../IContextEngine';

export class BasicSemanticEngine implements IContextEngine {
  public readonly name = 'Basic Semantic Engine';
  public readonly version = '1.0.0';
  
  private logger: Logger;
  private config?: ContextEngineConfig;
  private semanticGraph?: SemanticGraph;
  private stats: ContextEngineStats;

  constructor(logger: Logger) {
    this.logger = logger;
    this.stats = {
      totalNodes: 0,
      totalRelationships: 0,
      cacheHitRate: 0,
      averageQueryTime: 0,
      lastUpdated: new Date(),
      memoryUsage: 0
    };
  }

  public async initialize(config: ContextEngineConfig): Promise<void> {
    this.config = config;
    this.logger.info('🔧 Basic Semantic Engine initialized');
  }

  public async isAvailable(): Promise<boolean> {
    return true; // Always available as fallback
  }

  public async analyzeCodebase(rootPath: string): Promise<SemanticGraph> {
    this.logger.info('🔍 Analyzing codebase with basic semantic analysis...');
    
    const nodes = new Map<string, SemanticNode>();
    const relationships: SemanticRelationship[] = [];
    
    try {
      await this.scanDirectory(rootPath, rootPath, nodes, relationships);
      
      this.semanticGraph = {
        nodes,
        relationships,
        metadata: {
          generatedAt: new Date(),
          version: this.version,
          rootPath,
          totalNodes: nodes.size,
          totalRelationships: relationships.length
        }
      };

      this.stats.totalNodes = nodes.size;
      this.stats.totalRelationships = relationships.length;
      this.stats.lastUpdated = new Date();

      return this.semanticGraph;
    } catch (error) {
      this.logger.error(`Basic semantic analysis failed: ${error}`);
      throw error;
    }
  }

  private async scanDirectory(
    dirPath: string, 
    rootPath: string, 
    nodes: Map<string, SemanticNode>, 
    relationships: SemanticRelationship[]
  ): Promise<void> {
    try {
      const entries = await fs.readdir(dirPath, { withFileTypes: true });
      
      for (const entry of entries) {
        const fullPath = path.join(dirPath, entry.name);
        const relativePath = path.relative(rootPath, fullPath);
        
        if (entry.isDirectory()) {
          // Skip common ignore directories
          if (['node_modules', '.git', 'dist', 'build', '.next'].includes(entry.name)) {
            continue;
          }
          await this.scanDirectory(fullPath, rootPath, nodes, relationships);
        } else if (entry.isFile()) {
          await this.analyzeFile(fullPath, relativePath, nodes, relationships);
        }
      }
    } catch (error) {
      this.logger.debug(`Error scanning directory ${dirPath}: ${error}`);
    }
  }

  private async analyzeFile(
    filePath: string, 
    relativePath: string, 
    nodes: Map<string, SemanticNode>, 
    relationships: SemanticRelationship[]
  ): Promise<void> {
    const ext = path.extname(filePath).toLowerCase();
    
    // Only analyze code files
    if (!['.ts', '.js', '.py', '.java', '.cpp', '.c', '.cs', '.php', '.rb', '.go', '.rs'].includes(ext)) {
      return;
    }

    try {
      const content = await fs.readFile(filePath, 'utf-8');
      const fileNode: SemanticNode = {
        id: `file_${relativePath}`,
        type: 'file',
        name: path.basename(filePath),
        path: relativePath,
        content: content.substring(0, 1000), // Truncate for memory
        metadata: {
          extension: ext,
          size: content.length,
          lines: content.split('\n').length
        }
      };

      nodes.set(fileNode.id, fileNode);

      // Basic pattern detection
      await this.extractBasicPatterns(content, relativePath, nodes, relationships);
    } catch (error) {
      this.logger.debug(`Error analyzing file ${filePath}: ${error}`);
    }
  }

  private async extractBasicPatterns(
    content: string, 
    filePath: string, 
    nodes: Map<string, SemanticNode>, 
    relationships: SemanticRelationship[]
  ): Promise<void> {
    const lines = content.split('\n');
    
    // Extract functions
    const functionRegex = /(?:function|def|func|fn)\s+(\w+)/g;
    let match;
    while ((match = functionRegex.exec(content)) !== null) {
      const funcName = match[1];
      const lineNumber = content.substring(0, match.index).split('\n').length;
      
      const funcNode: SemanticNode = {
        id: `func_${filePath}_${funcName}`,
        type: 'function',
        name: funcName,
        path: filePath,
        startLine: lineNumber,
        metadata: {
          parentFile: filePath
        }
      };
      
      nodes.set(funcNode.id, funcNode);
      
      // Add relationship to file
      relationships.push({
        from: `file_${filePath}`,
        to: funcNode.id,
        type: 'contains',
        weight: 1.0
      });
    }

    // Extract classes
    const classRegex = /(?:class|interface)\s+(\w+)/g;
    while ((match = classRegex.exec(content)) !== null) {
      const className = match[1];
      const lineNumber = content.substring(0, match.index).split('\n').length;
      
      const classNode: SemanticNode = {
        id: `class_${filePath}_${className}`,
        type: 'class',
        name: className,
        path: filePath,
        startLine: lineNumber,
        metadata: {
          parentFile: filePath
        }
      };
      
      nodes.set(classNode.id, classNode);
      
      // Add relationship to file
      relationships.push({
        from: `file_${filePath}`,
        to: classNode.id,
        type: 'contains',
        weight: 1.0
      });
    }

    // Extract imports
    const importRegex = /(?:import|require|from)\s+['"](.*?)['"]|import\s+.*?\s+from\s+['"](.*?)['"]/g;
    while ((match = importRegex.exec(content)) !== null) {
      const importPath = match[1] || match[2];
      if (importPath && !importPath.startsWith('.')) {
        // External dependency
        const importNode: SemanticNode = {
          id: `import_${importPath}`,
          type: 'import',
          name: importPath,
          path: 'external',
          metadata: {
            external: true
          }
        };
        
        if (!nodes.has(importNode.id)) {
          nodes.set(importNode.id, importNode);
        }
        
        relationships.push({
          from: `file_${filePath}`,
          to: importNode.id,
          type: 'imports',
          weight: 0.8
        });
      }
    }
  }

  public async updateSemanticGraph(changedFiles: string[]): Promise<void> {
    // For basic engine, just re-analyze the changed files
    if (!this.semanticGraph) {
      return;
    }

    for (const file of changedFiles) {
      // Remove old nodes for this file
      const toRemove: string[] = [];
      for (const [id, node] of this.semanticGraph.nodes) {
        if (node.path === file) {
          toRemove.push(id);
        }
      }
      
      toRemove.forEach(id => this.semanticGraph!.nodes.delete(id));
      
      // Re-analyze the file
      await this.analyzeFile(file, file, this.semanticGraph.nodes, this.semanticGraph.relationships);
    }
    
    this.stats.lastUpdated = new Date();
  }

  public async buildEnhancedContext(query: ContextQuery): Promise<EnhancedContext> {
    const startTime = Date.now();
    
    if (!this.semanticGraph) {
      throw new Error('Semantic graph not available');
    }

    // Basic relevance scoring
    const relevanceScores = await this.scoreRelevance(query.query, 
      Array.from(this.semanticGraph.nodes.values())
        .filter(n => n.type === 'file')
        .map(n => n.path)
    );

    // Get related nodes
    const relatedFunctions = Array.from(this.semanticGraph.nodes.values())
      .filter(n => n.type === 'function')
      .slice(0, 10);
    
    const relatedClasses = Array.from(this.semanticGraph.nodes.values())
      .filter(n => n.type === 'class')
      .slice(0, 10);

    const dependencies = Array.from(this.semanticGraph.nodes.values())
      .filter(n => n.type === 'import')
      .slice(0, 10);

    const primaryContent = `Basic semantic analysis of codebase:
- Total files: ${Array.from(this.semanticGraph.nodes.values()).filter(n => n.type === 'file').length}
- Total functions: ${relatedFunctions.length}
- Total classes: ${relatedClasses.length}
- External dependencies: ${dependencies.length}`;

    return {
      primaryContent,
      semanticContext: {
        relatedFunctions,
        relatedClasses,
        dependencies,
        dependents: [],
        architecturalPatterns: ['Basic MVC', 'Module Pattern'],
        codePatterns: ['Function Declaration', 'Class Definition', 'Import/Export']
      },
      relevanceScores,
      metadata: {
        generatedAt: new Date(),
        queryType: query.type,
        processingTime: Date.now() - startTime,
        contextLength: primaryContent.length,
        semanticNodesIncluded: relatedFunctions.length + relatedClasses.length
      }
    };
  }

  public async scoreRelevance(query: string, files: string[]): Promise<RelevanceScore[]> {
    const scores: RelevanceScore[] = [];
    const queryLower = query.toLowerCase();
    
    for (const file of files) {
      let score = 0;
      const reasons: string[] = [];
      
      // Simple keyword matching
      if (file.toLowerCase().includes(queryLower)) {
        score += 0.8;
        reasons.push('Filename matches query');
      }
      
      // File extension relevance
      const ext = path.extname(file);
      if (['.ts', '.js'].includes(ext) && queryLower.includes('typescript')) {
        score += 0.6;
        reasons.push('TypeScript/JavaScript file');
      }
      
      // Path-based scoring
      if (file.includes('src/') || file.includes('lib/')) {
        score += 0.3;
        reasons.push('Source code file');
      }
      
      if (score > 0) {
        scores.push({
          fileId: `file_${file}`,
          filePath: file,
          score: Math.min(score, 1.0),
          reasons,
          semanticMatches: []
        });
      }
    }
    
    return scores.sort((a, b) => b.score - a.score);
  }

  public async findSimilarCode(codeSnippet: string, language?: string): Promise<SemanticMatch[]> {
    // Basic implementation - just return empty for now
    return [];
  }

  public async findRelatedNodes(nodeId: string, relationshipTypes?: string[]): Promise<SemanticNode[]> {
    if (!this.semanticGraph) {
      return [];
    }

    const related: SemanticNode[] = [];
    const targetTypes = relationshipTypes || ['calls', 'imports', 'contains'];
    
    for (const rel of this.semanticGraph.relationships) {
      if (rel.from === nodeId && targetTypes.includes(rel.type)) {
        const node = this.semanticGraph.nodes.get(rel.to);
        if (node) related.push(node);
      }
    }
    
    return related;
  }

  public async detectArchitecturalPatterns(graph: SemanticGraph): Promise<string[]> {
    const patterns: string[] = [];
    
    // Basic pattern detection
    const hasClasses = Array.from(graph.nodes.values()).some(n => n.type === 'class');
    const hasFunctions = Array.from(graph.nodes.values()).some(n => n.type === 'function');
    
    if (hasClasses) patterns.push('Object-Oriented');
    if (hasFunctions) patterns.push('Functional');
    
    return patterns;
  }

  public async detectCodePatterns(nodes: SemanticNode[]): Promise<string[]> {
    const patterns: string[] = [];
    
    const hasClasses = nodes.some(n => n.type === 'class');
    const hasFunctions = nodes.some(n => n.type === 'function');
    const hasImports = nodes.some(n => n.type === 'import');
    
    if (hasClasses) patterns.push('Class Definitions');
    if (hasFunctions) patterns.push('Function Declarations');
    if (hasImports) patterns.push('Module Imports');
    
    return patterns;
  }

  public async clearCache(): Promise<void> {
    // No cache to clear in basic implementation
  }

  public async getStats(): Promise<ContextEngineStats> {
    return { ...this.stats };
  }
}
