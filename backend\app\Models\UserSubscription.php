<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserSubscription extends Model
{
    protected $fillable = [
        'user_id',
        'subscription_plan_id',
        'stripe_subscription_id',
        'stripe_customer_id',
        'status',
        'billing_cycle',
        'trial_ends_at',
        'current_period_start',
        'current_period_end',
        'canceled_at',
        'ends_at',
        'api_requests_used_today',
        'api_requests_used_month',
        'usage_reset_date',
        'metadata',
    ];

    protected function casts(): array
    {
        return [
            'trial_ends_at' => 'datetime',
            'current_period_start' => 'datetime',
            'current_period_end' => 'datetime',
            'canceled_at' => 'datetime',
            'ends_at' => 'datetime',
            'usage_reset_date' => 'date',
            'metadata' => 'array',
        ];
    }

    /**
     * Get the user that owns the subscription.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the subscription plan.
     */
    public function subscriptionPlan(): BelongsTo
    {
        return $this->belongsTo(SubscriptionPlan::class);
    }

    /**
     * Check if subscription is active.
     */
    public function isActive(): bool
    {
        return $this->status === 'active';
    }

    /**
     * Check if subscription is on trial.
     */
    public function onTrial(): bool
    {
        return $this->status === 'trialing' &&
               $this->trial_ends_at &&
               $this->trial_ends_at->isFuture();
    }

    /**
     * Check if subscription is canceled.
     */
    public function isCanceled(): bool
    {
        return $this->status === 'canceled';
    }

    /**
     * Get remaining API requests for today.
     */
    public function getRemainingDailyRequests(): int
    {
        $limit = $this->subscriptionPlan->api_requests_daily;
        if ($limit === 0) return PHP_INT_MAX; // Unlimited

        return max(0, $limit - $this->api_requests_used_today);
    }

    /**
     * Get remaining API requests for this month.
     */
    public function getRemainingMonthlyRequests(): int
    {
        $limit = $this->subscriptionPlan->api_requests_monthly;
        if ($limit === 0) return PHP_INT_MAX; // Unlimited

        return max(0, $limit - $this->api_requests_used_month);
    }
}
