import path from 'path';
import { Codebase<PERSON>nalyzer, FileInfo } from '../codebase/CodebaseAnalyzer';
import { Logger } from '../utils/Logger';

export class ContextBuilder {
  private codebaseAnalyzer: CodebaseAnalyzer;
  protected logger: Logger;
  private maxContextLength: number = 50000; // Adjust based on model context window

  constructor(codebaseAnalyzer: CodebaseAnalyzer, logger: Logger) {
    this.codebaseAnalyzer = codebaseAnalyzer;
    this.logger = logger;
  }

  public async buildContext(): Promise<string> {
    try {
      const codebaseContext = await this.codebaseAnalyzer.scanCodebase();
      
      let context = `You are GOC Agent, an intelligent coding assistant with access to the following codebase:

## Project Overview
- Total files: ${codebaseContext.totalFiles}
- Languages: ${codebaseContext.languages.join(', ')}
- Total size: ${this.formatBytes(codebaseContext.totalSize)}

## Project Structure
\`\`\`
${codebaseContext.structure}
\`\`\`

## Key Files
`;

      // Add important files content
      const importantFiles = this.selectImportantFiles(codebaseContext.files);
      let currentLength = context.length;

      for (const file of importantFiles) {
        try {
          const content = await this.codebaseAnalyzer.getFileContent(file.path);
          const fileSection = `
### ${file.relativePath}
\`\`\`${this.getLanguageFromExtension(file.extension)}
${content}
\`\`\`
`;

          if (currentLength + fileSection.length > this.maxContextLength) {
            this.logger.debug('Context length limit reached, truncating...');
            break;
          }

          context += fileSection;
          currentLength += fileSection.length;
        } catch (error) {
          this.logger.debug(`Failed to read file ${file.relativePath}: ${error}`);
        }
      }

      context += `
## Instructions
- You have access to the above codebase structure and key files
- When making changes, consider the existing code patterns and architecture
- Always provide clear explanations for your suggestions
- If you need to see more files, ask the user to provide them
`;

      return context;
    } catch (error) {
      this.logger.error(`Failed to build context: ${error}`);
      return 'You are GOC Agent, an intelligent coding assistant. The codebase context could not be loaded.';
    }
  }

  public async buildFileContext(filePath: string): Promise<string> {
    try {
      const codebaseContext = await this.codebaseAnalyzer.scanCodebase();
      const content = await this.codebaseAnalyzer.getFileContent(filePath);
      const relativePath = path.relative(process.cwd(), filePath);
      
      let context = `You are GOC Agent, an intelligent coding assistant analyzing a specific file in this codebase:

## Project Overview
- Total files: ${codebaseContext.totalFiles}
- Languages: ${codebaseContext.languages.join(', ')}

## Current File: ${relativePath}
\`\`\`${this.getLanguageFromExtension(path.extname(filePath))}
${content}
\`\`\`

## Related Files
`;

      // Find related files (same directory, similar names, imports, etc.)
      const relatedFiles = this.findRelatedFiles(filePath, codebaseContext.files);
      let currentLength = context.length;

      for (const file of relatedFiles.slice(0, 5)) { // Limit to 5 related files
        try {
          const relatedContent = await this.codebaseAnalyzer.getFileContent(file.path);
          const fileSection = `
### ${file.relativePath}
\`\`\`${this.getLanguageFromExtension(file.extension)}
${relatedContent}
\`\`\`
`;

          if (currentLength + fileSection.length > this.maxContextLength) {
            break;
          }

          context += fileSection;
          currentLength += fileSection.length;
        } catch (error) {
          this.logger.debug(`Failed to read related file ${file.relativePath}: ${error}`);
        }
      }

      return context;
    } catch (error) {
      this.logger.error(`Failed to build file context: ${error}`);
      return `You are analyzing the file: ${filePath}`;
    }
  }

  public async buildEditContext(filePath: string, instruction: string): Promise<string> {
    const fileContext = await this.buildFileContext(filePath);
    
    return `${fileContext}

## Edit Request
The user wants to make the following changes to ${path.relative(process.cwd(), filePath)}:

${instruction}

Please provide the modified file content. Make sure to:
1. Follow the existing code style and patterns
2. Maintain compatibility with the rest of the codebase
3. Only make the requested changes
4. Preserve all existing functionality unless explicitly asked to change it
`;
  }

  private selectImportantFiles(files: FileInfo[]): FileInfo[] {
    // Prioritize important files
    const important: FileInfo[] = [];
    const priorities = [
      // Configuration files
      /^(package\.json|tsconfig\.json|\.env|config\.|settings\.).*$/,
      // Main entry points
      /^(index\.|main\.|app\.|server\.).*$/,
      // README and documentation
      /^(readme|changelog|license).*$/i,
      // Source files in root or src directory
      /^(src\/|lib\/)?[^\/]+\.(ts|js|py|java|cpp|c|cs|php|rb|go|rs)$/,
    ];

    // Sort by priority and size
    const sortedFiles = files.sort((a, b) => {
      const aPriority = priorities.findIndex(p => p.test(a.relativePath));
      const bPriority = priorities.findIndex(p => p.test(b.relativePath));
      
      if (aPriority !== -1 && bPriority !== -1) {
        return aPriority - bPriority;
      }
      if (aPriority !== -1) return -1;
      if (bPriority !== -1) return 1;
      
      // If no priority, prefer smaller files
      return a.size - b.size;
    });

    // Take top files up to a reasonable limit
    return sortedFiles.slice(0, 20);
  }

  private findRelatedFiles(filePath: string, allFiles: FileInfo[]): FileInfo[] {
    const relativePath = path.relative(process.cwd(), filePath);
    const directory = path.dirname(relativePath);
    const baseName = path.basename(relativePath, path.extname(relativePath));
    
    return allFiles
      .filter(file => {
        // Same directory
        if (path.dirname(file.relativePath) === directory) return true;
        
        // Similar names
        const fileBaseName = path.basename(file.relativePath, path.extname(file.relativePath));
        if (fileBaseName.includes(baseName) || baseName.includes(fileBaseName)) return true;
        
        // Test files
        if (file.relativePath.includes('.test.') || file.relativePath.includes('.spec.')) {
          if (file.relativePath.includes(baseName)) return true;
        }
        
        return false;
      })
      .filter(file => file.relativePath !== relativePath) // Exclude the current file
      .sort((a, b) => a.size - b.size); // Prefer smaller files
  }

  private getLanguageFromExtension(extension: string): string {
    const languageMap: { [key: string]: string } = {
      '.js': 'javascript',
      '.ts': 'typescript',
      '.jsx': 'jsx',
      '.tsx': 'tsx',
      '.py': 'python',
      '.java': 'java',
      '.cpp': 'cpp',
      '.c': 'c',
      '.h': 'c',
      '.cs': 'csharp',
      '.php': 'php',
      '.rb': 'ruby',
      '.go': 'go',
      '.rs': 'rust',
      '.md': 'markdown',
      '.json': 'json',
      '.yaml': 'yaml',
      '.yml': 'yaml',
      '.html': 'html',
      '.css': 'css',
      '.scss': 'scss',
      '.sql': 'sql'
    };

    return languageMap[extension] || '';
  }

  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}
