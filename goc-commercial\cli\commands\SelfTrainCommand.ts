import { Command } from 'commander';
import { Logger } from '../utils/Logger';
import { ConfigManager } from '../config/ConfigManager';
import { AIProviderManager } from '../ai/AIProviderManager';
import { CodebaseAnalyzer } from '../codebase/CodebaseAnalyzer';
import { MemoryManager } from '../agent/MemoryManager';
import { SelfTrainingFactory, SelfTrainingComponents } from '../training/SelfTrainingFactory';
import { ContinuousTrainingDaemon } from '../training/ContinuousTrainingDaemon';

export class SelfTrainCommand {
  private logger: Logger;
  private config: ConfigManager;
  private aiManager: AIProviderManager;
  private codebaseAnalyzer: CodebaseAnalyzer;
  private memoryManager: MemoryManager;
  private selfTrainingComponents?: SelfTrainingComponents;
  private continuousTrainingDaemon?: ContinuousTrainingDaemon;

  constructor() {
    this.logger = new Logger();
    this.config = new ConfigManager();
    this.aiManager = new AIProviderManager(this.config, this.logger);
    this.codebaseAnalyzer = new CodebaseAnalyzer(this.config, this.logger);
    this.memoryManager = new MemoryManager(this.logger);
  }

  public createCommand(): Command {
    const command = new Command('self-train');
    command.description('Enable and manage autonomous self-training capabilities');

    // Enable self-training
    command
      .command('enable')
      .description('Enable autonomous self-training')
      .option('--minimal', 'Enable minimal self-training mode')
      .option('--intensity <level>', 'Set learning intensity (low, medium, high)', 'medium')
      .option('--research', 'Enable autonomous web research')
      .action(async (options) => {
        await this.enableSelfTraining(options);
      });

    // Disable self-training
    command
      .command('disable')
      .description('Disable autonomous self-training')
      .action(async () => {
        await this.disableSelfTraining();
      });

    // Show training status
    command
      .command('status')
      .description('Show self-training status and metrics')
      .action(async () => {
        await this.showTrainingStatus();
      });

    // Manual training session
    command
      .command('train')
      .description('Start a manual training session')
      .option('--topics <topics>', 'Specific topics to research (comma-separated)')
      .action(async (options) => {
        await this.manualTrainingSession(options);
      });

    // Show knowledge stats
    command
      .command('knowledge')
      .description('Show knowledge repository statistics')
      .action(async () => {
        await this.showKnowledgeStats();
      });

    // Learning goals management
    command
      .command('goals')
      .description('Manage learning goals')
      .option('--list', 'List current learning goals')
      .option('--add <goal>', 'Add a new learning goal')
      .option('--complete <id>', 'Mark a learning goal as completed')
      .action(async (options) => {
        await this.manageLearningGoals(options);
      });

    // Continuous training daemon
    command
      .command('daemon')
      .description('Start continuous training daemon')
      .option('--intensity <level>', 'Training intensity (minimal, low, medium, high, maximum)', 'low')
      .option('--aspects <list>', 'Comma-separated list of aspects to train')
      .option('--background', 'Run in background')
      .action(async (options) => {
        await this.startContinuousTraining(options);
      });

    // Stop daemon
    command
      .command('stop-daemon')
      .description('Stop continuous training daemon')
      .action(async () => {
        await this.stopContinuousTraining();
      });

    // Daemon status
    command
      .command('daemon-status')
      .description('Show continuous training daemon status')
      .action(async () => {
        await this.showDaemonStatus();
      });

    // Detailed metrics
    command
      .command('metrics')
      .description('Show detailed training metrics and analytics')
      .action(async () => {
        await this.showDetailedMetrics();
      });

    return command;
  }

  private async enableSelfTraining(options: any): Promise<void> {
    try {
      this.logger.info('🚀 Initializing self-training system...');

      // Create self-training components
      this.selfTrainingComponents = await SelfTrainingFactory.initializeWithAgent(
        this.logger,
        this.aiManager,
        this.memoryManager,
        this.codebaseAnalyzer,
        options.minimal
      );

      // Validate the system
      const isValid = await SelfTrainingFactory.prototype.validateSelfTrainingSystem.call(
        new SelfTrainingFactory(this.logger, this.aiManager, this.memoryManager, this.codebaseAnalyzer),
        this.selfTrainingComponents
      );

      if (!isValid) {
        throw new Error('Self-training system validation failed');
      }

      // Configure based on options
      const config = {
        enabled: true,
        continuousLearning: true,
        autonomousResearch: options.research || !options.minimal,
        performanceOptimization: true,
        knowledgeSharing: true,
        adaptiveBehavior: true,
        backgroundProcessing: true,
        learningIntensity: options.intensity as 'low' | 'medium' | 'high'
      };

      this.selfTrainingComponents.orchestrator.updateConfig(config);

      // Start self-training
      await this.selfTrainingComponents.orchestrator.startSelfTraining();

      this.logger.success('✅ Self-training enabled successfully!');
      this.logger.info('🧠 The agent will now learn continuously from every interaction');
      this.logger.info('🔍 Autonomous research and knowledge expansion is active');
      this.logger.info('📊 Performance monitoring and optimization is enabled');

      // Show initial status
      await this.showTrainingStatus();

    } catch (error) {
      this.logger.error(`❌ Failed to enable self-training: ${error}`);
      process.exit(1);
    }
  }

  private async disableSelfTraining(): Promise<void> {
    try {
      if (!this.selfTrainingComponents) {
        this.logger.warn('⚠️ Self-training is not currently enabled');
        return;
      }

      await this.selfTrainingComponents.orchestrator.stopSelfTraining();
      this.logger.success('✅ Self-training disabled');

    } catch (error) {
      this.logger.error(`❌ Failed to disable self-training: ${error}`);
    }
  }

  private async showTrainingStatus(): Promise<void> {
    try {
      if (!this.selfTrainingComponents) {
        this.logger.info('📊 Self-training is not currently enabled');
        return;
      }

      const status = this.selfTrainingComponents.orchestrator.getTrainingStatus();
      const metrics = this.selfTrainingComponents.orchestrator.getMetrics();

      this.logger.info('\n📊 Self-Training Status:');
      this.logger.info(`   Status: ${status.isTraining ? '🟢 Active' : '🔴 Inactive'}`);
      this.logger.info(`   Learning Intensity: ${status.config.learningIntensity}`);
      this.logger.info(`   Autonomous Research: ${status.config.autonomousResearch ? '✅' : '❌'}`);
      this.logger.info(`   Continuous Learning: ${status.config.continuousLearning ? '✅' : '❌'}`);

      this.logger.info('\n📈 Training Metrics:');
      this.logger.info(`   Learning Cycles: ${metrics.totalLearningCycles}`);
      this.logger.info(`   Knowledge Growth Rate: ${(metrics.knowledgeGrowthRate * 100).toFixed(1)}%`);
      this.logger.info(`   Performance Improvement: ${(metrics.performanceImprovement * 100).toFixed(1)}%`);
      this.logger.info(`   Autonomy Level: ${(metrics.autonomyLevel * 100).toFixed(1)}%`);
      this.logger.info(`   Adaptability Score: ${(metrics.adaptabilityScore * 100).toFixed(1)}%`);

      this.logger.info('\n🎯 Learning Goals:');
      this.logger.info(`   Active Goals: ${status.activeGoals}`);
      this.logger.info(`   Completed Goals: ${status.completedGoals}`);

    } catch (error) {
      this.logger.error(`❌ Failed to show training status: ${error}`);
    }
  }

  private async manualTrainingSession(options: any): Promise<void> {
    try {
      if (!this.selfTrainingComponents) {
        this.logger.error('❌ Self-training system not initialized. Run "goc self-train enable" first.');
        return;
      }

      this.logger.info('🎓 Starting manual training session...');

      if (options.topics) {
        const topics = options.topics.split(',').map((t: string) => t.trim());
        this.logger.info(`🔍 Researching specific topics: ${topics.join(', ')}`);
        
        await this.selfTrainingComponents.webLearningEngine.conductAutonomousResearch(topics);
      } else {
        // Generate and research autonomous topics
        const topics = await this.selfTrainingComponents.curiosityEngine.generateResearchTopics();
        this.logger.info(`🔍 Researching generated topics: ${topics.slice(0, 3).join(', ')}`);
        
        await this.selfTrainingComponents.webLearningEngine.conductAutonomousResearch(topics.slice(0, 3));
      }

      // Run a full training cycle
      await this.selfTrainingComponents.orchestrator.startSelfTraining();

      this.logger.success('✅ Manual training session completed');

    } catch (error) {
      this.logger.error(`❌ Manual training session failed: ${error}`);
    }
  }

  private async showKnowledgeStats(): Promise<void> {
    try {
      if (!this.selfTrainingComponents) {
        this.logger.error('❌ Self-training system not initialized');
        return;
      }

      const stats = this.selfTrainingComponents.knowledgeRepository.getKnowledgeStats();
      const expertise = this.selfTrainingComponents.knowledgeRepository.getDomainExpertise();

      this.logger.info('\n📚 Knowledge Repository Statistics:');
      this.logger.info(`   Total Knowledge Nodes: ${stats.totalNodes}`);
      this.logger.info(`   Knowledge Domains: ${stats.totalDomains}`);
      this.logger.info(`   Cross-Domain Connections: ${stats.totalConnections}`);
      this.logger.info(`   Generated Insights: ${stats.totalInsights}`);
      this.logger.info(`   Average Confidence: ${(stats.averageConfidence * 100).toFixed(1)}%`);

      this.logger.info('\n🎯 Domain Expertise:');
      for (const [domain, level] of expertise) {
        const percentage = (level * 100).toFixed(1);
        const bar = '█'.repeat(Math.floor(level * 10)) + '░'.repeat(10 - Math.floor(level * 10));
        this.logger.info(`   ${domain}: ${bar} ${percentage}%`);
      }

    } catch (error) {
      this.logger.error(`❌ Failed to show knowledge stats: ${error}`);
    }
  }

  private async manageLearningGoals(options: any): Promise<void> {
    try {
      if (!this.selfTrainingComponents) {
        this.logger.error('❌ Self-training system not initialized');
        return;
      }

      if (options.list) {
        const goals = this.selfTrainingComponents.orchestrator.getLearningGoals();
        
        this.logger.info('\n🎯 Learning Goals:');
        for (const goal of goals) {
          const progress = (goal.progress * 100).toFixed(1);
          const status = goal.status === 'active' ? '🟢' : goal.status === 'completed' ? '✅' : '⏸️';
          this.logger.info(`   ${status} [${goal.domain}] ${goal.objective}`);
          this.logger.info(`      Progress: ${progress}% | Priority: ${goal.priority}`);
        }
      }

      if (options.add) {
        const goalId = await this.selfTrainingComponents.orchestrator.addLearningGoal({
          domain: 'custom',
          objective: options.add,
          priority: 0.7,
          progress: 0,
          resources: ['web-research'],
          status: 'active'
        });
        
        this.logger.success(`✅ Added learning goal: ${options.add} (ID: ${goalId})`);
      }

      if (options.complete) {
        await this.selfTrainingComponents.orchestrator.completeLearningGoal(options.complete);
        this.logger.success(`✅ Marked learning goal as completed: ${options.complete}`);
      }

    } catch (error) {
      this.logger.error(`❌ Failed to manage learning goals: ${error}`);
    }
  }

  private async startContinuousTraining(options: any): Promise<void> {
    try {
      this.logger.info('🚀 Starting continuous training daemon...');

      // Initialize self-training components if not already done
      if (!this.selfTrainingComponents) {
        this.selfTrainingComponents = await SelfTrainingFactory.initializeWithAgent(
          this.logger,
          this.aiManager,
          this.memoryManager,
          this.codebaseAnalyzer,
          false // Full mode for continuous training
        );
      }

      // Create continuous training daemon
      this.continuousTrainingDaemon = new ContinuousTrainingDaemon(
        this.logger,
        this.selfTrainingComponents.orchestrator,
        this.memoryManager,
        this.aiManager
      );

      // Configure daemon based on options
      const config = {
        enabled: true,
        intensity: options.intensity || 'low',
        aspects: {
          coding: true,
          webResearch: true,
          performance: true,
          adaptiveBehavior: true,
          knowledgeExpansion: true,
          curiosityDriven: true
        },
        scheduling: {
          quickTraining: 30,
          deepTraining: 4,
          comprehensiveTraining: 1
        },
        resourceLimits: {
          maxCpuUsage: 25,
          maxMemoryUsage: 512,
          maxNetworkRequests: 100
        }
      };

      // Parse aspects if provided
      if (options.aspects) {
        const aspectsList = options.aspects.split(',').map((a: string) => a.trim());
        config.aspects = {
          coding: aspectsList.includes('coding'),
          webResearch: aspectsList.includes('webResearch'),
          performance: aspectsList.includes('performance'),
          adaptiveBehavior: aspectsList.includes('adaptiveBehavior'),
          knowledgeExpansion: aspectsList.includes('knowledgeExpansion'),
          curiosityDriven: aspectsList.includes('curiosityDriven')
        };
      }

      this.continuousTrainingDaemon.updateConfig(config);

      // Start the daemon
      await this.continuousTrainingDaemon.start();

      this.logger.success('✅ Continuous training daemon started successfully!');
      this.logger.info('🔄 The agent will now train continuously across all aspects');
      this.logger.info(`📊 Training intensity: ${config.intensity}`);
      this.logger.info(`🎯 Active aspects: ${Object.entries(config.aspects).filter(([_, enabled]) => enabled).map(([aspect]) => aspect).join(', ')}`);

      if (!options.background) {
        this.logger.info('💡 Use Ctrl+C to stop the daemon');
        // Keep process alive
        process.on('SIGINT', async () => {
          await this.stopContinuousTraining();
          process.exit(0);
        });

        // Keep the process running
        await new Promise(() => {}); // Infinite wait
      }

    } catch (error) {
      this.logger.error(`❌ Failed to start continuous training: ${error}`);
    }
  }

  private async stopContinuousTraining(): Promise<void> {
    try {
      if (!this.continuousTrainingDaemon) {
        this.logger.warn('⚠️ Continuous training daemon is not running');
        return;
      }

      this.logger.info('🛑 Stopping continuous training daemon...');
      await this.continuousTrainingDaemon.stop();
      this.continuousTrainingDaemon = undefined;

      this.logger.success('✅ Continuous training daemon stopped');

    } catch (error) {
      this.logger.error(`❌ Failed to stop continuous training: ${error}`);
    }
  }

  private async showDaemonStatus(): Promise<void> {
    try {
      if (!this.continuousTrainingDaemon) {
        this.logger.info('📊 Continuous training daemon is not running');
        return;
      }

      const status = this.continuousTrainingDaemon.getStatus();

      this.logger.info('\n🤖 Continuous Training Daemon Status:');
      this.logger.info(`   Status: ${status.isRunning ? '🟢 Running' : '🔴 Stopped'}`);
      this.logger.info(`   Intensity: ${status.config.intensity}`);
      this.logger.info(`   Active Schedules: ${status.activeSchedules.join(', ')}`);

      this.logger.info('\n🎯 Training Aspects:');
      for (const [aspect, enabled] of Object.entries(status.config.aspects)) {
        this.logger.info(`   ${aspect}: ${enabled ? '✅' : '❌'}`);
      }

      this.logger.info('\n⏰ Last Training Times:');
      for (const [type, time] of Object.entries(status.lastTrainingTimes)) {
        this.logger.info(`   ${type}: ${time || 'Never'}`);
      }

      this.logger.info('\n📈 Performance Metrics:');
      const metrics = status.metrics;
      this.logger.info(`   Total Sessions: ${metrics.totalSessions}`);
      this.logger.info(`   Success Rate: ${metrics.recentSuccessRate}%`);
      this.logger.info(`   Avg Duration: ${metrics.averageSessionDuration}s`);
      this.logger.info(`   Memory Usage: ${metrics.currentMemoryUsage}MB`);

    } catch (error) {
      this.logger.error(`❌ Failed to show daemon status: ${error}`);
    }
  }

  private async showDetailedMetrics(): Promise<void> {
    try {
      if (!this.continuousTrainingDaemon) {
        this.logger.error('❌ Continuous training daemon is not running');
        return;
      }

      const status = this.continuousTrainingDaemon.getStatus();
      const metrics = status.metrics;

      this.logger.info('\n📊 Detailed Training Metrics:');

      this.logger.info('\n🎯 Overall Performance:');
      this.logger.info(`   Total Training Sessions: ${metrics.totalSessions}`);
      this.logger.info(`   Recent Success Rate: ${metrics.recentSuccessRate}%`);
      this.logger.info(`   Average Session Duration: ${metrics.averageSessionDuration}s`);
      this.logger.info(`   Current Memory Usage: ${metrics.currentMemoryUsage}MB`);

      if (metrics.performance) {
        this.logger.info('\n📈 Performance Trends:');
        this.logger.info(`   Success Rate: ${(metrics.performance.successRate * 100).toFixed(1)}%`);
        this.logger.info(`   Efficiency Score: ${metrics.performance.efficiencyScore.toFixed(2)}`);
        this.logger.info(`   Learning Velocity: ${metrics.performance.learningVelocity.toFixed(2)}/hour`);
        this.logger.info(`   Improvement Trend: ${metrics.performance.improvementTrend > 0 ? '📈' : '📉'} ${(metrics.performance.improvementTrend * 100).toFixed(1)}%`);
      }

      if (metrics.trends && !metrics.trends.insufficient_data) {
        this.logger.info('\n📊 Recent Trends:');
        this.logger.info(`   Success Rate Trend: ${metrics.trends.successRateTrend > 0 ? '📈' : '📉'} ${(metrics.trends.successRateTrend * 100).toFixed(1)}%`);
        this.logger.info(`   Efficiency Trend: ${metrics.trends.efficiencyTrend > 0 ? '📈' : '📉'} ${metrics.trends.efficiencyTrend.toFixed(2)}`);
        this.logger.info(`   Learning Velocity Trend: ${metrics.trends.learningVelocityTrend > 0 ? '📈' : '📉'} ${metrics.trends.learningVelocityTrend.toFixed(2)}`);
        this.logger.info(`   Overall Trend: ${metrics.trends.overallTrend > 0 ? '🟢 Improving' : '🔴 Declining'}`);
      }

    } catch (error) {
      this.logger.error(`❌ Failed to show detailed metrics: ${error}`);
    }
  }
}
