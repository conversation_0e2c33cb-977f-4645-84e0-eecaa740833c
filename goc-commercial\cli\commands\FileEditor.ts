import fs from 'fs';
import path from 'path';
import { createTwoFilesPatch } from 'diff';
import inquirer from 'inquirer';
import chalk from 'chalk';
import { AIProviderManager, ChatMessage } from '../ai/AIProviderManager';
import { ContextBuilder } from './ContextBuilder';
import { Logger } from '../utils/Logger';
import { ChangeTracker } from '../utils/ChangeTracker';
import { FileChangeAnalyzer } from '../utils/FileChangeAnalyzer';

export class FileEditor {
  private logger: Logger;
  private changeTracker: ChangeTracker;

  constructor(logger: Logger, changeTracker: ChangeTracker) {
    this.logger = logger;
    this.changeTracker = changeTracker;
  }

  public async editFile(
    filePath: string,
    instruction: string,
    aiManager: AIProviderManager,
    contextBuilder: ContextBuilder
  ): Promise<void> {
    try {
      // Check if file exists
      if (!fs.existsSync(filePath)) {
        throw new Error(`File not found: ${filePath}`);
      }

      // Track the start of file operation
      this.changeTracker.trackFileOperation(filePath, 'editing');

      // Read current content
      const originalContent = fs.readFileSync(filePath, 'utf8');

      // Build context for the edit
      const context = await contextBuilder.buildEditContext(filePath, instruction);

      // Prepare messages for AI
      const messages: ChatMessage[] = [
        {
          role: 'system',
          content: context
        },
        {
          role: 'user',
          content: `Please modify the file according to the instruction: ${instruction}`
        }
      ];

      this.logger.loading('Generating changes...');

      // Get AI response
      const response = await aiManager.chat(messages);

      // Extract code from response (assuming it's wrapped in code blocks)
      const newContent = this.extractCodeFromResponse(response.content);

      if (!newContent) {
        throw new Error('Could not extract valid code from AI response');
      }

      // Show diff
      const diff = this.createDiff(filePath, originalContent, newContent);
      console.log('\nProposed changes:');
      console.log(diff);

      // Ask for confirmation
      const { confirm } = await inquirer.prompt([
        {
          type: 'confirm',
          name: 'confirm',
          message: 'Apply these changes?',
          default: true
        }
      ]);

      if (confirm) {
        // Create backup
        const backupPath = FileChangeAnalyzer.createBackup(filePath, originalContent);

        // Write new content
        fs.writeFileSync(filePath, newContent);

        // Record the change
        await this.changeTracker.recordFileChange(
          filePath,
          'edited',
          originalContent,
          newContent,
          backupPath
        );
      } else {
        this.logger.info('Changes cancelled');
      }

    } catch (error) {
      throw new Error(`Failed to edit file: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  public async createFile(
    filePath: string,
    instruction: string,
    aiManager: AIProviderManager,
    contextBuilder: ContextBuilder
  ): Promise<void> {
    try {
      // Track the start of file operation
      this.changeTracker.trackFileOperation(filePath, 'creating');

      // Check if file already exists
      if (fs.existsSync(filePath)) {
        const { overwrite } = await inquirer.prompt([
          {
            type: 'confirm',
            name: 'overwrite',
            message: `File ${filePath} already exists. Overwrite?`,
            default: false
          }
        ]);

        if (!overwrite) {
          this.logger.info('File creation cancelled');
          return;
        }
      }

      // Build context
      const context = await contextBuilder.buildContext();
      
      // Prepare messages for AI
      const messages: ChatMessage[] = [
        {
          role: 'system',
          content: `${context}

You are asked to create a new file. Please provide the complete file content based on the instruction.`
        },
        {
          role: 'user',
          content: `Create a new file at ${filePath} with the following requirements: ${instruction}`
        }
      ];

      this.logger.loading('Generating file content...');
      
      // Get AI response
      const response = await aiManager.chat(messages);
      
      // Extract code from response
      const content = this.extractCodeFromResponse(response.content);
      
      if (!content) {
        throw new Error('Could not extract valid code from AI response');
      }

      // Show preview
      console.log('\nGenerated content:');
      console.log(chalk.gray('─'.repeat(50)));
      console.log(content);
      console.log(chalk.gray('─'.repeat(50)));

      // Ask for confirmation
      const { confirm } = await inquirer.prompt([
        {
          type: 'confirm',
          name: 'confirm',
          message: 'Create this file?',
          default: true
        }
      ]);

      if (confirm) {
        // Ensure directory exists
        const dir = path.dirname(filePath);
        if (!fs.existsSync(dir)) {
          fs.mkdirSync(dir, { recursive: true });
        }

        // Write file
        fs.writeFileSync(filePath, content);

        // Record the change
        await this.changeTracker.recordFileChange(
          filePath,
          'created',
          undefined,
          content
        );
      } else {
        this.logger.info('File creation cancelled');
      }

    } catch (error) {
      throw new Error(`Failed to create file: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  private extractCodeFromResponse(response: string): string | null {
    // Try to extract code from markdown code blocks
    const codeBlockRegex = /```[\w]*\n([\s\S]*?)\n```/;
    const match = response.match(codeBlockRegex);
    
    if (match && match[1]) {
      return match[1].trim();
    }

    // If no code blocks found, check if the entire response looks like code
    const lines = response.split('\n');
    const codeIndicators = [
      'import ', 'export ', 'function ', 'class ', 'const ', 'let ', 'var ',
      'def ', 'public ', 'private ', 'protected ', '#include', 'package ',
      '<?php', '<!DOCTYPE', '<html', '{', '}', ';'
    ];

    const codeLines = lines.filter(line => 
      codeIndicators.some(indicator => line.trim().startsWith(indicator)) ||
      line.trim().endsWith('{') ||
      line.trim().endsWith(';') ||
      line.trim().endsWith('}')
    );

    // If more than 30% of lines look like code, treat the whole response as code
    if (codeLines.length / lines.length > 0.3) {
      return response.trim();
    }

    return null;
  }

  private createDiff(filePath: string, oldContent: string, newContent: string): string {
    const patch = createTwoFilesPatch(
      filePath,
      filePath,
      oldContent,
      newContent,
      'original',
      'modified'
    );

    // Colorize the diff
    return patch
      .split('\n')
      .map(line => {
        if (line.startsWith('+') && !line.startsWith('+++')) {
          return chalk.green(line);
        } else if (line.startsWith('-') && !line.startsWith('---')) {
          return chalk.red(line);
        } else if (line.startsWith('@@')) {
          return chalk.cyan(line);
        }
        return line;
      })
      .join('\n');
  }
}
