import chalk from 'chalk';

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3
}

export interface SessionInfo {
  provider?: string;
  model?: string;
  contextEngine?: string;
  operation?: string;
}

export class Logger {
  private level: LogLevel;
  private sessionInfo: SessionInfo = {};
  private suppressInitLogs: boolean = false;
  private userFriendlyMode: boolean = false;

  constructor(level: LogLevel = LogLevel.INFO) {
    this.level = level;
  }

  public setSuppressInitLogs(suppress: boolean): void {
    this.suppressInitLogs = suppress;
  }

  public getSuppressInitLogs(): boolean {
    return this.suppressInitLogs;
  }

  public setUserFriendlyMode(enabled: boolean): void {
    this.userFriendlyMode = enabled;
  }

  public setLevel(level: LogLevel): void {
    this.level = level;
  }

  public setSessionInfo(info: Partial<SessionInfo>): void {
    this.sessionInfo = { ...this.sessionInfo, ...info };
  }

  public clearSessionInfo(): void {
    this.sessionInfo = {};
  }

  private formatSessionHeader(): string {
    if (!this.sessionInfo.provider && !this.sessionInfo.model && !this.sessionInfo.contextEngine) {
      return '';
    }

    const parts: string[] = [];

    if (this.sessionInfo.provider) {
      parts.push(chalk.cyan(`${this.sessionInfo.provider}`));
    }

    if (this.sessionInfo.model) {
      parts.push(chalk.gray(`${this.sessionInfo.model}`));
    }

    if (this.sessionInfo.contextEngine) {
      parts.push(chalk.magenta(`${this.sessionInfo.contextEngine}`));
    }

    const header = parts.join(chalk.gray(' • '));
    const separator = chalk.gray('─'.repeat(Math.min(80, header.length + 10)));

    return `\n${chalk.gray('┌')} ${header}\n${chalk.gray('└')}${separator}\n`;
  }

  public showSessionHeader(): void {
    const header = this.formatSessionHeader();
    if (header) {
      console.log(header);
    }
  }

  public debug(message: string, ...args: any[]): void {
    if (this.level <= LogLevel.DEBUG) {
      console.log(chalk.gray(`[DEBUG] ${message}`), ...args);
    }
  }

  public info(message: string, ...args: any[]): void {
    if (this.level <= LogLevel.INFO && !this.suppressInitLogs) {
      // Skip training-related logs during chat sessions
      if (this.suppressInitLogs && this.isTrainingRelatedLog(message)) {
        return;
      }

      // In user-friendly mode, suppress technical logs
      if (this.userFriendlyMode && this.isTechnicalLog(message)) {
        return;
      }

      console.log(chalk.blue(`[INFO] ${message}`), ...args);
    }
  }

  private isTrainingRelatedLog(message: string): boolean {
    const trainingKeywords = [
      'self-training',
      'Self-Training',
      'Orchestrator',
      'learning goals',
      'continuous learning',
      'Initializing',
      'Creating minimal',
      'configuration updated',
      'Starting continuous learning engine'
    ];

    return trainingKeywords.some(keyword => message.includes(keyword));
  }

  private isTechnicalLog(message: string): boolean {
    const technicalKeywords = [
      'Scanning codebase',
      'Scanned',
      'files',
      'Bytes',
      'Analyzed codebase',
      'Raw response',
      'Cleaned response',
      'Cycle',
      'Evaluating progress',
      'Executing analyze',
      'Failed to parse',
      'JSON',
      'iteration'
    ];

    return technicalKeywords.some(keyword => message.includes(keyword));
  }

  public warn(message: string, ...args: any[]): void {
    if (this.level <= LogLevel.WARN) {
      // In user-friendly mode, suppress technical warnings
      if (this.userFriendlyMode && this.isTechnicalLog(message)) {
        return;
      }
      console.log(chalk.yellow(`[WARN] ${message}`), ...args);
    }
  }

  public error(message: string, ...args: any[]): void {
    if (this.level <= LogLevel.ERROR) {
      console.error(chalk.red(`[ERROR] ${message}`), ...args);
    }
  }

  public success(message: string, ...args: any[]): void {
    if (this.suppressInitLogs && this.isTrainingRelatedLog(message)) {
      return;
    }
    console.log(chalk.green(`✓ ${message}`), ...args);
  }

  public loading(message: string): void {
    console.log(chalk.cyan(`⏳ ${message}`));
  }

  public processing(): void {
    console.log(chalk.cyan(`🤔 Thinking...`));
  }

  public thinking(): void {
    console.log(chalk.cyan(`🧠 Analyzing...`));
  }

  public planning(): void {
    console.log(chalk.cyan(`📋 Planning approach...`));
  }

  public executing(): void {
    console.log(chalk.cyan(`⚡ Executing...`));
  }

  // Change tracking specific methods
  public fileOperation(operation: string, filename: string): void {
    const icon = operation === 'creating' ? '📝' : operation === 'editing' ? '✏️' : '🔧';
    console.log(chalk.cyan(`${icon} ${operation} ${chalk.bold(filename)}`));
  }

  public changesSummary(changes: { filename: string; linesAdded: number; linesDeleted: number; operation: string }[]): void {
    console.log(chalk.yellow('\n📊 Changes Summary:'));
    console.log(chalk.gray('─'.repeat(60)));

    changes.forEach(change => {
      const icon = change.operation === 'created' ? '📝' : change.operation === 'edited' ? '✏️' : '🗑️';
      const addedText = change.linesAdded > 0 ? chalk.green(`+${change.linesAdded}`) : '';
      const deletedText = change.linesDeleted > 0 ? chalk.red(`-${change.linesDeleted}`) : '';
      const changesText = [addedText, deletedText].filter(Boolean).join(' ');

      console.log(`${icon} ${chalk.bold(change.filename)} ${changesText ? `(${changesText})` : ''}`);
    });

    const totalAdded = changes.reduce((sum, c) => sum + c.linesAdded, 0);
    const totalDeleted = changes.reduce((sum, c) => sum + c.linesDeleted, 0);

    console.log(chalk.gray('─'.repeat(60)));
    console.log(chalk.bold(`Total: ${chalk.green(`+${totalAdded}`)} ${chalk.red(`-${totalDeleted}`)} lines across ${changes.length} files`));
  }

  public revertOperation(filename: string): void {
    console.log(chalk.yellow(`🔄 Reverting changes to ${chalk.bold(filename)}`));
  }
}
