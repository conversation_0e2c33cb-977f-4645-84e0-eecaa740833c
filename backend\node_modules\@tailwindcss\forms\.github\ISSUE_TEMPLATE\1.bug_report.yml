name: Bug Report
description: Create a bug report for @tailwindcss/forms.
labels: []
body:
  - type: input
    attributes:
      label: What version of @tailwindcss/forms are you using?
      description: 'For example: v0.1.4'
    validations:
      required: true
  - type: input
    attributes:
      label: What version of Node.js are you using?
      description: 'For example: v12.0.0'
    validations:
      required: true
  - type: input
    attributes:
      label: What browser are you using?
      description: 'For example: Chrome, Safari, or N/A'
    validations:
      required: true
  - type: input
    attributes:
      label: What operating system are you using?
      description: 'For example: macOS, Windows'
    validations:
      required: true
  - type: input
    attributes:
      label: Reproduction repository
      description: A public GitHub repo that includes a minimal reproduction of the bug. Unfortunately we can't provide support without a reproduction, and your issue will be closed and locked with no comment if this is not provided.
    validations:
      required: true
  - type: textarea
    attributes:
      label: Describe your issue
      description: Describe the problem you're seeing, any important steps to reproduce and what behavior you expect instead
