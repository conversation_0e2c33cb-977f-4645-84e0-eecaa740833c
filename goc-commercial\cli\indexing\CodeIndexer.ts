// Intelligent Code Indexing System for GOC Agent

import * as fs from 'fs/promises';
import * as path from 'path';
import { parse } from '@babel/parser';
import traverse from '@babel/traverse';
import * as t from '@babel/types';
import Fuse from 'fuse.js';
import { Logger } from '../utils/Logger';
import { CodebaseAnalyzer, FileInfo } from '../codebase/CodebaseAnalyzer';

export interface CodeSymbol {
  id: string;
  name: string;
  type: 'function' | 'class' | 'interface' | 'variable' | 'constant' | 'method' | 'property';
  filePath: string;
  startLine: number;
  endLine: number;
  signature?: string;
  docstring?: string;
  parameters?: string[];
  returnType?: string;
  visibility?: 'public' | 'private' | 'protected';
  isStatic?: boolean;
  isAsync?: boolean;
  parentClass?: string;
  imports?: string[];
  exports?: string[];
  metadata: Record<string, any>;
}

export interface CodeRelationship {
  from: string; // Symbol ID
  to: string;   // Symbol ID
  type: 'calls' | 'imports' | 'extends' | 'implements' | 'uses' | 'defines' | 'references';
  weight: number; // Relevance score 0-1
  context?: string; // Where this relationship occurs
}

export interface SearchResult {
  symbol: CodeSymbol;
  relevanceScore: number;
  matchReasons: string[];
  contextSnippet?: string;
  relatedSymbols: CodeSymbol[];
}

export interface IndexStats {
  totalSymbols: number;
  totalRelationships: number;
  filesCovered: number;
  lastIndexed: Date;
  indexSize: number; // bytes
  averageQueryTime: number;
}

export class CodeIndexer {
  private logger: Logger;
  private codebaseAnalyzer: CodebaseAnalyzer;
  private symbols: Map<string, CodeSymbol> = new Map();
  private relationships: CodeRelationship[] = [];
  private fileIndex: Map<string, string[]> = new Map(); // file -> symbol IDs
  private nameIndex: Map<string, string[]> = new Map(); // name -> symbol IDs
  private typeIndex: Map<string, string[]> = new Map(); // type -> symbol IDs
  private keywordIndex: Map<string, string[]> = new Map(); // keyword -> symbol IDs
  private fuzzySearchEngine?: Fuse<CodeSymbol>; // Fuzzy search engine
  private stats: IndexStats;

  constructor(logger: Logger, codebaseAnalyzer: CodebaseAnalyzer) {
    this.logger = logger;
    this.codebaseAnalyzer = codebaseAnalyzer;
    this.stats = {
      totalSymbols: 0,
      totalRelationships: 0,
      filesCovered: 0,
      lastIndexed: new Date(),
      indexSize: 0,
      averageQueryTime: 0
    };
  }

  public async buildIndex(rootPath: string = process.cwd()): Promise<void> {
    const startTime = Date.now();
    this.logger.info('🔍 Building intelligent code index...');

    // Clear existing index
    this.clearIndex();

    try {
      // Get all code files
      const codebaseContext = await this.codebaseAnalyzer.scanCodebase(rootPath);
      const codeFiles = codebaseContext.files.filter(file => this.isCodeFile(file.extension));

      this.logger.info(`📁 Indexing ${codeFiles.length} code files...`);

      // Index each file
      for (const file of codeFiles) {
        await this.indexFile(file);
      }

      // Build relationships
      await this.buildRelationships();

      // Build fuzzy search engine
      this.buildFuzzySearchEngine();

      // Update stats
      this.updateStats();

      const indexTime = Date.now() - startTime;
      this.logger.success(`✅ Index built in ${indexTime}ms`);
      this.logger.info(`📊 Indexed ${this.symbols.size} symbols with ${this.relationships.length} relationships`);

    } catch (error) {
      this.logger.error(`❌ Failed to build index: ${error}`);
      throw error;
    }
  }

  private async indexFile(file: FileInfo): Promise<void> {
    try {
      const content = await this.codebaseAnalyzer.getFileContent(file.path);
      const symbols = await this.extractSymbols(file.relativePath, content);
      
      const symbolIds: string[] = [];
      
      for (const symbol of symbols) {
        this.symbols.set(symbol.id, symbol);
        symbolIds.push(symbol.id);
        
        // Update indexes
        this.addToNameIndex(symbol.name.toLowerCase(), symbol.id);
        this.addToTypeIndex(symbol.type, symbol.id);
        this.addKeywordsToIndex(symbol, symbol.id);
      }
      
      this.fileIndex.set(file.relativePath, symbolIds);
      
    } catch (error) {
      this.logger.debug(`Failed to index file ${file.relativePath}: ${error}`);
    }
  }

  private async extractSymbols(filePath: string, content: string): Promise<CodeSymbol[]> {
    const symbols: CodeSymbol[] = [];
    const ext = path.extname(filePath).toLowerCase();

    try {
      // TypeScript/JavaScript extraction using Babel AST
      if (['.ts', '.js', '.tsx', '.jsx'].includes(ext)) {
        symbols.push(...await this.extractTSSymbolsWithAST(filePath, content));
      }
      // Python extraction (fallback to regex for now)
      else if (ext === '.py') {
        symbols.push(...this.extractPythonSymbols(filePath, content));
      }
      // Java extraction (fallback to regex for now)
      else if (ext === '.java') {
        symbols.push(...this.extractSymbolsWithRegex(filePath, content));
      }
      // Add more language extractors as needed
    } catch (error) {
      this.logger.debug(`Failed to extract symbols from ${filePath}: ${error}`);
      // Fallback to simple regex extraction
      symbols.push(...this.extractSymbolsWithRegex(filePath, content));
    }

    return symbols;
  }

  private async extractTSSymbolsWithAST(filePath: string, content: string): Promise<CodeSymbol[]> {
    const symbols: CodeSymbol[] = [];

    try {
      // Parse with Babel
      const ast = parse(content, {
        sourceType: 'module',
        allowImportExportEverywhere: true,
        allowReturnOutsideFunction: true,
        plugins: [
          'typescript',
          'jsx',
          'decorators-legacy',
          'classProperties',
          'asyncGenerators',
          'functionBind',
          'exportDefaultFrom',
          'exportNamespaceFrom',
          'dynamicImport',
          'nullishCoalescingOperator',
          'optionalChaining'
        ]
      });

      // Traverse the AST and extract symbols
      traverse(ast, {
        // Function declarations
        FunctionDeclaration: (path) => {
          const node = path.node;
          if (node.id) {
            const symbol: CodeSymbol = {
              id: `${filePath}:func:${node.id.name}:${node.loc?.start.line || 0}`,
              name: node.id.name,
              type: 'function',
              filePath,
              startLine: node.loc?.start.line || 0,
              endLine: node.loc?.end.line || 0,
              signature: this.generateFunctionSignature(node),
              parameters: node.params.map(param => this.getParameterName(param)),
              isAsync: node.async,
              metadata: {
                exported: this.isExported(path),
                generator: node.generator
              }
            };
            symbols.push(symbol);
          }
        },

        // Arrow functions and function expressions
        VariableDeclarator: (path) => {
          const node = path.node;
          if (t.isIdentifier(node.id) &&
              (t.isArrowFunctionExpression(node.init) || t.isFunctionExpression(node.init))) {
            const funcNode = node.init;
            const symbol: CodeSymbol = {
              id: `${filePath}:func:${node.id.name}:${node.loc?.start.line || 0}`,
              name: node.id.name,
              type: 'function',
              filePath,
              startLine: node.loc?.start.line || 0,
              endLine: node.loc?.end.line || 0,
              signature: this.generateArrowFunctionSignature(node.id.name, funcNode),
              parameters: funcNode.params.map(param => this.getParameterName(param)),
              isAsync: funcNode.async,
              metadata: {
                exported: this.isExported(path.parentPath),
                arrowFunction: t.isArrowFunctionExpression(funcNode)
              }
            };
            symbols.push(symbol);
          }
        },

        // Class declarations
        ClassDeclaration: (path) => {
          const node = path.node;
          if (node.id) {
            const symbol: CodeSymbol = {
              id: `${filePath}:class:${node.id.name}:${node.loc?.start.line || 0}`,
              name: node.id.name,
              type: 'class',
              filePath,
              startLine: node.loc?.start.line || 0,
              endLine: node.loc?.end.line || 0,
              signature: this.generateClassSignature(node),
              metadata: {
                exported: this.isExported(path),
                abstract: node.abstract || false,
                extends: node.superClass ? this.getNodeName(node.superClass) : undefined,
                implements: node.implements?.map(impl => {
                  if ('expression' in impl) {
                    return this.getNodeName(impl.expression);
                  }
                  return this.getNodeName(impl);
                }) || []
              }
            };
            symbols.push(symbol);
          }
        },

        // Method definitions
        ClassMethod: (path) => {
          const node = path.node;
          if (t.isIdentifier(node.key)) {
            const className = this.getParentClassName(path);
            const symbol: CodeSymbol = {
              id: `${filePath}:method:${className}:${node.key.name}:${node.loc?.start.line || 0}`,
              name: node.key.name,
              type: 'method',
              filePath,
              startLine: node.loc?.start.line || 0,
              endLine: node.loc?.end.line || 0,
              signature: this.generateMethodSignature(node),
              parameters: node.params.map(param => this.getParameterName(param)),
              parentClass: className,
              isAsync: node.async,
              isStatic: node.static,
              visibility: this.getMethodVisibility(node),
              metadata: {
                kind: node.kind, // method, constructor, get, set
                generator: node.generator
              }
            };
            symbols.push(symbol);
          }
        },

        // Interface declarations (TypeScript)
        TSInterfaceDeclaration: (path) => {
          const node = path.node;
          const symbol: CodeSymbol = {
            id: `${filePath}:interface:${node.id.name}:${node.loc?.start.line || 0}`,
            name: node.id.name,
            type: 'interface',
            filePath,
            startLine: node.loc?.start.line || 0,
            endLine: node.loc?.end.line || 0,
            signature: `interface ${node.id.name}`,
            metadata: {
              exported: this.isExported(path),
              extends: node.extends?.map(ext => this.getNodeName(ext.expression)) || []
            }
          };
          symbols.push(symbol);
        },

        // Import declarations
        ImportDeclaration: (path) => {
          const node = path.node;
          const importPath = node.source.value;

          node.specifiers.forEach(spec => {
            if (t.isImportDefaultSpecifier(spec) || t.isImportSpecifier(spec) || t.isImportNamespaceSpecifier(spec)) {
              const symbol: CodeSymbol = {
                id: `${filePath}:import:${spec.local.name}:${node.loc?.start.line || 0}`,
                name: spec.local.name,
                type: 'variable', // Imported symbols are treated as variables
                filePath,
                startLine: node.loc?.start.line || 0,
                endLine: node.loc?.start.line || 0,
                signature: `import ${spec.local.name} from '${importPath}'`,
                imports: [importPath],
                metadata: {
                  importType: spec.type,
                  importPath
                }
              };
              symbols.push(symbol);
            }
          });
        }
      });

    } catch (error) {
      this.logger.debug(`AST parsing failed for ${filePath}: ${error}`);
      // Fallback to regex-based extraction
      return this.extractSymbolsWithRegex(filePath, content);
    }

    return symbols;
  }

  // Helper methods for AST parsing
  private generateFunctionSignature(node: t.FunctionDeclaration): string {
    const name = node.id?.name || 'anonymous';
    const params = node.params.map(param => this.getParameterName(param)).join(', ');
    const async = node.async ? 'async ' : '';
    return `${async}function ${name}(${params})`;
  }

  private generateArrowFunctionSignature(name: string, node: t.ArrowFunctionExpression | t.FunctionExpression): string {
    const params = node.params.map(param => this.getParameterName(param)).join(', ');
    const async = node.async ? 'async ' : '';
    return `${async}${name} = (${params}) => {}`;
  }

  private generateClassSignature(node: t.ClassDeclaration): string {
    const name = node.id?.name || 'anonymous';
    const extends_ = node.superClass ? ` extends ${this.getNodeName(node.superClass)}` : '';
    return `class ${name}${extends_}`;
  }

  private generateMethodSignature(node: t.ClassMethod): string {
    const name = t.isIdentifier(node.key) ? node.key.name : 'unknown';
    const params = node.params.map(param => this.getParameterName(param)).join(', ');
    const static_ = node.static ? 'static ' : '';
    const async = node.async ? 'async ' : '';
    return `${static_}${async}${name}(${params})`;
  }

  private getParameterName(param: t.LVal): string {
    if (t.isIdentifier(param)) {
      return param.name;
    } else if (t.isAssignmentPattern(param) && t.isIdentifier(param.left)) {
      return param.left.name;
    } else if (t.isRestElement(param) && t.isIdentifier(param.argument)) {
      return `...${param.argument.name}`;
    }
    return 'unknown';
  }

  private getNodeName(node: t.Node): string {
    if (t.isIdentifier(node)) {
      return node.name;
    } else if (t.isMemberExpression(node)) {
      return `${this.getNodeName(node.object)}.${this.getNodeName(node.property)}`;
    }
    return 'unknown';
  }

  private getParentClassName(path: any): string {
    let parent = path.parent;
    while (parent) {
      if (t.isClassDeclaration(parent) && parent.id) {
        return parent.id.name;
      }
      parent = parent.parent;
    }
    return 'unknown';
  }

  private getMethodVisibility(node: t.ClassMethod): 'public' | 'private' | 'protected' {
    // TypeScript visibility is handled by decorators or modifiers
    // For now, default to public
    return 'public';
  }

  private isExported(path: any): boolean {
    let parent = path.parent;
    while (parent) {
      if (t.isExportNamedDeclaration(parent) || t.isExportDefaultDeclaration(parent)) {
        return true;
      }
      parent = parent.parent;
    }
    return false;
  }

  // Fallback regex-based extraction
  private extractSymbolsWithRegex(filePath: string, content: string): CodeSymbol[] {
    const symbols: CodeSymbol[] = [];
    const lines = content.split('\n');

    // Simple function extraction
    const functionRegex = /(?:export\s+)?(?:async\s+)?function\s+(\w+)\s*\(/g;
    let match;
    while ((match = functionRegex.exec(content)) !== null) {
      const lineNumber = content.substring(0, match.index).split('\n').length;
      symbols.push({
        id: `${filePath}:func:${match[1]}:${lineNumber}`,
        name: match[1],
        type: 'function',
        filePath,
        startLine: lineNumber,
        endLine: lineNumber + 5, // Estimate
        signature: match[0],
        metadata: {}
      });
    }

    // Simple class extraction
    const classRegex = /(?:export\s+)?class\s+(\w+)/g;
    while ((match = classRegex.exec(content)) !== null) {
      const lineNumber = content.substring(0, match.index).split('\n').length;
      symbols.push({
        id: `${filePath}:class:${match[1]}:${lineNumber}`,
        name: match[1],
        type: 'class',
        filePath,
        startLine: lineNumber,
        endLine: lineNumber + 10, // Estimate
        signature: match[0],
        metadata: {}
      });
    }

    return symbols;
  }

  private extractTSSymbols(filePath: string, content: string, lines: string[]): CodeSymbol[] {
    const symbols: CodeSymbol[] = [];

    // Extract functions
    const functionRegex = /(?:export\s+)?(?:async\s+)?function\s+(\w+)\s*\(([^)]*)\)(?:\s*:\s*([^{]+))?/g;
    let match;
    while ((match = functionRegex.exec(content)) !== null) {
      const lineNumber = content.substring(0, match.index).split('\n').length;
      const params = match[2] ? match[2].split(',').map(p => p.trim()) : [];
      
      symbols.push({
        id: `${filePath}:func:${match[1]}:${lineNumber}`,
        name: match[1],
        type: 'function',
        filePath,
        startLine: lineNumber,
        endLine: this.findEndLine(lineNumber),
        signature: match[0],
        parameters: params,
        returnType: match[3]?.trim(),
        isAsync: match[0].includes('async'),
        metadata: {
          exported: match[0].includes('export')
        }
      });
    }

    // Extract classes
    const classRegex = /(?:export\s+)?(?:abstract\s+)?class\s+(\w+)(?:\s+extends\s+(\w+))?(?:\s+implements\s+([^{]+))?/g;
    while ((match = classRegex.exec(content)) !== null) {
      const lineNumber = content.substring(0, match.index).split('\n').length;
      
      symbols.push({
        id: `${filePath}:class:${match[1]}:${lineNumber}`,
        name: match[1],
        type: 'class',
        filePath,
        startLine: lineNumber,
        endLine: this.findEndLine(lineNumber),
        signature: match[0],
        metadata: {
          exported: match[0].includes('export'),
          abstract: match[0].includes('abstract'),
          extends: match[2],
          implements: match[3]?.split(',').map(i => i.trim())
        }
      });
    }

    // Extract interfaces
    const interfaceRegex = /(?:export\s+)?interface\s+(\w+)(?:\s+extends\s+([^{]+))?/g;
    while ((match = interfaceRegex.exec(content)) !== null) {
      const lineNumber = content.substring(0, match.index).split('\n').length;
      
      symbols.push({
        id: `${filePath}:interface:${match[1]}:${lineNumber}`,
        name: match[1],
        type: 'interface',
        filePath,
        startLine: lineNumber,
        endLine: this.findEndLine(lineNumber),
        signature: match[0],
        metadata: {
          exported: match[0].includes('export'),
          extends: match[2]?.split(',').map(i => i.trim())
        }
      });
    }

    // Extract methods within classes
    const methodRegex = /(?:public|private|protected)?\s*(?:static\s+)?(?:async\s+)?(\w+)\s*\(([^)]*)\)(?:\s*:\s*([^{]+))?/g;
    while ((match = methodRegex.exec(content)) !== null) {
      const lineNumber = content.substring(0, match.index).split('\n').length;
      const params = match[2] ? match[2].split(',').map(p => p.trim()) : [];
      
      // Check if this is inside a class
      const parentClass = this.findParentClass(content, match.index);
      if (parentClass) {
        symbols.push({
          id: `${filePath}:method:${parentClass}:${match[1]}:${lineNumber}`,
          name: match[1],
          type: 'method',
          filePath,
          startLine: lineNumber,
          endLine: this.findEndLine(lineNumber),
          signature: match[0],
          parameters: params,
          returnType: match[3]?.trim(),
          parentClass,
          isAsync: match[0].includes('async'),
          isStatic: match[0].includes('static'),
          visibility: this.extractVisibility(match[0]),
          metadata: {}
        });
      }
    }

    return symbols;
  }

  private extractPythonSymbols(filePath: string, content: string): CodeSymbol[] {
    const symbols: CodeSymbol[] = [];

    // Extract Python functions
    const functionRegex = /def\s+(\w+)\s*\(([^)]*)\)(?:\s*->\s*([^:]+))?:/g;
    let match;
    while ((match = functionRegex.exec(content)) !== null) {
      const lineNumber = content.substring(0, match.index).split('\n').length;
      const params = match[2] ? match[2].split(',').map(p => p.trim()) : [];
      
      symbols.push({
        id: `${filePath}:func:${match[1]}:${lineNumber}`,
        name: match[1],
        type: 'function',
        filePath,
        startLine: lineNumber,
        endLine: lineNumber + 10, // Simplified
        signature: match[0],
        parameters: params,
        returnType: match[3]?.trim(),
        metadata: {}
      });
    }

    // Extract Python classes
    const classRegex = /class\s+(\w+)(?:\(([^)]+)\))?:/g;
    while ((match = classRegex.exec(content)) !== null) {
      const lineNumber = content.substring(0, match.index).split('\n').length;
      
      symbols.push({
        id: `${filePath}:class:${match[1]}:${lineNumber}`,
        name: match[1],
        type: 'class',
        filePath,
        startLine: lineNumber,
        endLine: lineNumber + 15, // Simplified
        signature: match[0],
        metadata: {
          inherits: match[2]?.split(',').map(i => i.trim())
        }
      });
    }

    return symbols;
  }

  // Build fuzzy search engine
  private buildFuzzySearchEngine(): void {
    const symbolsArray = Array.from(this.symbols.values());

    this.fuzzySearchEngine = new Fuse(symbolsArray, {
      keys: [
        { name: 'name', weight: 0.4 },
        { name: 'signature', weight: 0.3 },
        { name: 'filePath', weight: 0.2 },
        { name: 'docstring', weight: 0.1 }
      ],
      threshold: 0.4, // Lower = more strict
      includeScore: true,
      includeMatches: true
    });
  }

  // Intelligent search functionality with fuzzy search
  public async search(query: string, options: {
    type?: string;
    filePath?: string;
    maxResults?: number;
    includeRelated?: boolean;
    useFuzzySearch?: boolean;
  } = {}): Promise<SearchResult[]> {
    const startTime = Date.now();
    const results: SearchResult[] = [];
    const maxResults = options.maxResults || 20;

    // Use fuzzy search if available and requested
    if (options.useFuzzySearch !== false && this.fuzzySearchEngine) {
      const fuzzyResults = this.fuzzySearchEngine.search(query);

      for (const result of fuzzyResults.slice(0, maxResults)) {
        const symbol = result.item;

        // Filter by file if specified
        if (options.filePath && !symbol.filePath.includes(options.filePath)) {
          continue;
        }

        // Filter by type if specified
        if (options.type && symbol.type !== options.type) {
          continue;
        }

        const relevanceScore = 1 - (result.score || 0); // Invert Fuse.js score
        const matchReasons = this.getFuzzyMatchReasons(result);

        const relatedSymbols = options.includeRelated
          ? await this.getRelatedSymbols(symbol.id, 3)
          : [];

        results.push({
          symbol,
          relevanceScore,
          matchReasons,
          relatedSymbols
        });
      }
    } else {
      // Fallback to traditional search
      const queryLower = query.toLowerCase();

      // Search by name (exact and partial matches)
      const nameMatches = this.searchByName(queryLower);

      // Search by keywords in docstrings and signatures
      const keywordMatches = this.searchByKeywords(queryLower);

      // Search by type
      const typeMatches = options.type ? this.searchByType(options.type) : [];

      // Combine and score results
      const allMatches = new Set([...nameMatches, ...keywordMatches, ...typeMatches]);

      for (const symbolId of allMatches) {
        const symbol = this.symbols.get(symbolId);
        if (!symbol) continue;

        // Filter by file if specified
        if (options.filePath && !symbol.filePath.includes(options.filePath)) {
          continue;
        }

        const relevanceScore = this.calculateRelevanceScore(symbol, query);
        const matchReasons = this.getMatchReasons(symbol, query);

        if (relevanceScore > 0.1) { // Minimum relevance threshold
          const relatedSymbols = options.includeRelated
            ? await this.getRelatedSymbols(symbolId, 3)
            : [];

          results.push({
            symbol,
            relevanceScore,
            matchReasons,
            relatedSymbols
          });
        }
      }

      // Sort by relevance and limit results
      results.sort((a, b) => b.relevanceScore - a.relevanceScore);
    }

    // Update stats
    const queryTime = Date.now() - startTime;
    this.stats.averageQueryTime = (this.stats.averageQueryTime + queryTime) / 2;

    return results.slice(0, maxResults);
  }

  private getFuzzyMatchReasons(result: any): string[] {
    const reasons: string[] = [];

    if (result.matches) {
      for (const match of result.matches) {
        switch (match.key) {
          case 'name':
            reasons.push('Name match');
            break;
          case 'signature':
            reasons.push('Signature match');
            break;
          case 'filePath':
            reasons.push('File path match');
            break;
          case 'docstring':
            reasons.push('Documentation match');
            break;
        }
      }
    }

    return reasons.length > 0 ? reasons : ['Fuzzy match'];
  }

  private searchByName(query: string): string[] {
    const results: string[] = [];

    // Exact match
    if (this.nameIndex.has(query)) {
      results.push(...this.nameIndex.get(query)!);
    }

    // Partial matches
    for (const [name, symbolIds] of this.nameIndex) {
      if (name.includes(query) && name !== query) {
        results.push(...symbolIds);
      }
    }

    return results;
  }

  private searchByKeywords(query: string): string[] {
    const results: string[] = [];
    const queryWords = query.split(/\s+/).filter(w => w.length > 2);

    for (const word of queryWords) {
      if (this.keywordIndex.has(word)) {
        results.push(...this.keywordIndex.get(word)!);
      }
    }

    return results;
  }

  private searchByType(type: string): string[] {
    return this.typeIndex.get(type) || [];
  }

  private calculateRelevanceScore(symbol: CodeSymbol, query: string): number {
    let score = 0;
    const queryLower = query.toLowerCase();
    const nameLower = symbol.name.toLowerCase();

    // Exact name match
    if (nameLower === queryLower) {
      score += 1.0;
    }
    // Name starts with query
    else if (nameLower.startsWith(queryLower)) {
      score += 0.8;
    }
    // Name contains query
    else if (nameLower.includes(queryLower)) {
      score += 0.6;
    }

    // Signature contains query
    if (symbol.signature?.toLowerCase().includes(queryLower)) {
      score += 0.4;
    }

    // Docstring contains query
    if (symbol.docstring?.toLowerCase().includes(queryLower)) {
      score += 0.3;
    }

    // File path relevance
    if (symbol.filePath.toLowerCase().includes(queryLower)) {
      score += 0.2;
    }

    // Type preference (functions and classes are more important)
    if (['function', 'class', 'interface'].includes(symbol.type)) {
      score += 0.1;
    }

    return Math.min(score, 1.0);
  }

  private getMatchReasons(symbol: CodeSymbol, query: string): string[] {
    const reasons: string[] = [];
    const queryLower = query.toLowerCase();
    const nameLower = symbol.name.toLowerCase();

    if (nameLower === queryLower) {
      reasons.push('Exact name match');
    } else if (nameLower.startsWith(queryLower)) {
      reasons.push('Name starts with query');
    } else if (nameLower.includes(queryLower)) {
      reasons.push('Name contains query');
    }

    if (symbol.signature?.toLowerCase().includes(queryLower)) {
      reasons.push('Found in signature');
    }

    if (symbol.docstring?.toLowerCase().includes(queryLower)) {
      reasons.push('Found in documentation');
    }

    if (symbol.filePath.toLowerCase().includes(queryLower)) {
      reasons.push('Found in file path');
    }

    return reasons;
  }

  private async getRelatedSymbols(symbolId: string, maxRelated: number = 5): Promise<CodeSymbol[]> {
    const related: CodeSymbol[] = [];

    // Find symbols that have relationships with this symbol
    for (const rel of this.relationships) {
      if (rel.from === symbolId || rel.to === symbolId) {
        const relatedId = rel.from === symbolId ? rel.to : rel.from;
        const relatedSymbol = this.symbols.get(relatedId);
        if (relatedSymbol && related.length < maxRelated) {
          related.push(relatedSymbol);
        }
      }
    }

    return related;
  }

  // Helper methods
  private isCodeFile(extension: string): boolean {
    const codeExtensions = ['.ts', '.js', '.tsx', '.jsx', '.py', '.java', '.cpp', '.c', '.cs', '.php', '.rb', '.go', '.rs'];
    return codeExtensions.includes(extension.toLowerCase());
  }

  // Simplified helper methods
  private findEndLine(startLine: number): number {
    return startLine + 10; // Simplified estimation
  }

  private getIndentation(line: string): number {
    return line.length - line.trimStart().length;
  }

  private findParentClass(content: string, position: number): string | null {
    const beforePosition = content.substring(0, position);
    const classMatch = beforePosition.match(/class\s+(\w+)[^{]*{[^}]*$/);
    return classMatch ? classMatch[1] : null;
  }

  private extractVisibility(signature: string): 'public' | 'private' | 'protected' {
    if (signature.includes('private')) return 'private';
    if (signature.includes('protected')) return 'protected';
    return 'public';
  }

  private addToNameIndex(name: string, symbolId: string): void {
    if (!this.nameIndex.has(name)) {
      this.nameIndex.set(name, []);
    }
    this.nameIndex.get(name)!.push(symbolId);
  }

  private addToTypeIndex(type: string, symbolId: string): void {
    if (!this.typeIndex.has(type)) {
      this.typeIndex.set(type, []);
    }
    this.typeIndex.get(type)!.push(symbolId);
  }

  private addKeywordsToIndex(symbol: CodeSymbol, symbolId: string): void {
    const keywords = [
      ...symbol.name.toLowerCase().split(/[_-]/),
      ...(symbol.signature?.toLowerCase().split(/\W+/) || []),
      ...(symbol.docstring?.toLowerCase().split(/\W+/) || [])
    ].filter(word => word.length > 2);

    for (const keyword of keywords) {
      if (!this.keywordIndex.has(keyword)) {
        this.keywordIndex.set(keyword, []);
      }
      this.keywordIndex.get(keyword)!.push(symbolId);
    }
  }

  private async buildRelationships(): Promise<void> {
    // Build relationships between symbols
    for (const [symbolId, symbol] of this.symbols) {
      // Find function calls, imports, etc.
      if (symbol.type === 'function' || symbol.type === 'method') {
        await this.findFunctionRelationships(symbol);
      }
    }
  }

  private async findFunctionRelationships(symbol: CodeSymbol): Promise<void> {
    try {
      const content = await this.codebaseAnalyzer.getFileContent(symbol.filePath);
      const functionContent = this.extractFunctionContent(content, symbol);

      // Find function calls within this function
      const callRegex = /(\w+)\s*\(/g;
      let match;
      while ((match = callRegex.exec(functionContent)) !== null) {
        const calledFunction = match[1];

        // Find the called function in our index
        const calledSymbols = this.nameIndex.get(calledFunction.toLowerCase()) || [];
        for (const calledSymbolId of calledSymbols) {
          const calledSymbol = this.symbols.get(calledSymbolId);
          if (calledSymbol && calledSymbol.type === 'function') {
            this.relationships.push({
              from: symbol.id,
              to: calledSymbolId,
              type: 'calls',
              weight: 0.8,
              context: `${symbol.name} calls ${calledFunction}`
            });
          }
        }
      }
    } catch (error) {
      this.logger.debug(`Failed to build relationships for ${symbol.name}: ${error}`);
    }
  }

  private extractFunctionContent(content: string, symbol: CodeSymbol): string {
    const lines = content.split('\n');
    const startIndex = Math.max(0, symbol.startLine - 1);
    const endIndex = Math.min(lines.length, symbol.endLine || symbol.startLine + 20);
    return lines.slice(startIndex, endIndex).join('\n');
  }

  private clearIndex(): void {
    this.symbols.clear();
    this.relationships = [];
    this.fileIndex.clear();
    this.nameIndex.clear();
    this.typeIndex.clear();
    this.keywordIndex.clear();
  }

  private updateStats(): void {
    this.stats = {
      totalSymbols: this.symbols.size,
      totalRelationships: this.relationships.length,
      filesCovered: this.fileIndex.size,
      lastIndexed: new Date(),
      indexSize: this.calculateIndexSize(),
      averageQueryTime: this.stats.averageQueryTime
    };
  }

  private calculateIndexSize(): number {
    return JSON.stringify({
      symbols: Array.from(this.symbols.values()),
      relationships: this.relationships
    }).length;
  }

  public getStats(): IndexStats {
    return { ...this.stats };
  }

  public getSymbolById(id: string): CodeSymbol | undefined {
    return this.symbols.get(id);
  }

  public getSymbolsByFile(filePath: string): CodeSymbol[] {
    const symbolIds = this.fileIndex.get(filePath) || [];
    return symbolIds.map(id => this.symbols.get(id)!).filter(Boolean);
  }

  public getSymbolsByType(type: string): CodeSymbol[] {
    const symbolIds = this.typeIndex.get(type) || [];
    return symbolIds.map(id => this.symbols.get(id)!).filter(Boolean);
  }
}
