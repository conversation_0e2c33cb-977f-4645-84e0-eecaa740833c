import { Command } from 'commander';
import inquirer from 'inquirer';
import chalk from 'chalk';
import boxen from 'boxen';
import ora from 'ora';
import { ConfigManager } from '../config/ConfigManager';
import { BackendApiService } from '../services/BackendApiService';

export class EnhancedChatCommand {
    private config: ConfigManager;
    private api: BackendApiService;
    private currentSession: any = null;

    constructor() {
        this.config = ConfigManager.getInstance();
        this.api = new BackendApiService();
    }

    public register(program: Command): void {
        program
            .command('chat-enhanced')
            .alias('ce')
            .description('Enhanced chat with <PERSON><PERSON> backend integration')
            .option('-s, --session <id>', 'Resume existing session')
            .option('-p, --provider <provider>', 'AI provider to use')
            .option('-m, --model <model>', 'AI model to use')
            .action(async (options) => {
                await this.execute(options);
            });
    }

    private async execute(options: any): Promise<void> {
        try {
            // Check backend health
            const spinner = ora('Checking backend connection...').start();
            const isHealthy = await this.api.healthCheck();
            
            if (!isHealthy) {
                spinner.fail('Backend is not available. Please start the Laravel backend.');
                console.log(chalk.yellow('\nTo start the backend:'));
                console.log(chalk.cyan('cd backend && php artisan serve'));
                return;
            }
            spinner.succeed('Backend connected successfully');

            // Authenticate if needed
            if (!this.api.isAuthenticated()) {
                await this.authenticate();
            }

            // Get or create session
            await this.setupSession(options.session);

            // Get available providers
            const providers = await this.api.getProviders();
            
            // Select provider and model if not specified
            const selectedProvider = options.provider || await this.selectProvider(providers);
            const selectedModel = options.model || await this.selectModel(providers[selectedProvider]);

            // Display session info
            this.displaySessionInfo(selectedProvider, selectedModel);

            // Start chat loop
            await this.chatLoop(selectedProvider, selectedModel);

        } catch (error) {
            console.error(chalk.red('Error:'), error.message);
        }
    }

    private async authenticate(): Promise<void> {
        console.log(chalk.blue('\n🔐 Authentication Required\n'));

        const authChoice = await inquirer.prompt([
            {
                type: 'list',
                name: 'action',
                message: 'Choose authentication method:',
                choices: [
                    { name: 'Login with existing account', value: 'login' },
                    { name: 'Register new account', value: 'register' },
                ],
            },
        ]);

        if (authChoice.action === 'register') {
            await this.register();
        } else {
            await this.login();
        }
    }

    private async register(): Promise<void> {
        const answers = await inquirer.prompt([
            {
                type: 'input',
                name: 'name',
                message: 'Enter your name:',
                validate: (input) => input.trim().length > 0 || 'Name is required',
            },
            {
                type: 'input',
                name: 'email',
                message: 'Enter your email:',
                validate: (input) => {
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    return emailRegex.test(input) || 'Please enter a valid email';
                },
            },
            {
                type: 'password',
                name: 'password',
                message: 'Enter your password:',
                validate: (input) => input.length >= 8 || 'Password must be at least 8 characters',
            },
            {
                type: 'password',
                name: 'passwordConfirmation',
                message: 'Confirm your password:',
                validate: (input, answers) => input === answers.password || 'Passwords do not match',
            },
        ]);

        const spinner = ora('Creating account...').start();
        try {
            const response = await this.api.register(
                answers.name,
                answers.email,
                answers.password,
                answers.passwordConfirmation
            );
            spinner.succeed('Account created successfully!');
            console.log(chalk.green(`Welcome, ${response.user.name}!`));
        } catch (error) {
            spinner.fail('Registration failed');
            throw error;
        }
    }

    private async login(): Promise<void> {
        const answers = await inquirer.prompt([
            {
                type: 'input',
                name: 'email',
                message: 'Enter your email:',
            },
            {
                type: 'password',
                name: 'password',
                message: 'Enter your password:',
            },
        ]);

        const spinner = ora('Logging in...').start();
        try {
            const response = await this.api.login(answers.email, answers.password);
            spinner.succeed('Login successful!');
            console.log(chalk.green(`Welcome back, ${response.user.name}!`));
        } catch (error) {
            spinner.fail('Login failed');
            throw error;
        }
    }

    private async setupSession(sessionId?: string): Promise<void> {
        if (sessionId) {
            // Resume existing session
            try {
                this.currentSession = await this.api.getSession(parseInt(sessionId));
                console.log(chalk.green(`\n📝 Resumed session: ${this.currentSession.title}`));
            } catch (error) {
                console.log(chalk.yellow('Session not found, creating new session...'));
                this.currentSession = await this.api.createSession('New Enhanced Session');
            }
        } else {
            // Create new session
            this.currentSession = await this.api.createSession('New Enhanced Session');
            console.log(chalk.green(`\n📝 Created new session: ${this.currentSession.title}`));
        }
    }

    private async selectProvider(providers: Record<string, any>): Promise<string> {
        const availableProviders = Object.entries(providers)
            .filter(([_, provider]) => provider.available)
            .map(([name, provider]) => ({
                name: `${provider.name} (${provider.models.length} models)`,
                value: name,
            }));

        if (availableProviders.length === 0) {
            throw new Error('No AI providers are available');
        }

        if (availableProviders.length === 1) {
            return availableProviders[0].value;
        }

        const answer = await inquirer.prompt([
            {
                type: 'list',
                name: 'provider',
                message: 'Select AI provider:',
                choices: availableProviders,
            },
        ]);

        return answer.provider;
    }

    private async selectModel(provider: any): Promise<string> {
        if (provider.models.length === 1) {
            return provider.models[0];
        }

        const answer = await inquirer.prompt([
            {
                type: 'list',
                name: 'model',
                message: 'Select model:',
                choices: provider.models,
            },
        ]);

        return answer.model;
    }

    private displaySessionInfo(provider: string, model: string): void {
        const info = boxen(
            `${chalk.bold('Enhanced GOC Agent')}\n\n` +
            `${chalk.blue('Session:')} ${this.currentSession.title}\n` +
            `${chalk.blue('Provider:')} ${provider}\n` +
            `${chalk.blue('Model:')} ${model}\n` +
            `${chalk.blue('Backend:')} Connected\n\n` +
            `${chalk.gray('Type "exit" to quit, "help" for commands')}`,
            {
                padding: 1,
                margin: 1,
                borderStyle: 'round',
                borderColor: 'blue',
            }
        );
        console.log(info);
    }

    private async chatLoop(provider: string, model: string): Promise<void> {
        while (true) {
            const answer = await inquirer.prompt([
                {
                    type: 'input',
                    name: 'message',
                    message: chalk.cyan('You:'),
                    validate: (input) => input.trim().length > 0 || 'Please enter a message',
                },
            ]);

            const message = answer.message.trim();

            if (message.toLowerCase() === 'exit') {
                console.log(chalk.yellow('Goodbye! 👋'));
                break;
            }

            if (message.toLowerCase() === 'help') {
                this.showHelp();
                continue;
            }

            if (message.startsWith('/')) {
                await this.handleCommand(message);
                continue;
            }

            await this.sendMessage(message, provider, model);
        }
    }

    private async sendMessage(message: string, provider: string, model: string): Promise<void> {
        const spinner = ora('Thinking...').start();
        
        try {
            const response = await this.api.chat(this.currentSession.id, message, provider, model);
            spinner.stop();

            console.log(chalk.green('\nAssistant:'));
            console.log(response.assistant_message.content);
            
            // Show token usage if available
            if (response.assistant_message.token_usage) {
                const usage = response.assistant_message.token_usage;
                console.log(chalk.gray(`\n📊 Tokens: ${usage.total_tokens} (${usage.prompt_tokens} + ${usage.completion_tokens})`));
            }
            
            console.log(''); // Empty line for spacing
        } catch (error) {
            spinner.fail('Failed to get response');
            console.error(chalk.red('Error:'), error.message);
        }
    }

    private showHelp(): void {
        const help = `
${chalk.bold('Available Commands:')}

${chalk.cyan('/tools')}          - List available tools
${chalk.cyan('/tool <name>')}    - Execute a specific tool
${chalk.cyan('/status')}         - Show agent status
${chalk.cyan('/providers')}      - List AI providers
${chalk.cyan('/sessions')}       - List your sessions
${chalk.cyan('/switch <id>')}    - Switch to another session
${chalk.cyan('/index <path>')}   - Index project for context search
${chalk.cyan('/search <query>')} - Search code using context engine
${chalk.cyan('help')}            - Show this help
${chalk.cyan('exit')}            - Exit the chat

${chalk.gray('You can also just type your message normally to chat with the AI.')}
        `;
        console.log(help);
    }

    private async handleCommand(command: string): Promise<void> {
        const [cmd, ...args] = command.slice(1).split(' ');
        
        try {
            switch (cmd) {
                case 'status':
                    await this.showStatus();
                    break;
                case 'providers':
                    await this.showProviders();
                    break;
                case 'sessions':
                    await this.showSessions();
                    break;
                case 'index':
                    await this.indexProject(args.join(' '));
                    break;
                case 'search':
                    await this.searchCode(args.join(' '));
                    break;
                case 'tool':
                    await this.executeTool(args[0], args.slice(1));
                    break;
                case 'tools':
                    await this.listTools();
                    break;
                default:
                    console.log(chalk.red(`Unknown command: ${cmd}`));
                    this.showHelp();
            }
        } catch (error) {
            console.error(chalk.red('Command error:'), error.message);
        }
    }

    private async showStatus(): Promise<void> {
        const spinner = ora('Getting status...').start();
        try {
            const status = await this.api.getStatus();
            spinner.succeed('Status retrieved');
            
            console.log(chalk.blue('\n📊 Agent Status:'));
            console.log(`Sessions: ${status.statistics.total_sessions}`);
            console.log(`Messages: ${status.statistics.total_messages}`);
            console.log(`API calls today: ${status.statistics.api_usage_today}`);
            console.log(`Tokens used today: ${status.statistics.tokens_used_today}`);
            console.log(`Quota remaining: ${status.statistics.quota_remaining}`);
        } catch (error) {
            spinner.fail('Failed to get status');
            throw error;
        }
    }

    private async showProviders(): Promise<void> {
        const providers = await this.api.getProviders();
        
        console.log(chalk.blue('\n🤖 Available Providers:'));
        for (const [name, provider] of Object.entries(providers)) {
            const status = provider.available ? chalk.green('✓') : chalk.red('✗');
            console.log(`${status} ${provider.name} (${provider.models.length} models)`);
        }
    }

    private async showSessions(): Promise<void> {
        const sessions = await this.api.getSessions();
        
        console.log(chalk.blue('\n📝 Your Sessions:'));
        sessions.forEach((session: any) => {
            const current = session.id === this.currentSession.id ? chalk.green('→ ') : '  ';
            console.log(`${current}${session.id}: ${session.title} (${session.status})`);
        });
    }

    private async indexProject(projectPath: string): Promise<void> {
        if (!projectPath) {
            console.log(chalk.red('Please provide a project path'));
            return;
        }

        const spinner = ora('Indexing project...').start();
        try {
            const result = await this.api.indexProject(projectPath);
            spinner.succeed('Project indexed successfully');
            
            console.log(chalk.green('\n📚 Indexing Results:'));
            console.log(`Files processed: ${result.data.files_processed}`);
            console.log(`Files indexed: ${result.data.files_indexed}`);
            console.log(`Embeddings created: ${result.data.embeddings_created}`);
        } catch (error) {
            spinner.fail('Failed to index project');
            throw error;
        }
    }

    private async searchCode(query: string): Promise<void> {
        if (!query) {
            console.log(chalk.red('Please provide a search query'));
            return;
        }

        const spinner = ora('Searching code...').start();
        try {
            const result = await this.api.searchCode(query);
            spinner.succeed('Search completed');
            
            console.log(chalk.green(`\n🔍 Search Results for "${query}":`));
            result.data.results.forEach((file: any, index: number) => {
                console.log(`${index + 1}. ${file.file_path} (${file.language})`);
                if (file.metadata.functions.length > 0) {
                    console.log(`   Functions: ${file.metadata.functions.join(', ')}`);
                }
            });
        } catch (error) {
            spinner.fail('Search failed');
            throw error;
        }
    }

    private async executeTool(toolName: string, args: string[]): Promise<void> {
        if (!toolName) {
            console.log(chalk.red('Please specify a tool name'));
            return;
        }

        const spinner = ora(`Executing ${toolName}...`).start();
        try {
            // Convert args to parameters object (simplified)
            const parameters = args.length > 0 ? { args } : {};
            const result = await this.api.executeTool(toolName, parameters);
            
            spinner.succeed(`Tool ${toolName} executed`);
            console.log(chalk.green('\n🔧 Tool Result:'));
            console.log(JSON.stringify(result.result, null, 2));
        } catch (error) {
            spinner.fail(`Tool ${toolName} failed`);
            throw error;
        }
    }

    private async listTools(): Promise<void> {
        console.log(chalk.blue('\n🔧 Available Tools:'));
        const tools = [
            'view - View file or directory contents',
            'str-replace-editor - Edit files with precise replacements',
            'save-file - Create new files',
            'remove-files - Delete files safely',
            'web-search - Search the web',
            'web-fetch - Fetch web page content',
        ];
        
        tools.forEach(tool => console.log(`  • ${tool}`));
    }
}
