import { Logger } from '../utils/Logger';
import { MemoryManager } from '../agent/MemoryManager';
import * as fs from 'fs';
import * as path from 'path';

export interface TrainingSession {
  id: string;
  type: 'quick' | 'deep' | 'comprehensive';
  startTime: Date;
  endTime: Date;
  duration: number;
  success: boolean;
  aspects: string[];
  intensity: string;
  metrics: {
    memoryUsage: number;
    cpuTime: number;
    networkRequests: number;
    errorsEncountered: number;
    improvementsDetected: number;
  };
}

export interface SystemMetrics {
  timestamp: Date;
  memoryUsage: number;
  cpuUsage?: number;
  networkActivity?: number;
  activeTrainingSessions: number;
}

export interface PerformanceMetrics {
  successRate: number;
  averageDuration: number;
  improvementTrend: number;
  efficiencyScore: number;
  learningVelocity: number;
}

export class TrainingMetrics {
  private logger: Logger;
  private memoryManager: MemoryManager;
  private metricsPath: string;
  private trainingSessions: TrainingSession[] = [];
  private systemMetrics: SystemMetrics[] = [];
  private performanceHistory: PerformanceMetrics[] = [];

  constructor(logger: Logger, memoryManager: MemoryManager) {
    this.logger = logger;
    this.memoryManager = memoryManager;
    this.metricsPath = path.join(process.cwd(), '.goc', 'training-metrics.json');
  }

  public async initialize(): Promise<void> {
    this.logger.debug('Initializing training metrics...');
    
    await this.loadMetrics();
    this.startMetricsCollection();
    
    this.logger.debug('✅ Training metrics initialized');
  }

  private async loadMetrics(): Promise<void> {
    try {
      if (fs.existsSync(this.metricsPath)) {
        const data = JSON.parse(fs.readFileSync(this.metricsPath, 'utf8'));
        
        this.trainingSessions = data.trainingSessions?.map((session: any) => ({
          ...session,
          startTime: new Date(session.startTime),
          endTime: new Date(session.endTime)
        })) || [];
        
        this.systemMetrics = data.systemMetrics?.map((metric: any) => ({
          ...metric,
          timestamp: new Date(metric.timestamp)
        })) || [];
        
        this.performanceHistory = data.performanceHistory || [];
        
        this.logger.debug(`Loaded ${this.trainingSessions.length} training sessions and ${this.systemMetrics.length} system metrics`);
      }
    } catch (error) {
      this.logger.warn(`Failed to load training metrics: ${error}`);
    }
  }

  private async saveMetrics(): Promise<void> {
    try {
      const dir = path.dirname(this.metricsPath);
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }

      const data = {
        trainingSessions: this.trainingSessions,
        systemMetrics: this.systemMetrics,
        performanceHistory: this.performanceHistory,
        lastUpdated: new Date()
      };

      fs.writeFileSync(this.metricsPath, JSON.stringify(data, null, 2));
    } catch (error) {
      this.logger.error(`Failed to save training metrics: ${error}`);
    }
  }

  public recordTrainingSession(type: string, success: boolean, duration?: number, aspects?: string[]): void {
    const session: TrainingSession = {
      id: this.generateSessionId(),
      type: type as 'quick' | 'deep' | 'comprehensive',
      startTime: new Date(Date.now() - (duration || 0)),
      endTime: new Date(),
      duration: duration || 0,
      success,
      aspects: aspects || [],
      intensity: 'unknown',
      metrics: {
        memoryUsage: this.getCurrentMemoryUsage(),
        cpuTime: duration || 0,
        networkRequests: 0,
        errorsEncountered: success ? 0 : 1,
        improvementsDetected: success ? 1 : 0
      }
    };

    this.trainingSessions.push(session);
    
    // Keep only recent sessions (last 1000)
    if (this.trainingSessions.length > 1000) {
      this.trainingSessions = this.trainingSessions.slice(-1000);
    }

    this.updatePerformanceMetrics();
    this.saveMetrics();

    this.logger.debug(`Recorded training session: ${type} (${success ? 'success' : 'failure'})`);
  }

  public recordSystemMetrics(metrics: Partial<SystemMetrics>): void {
    const systemMetric: SystemMetrics = {
      timestamp: new Date(),
      memoryUsage: this.getCurrentMemoryUsage(),
      activeTrainingSessions: 0,
      ...metrics
    };

    this.systemMetrics.push(systemMetric);
    
    // Keep only recent metrics (last 2000 entries)
    if (this.systemMetrics.length > 2000) {
      this.systemMetrics = this.systemMetrics.slice(-2000);
    }

    // Save periodically
    if (this.systemMetrics.length % 100 === 0) {
      this.saveMetrics();
    }
  }

  private updatePerformanceMetrics(): void {
    const recentSessions = this.trainingSessions.slice(-50);
    if (recentSessions.length === 0) return;

    const successRate = recentSessions.filter(s => s.success).length / recentSessions.length;
    const averageDuration = recentSessions.reduce((sum, s) => sum + s.duration, 0) / recentSessions.length;
    
    // Calculate improvement trend (comparing recent vs older sessions)
    const olderSessions = this.trainingSessions.slice(-100, -50);
    const olderSuccessRate = olderSessions.length > 0 
      ? olderSessions.filter(s => s.success).length / olderSessions.length 
      : successRate;
    
    const improvementTrend = successRate - olderSuccessRate;
    
    // Calculate efficiency score (success rate / average duration)
    const efficiencyScore = averageDuration > 0 ? successRate / (averageDuration / 60000) : 0;
    
    // Calculate learning velocity (improvements per time unit)
    const totalImprovements = recentSessions.reduce((sum, s) => sum + s.metrics.improvementsDetected, 0);
    const totalTime = recentSessions.reduce((sum, s) => sum + s.duration, 0);
    const learningVelocity = totalTime > 0 ? (totalImprovements / totalTime) * 3600000 : 0; // per hour

    const performanceMetric: PerformanceMetrics = {
      successRate,
      averageDuration,
      improvementTrend,
      efficiencyScore,
      learningVelocity
    };

    this.performanceHistory.push(performanceMetric);
    
    // Keep only recent performance history
    if (this.performanceHistory.length > 100) {
      this.performanceHistory = this.performanceHistory.slice(-100);
    }
  }

  private startMetricsCollection(): void {
    // Collect system metrics every 5 minutes
    setInterval(() => {
      this.recordSystemMetrics({
        timestamp: new Date(),
        memoryUsage: this.getCurrentMemoryUsage()
      });
    }, 5 * 60 * 1000);
  }

  private getCurrentMemoryUsage(): number {
    const memoryUsage = process.memoryUsage();
    return Math.round(memoryUsage.heapUsed / 1024 / 1024); // MB
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  public getOverallMetrics(): any {
    const recentSessions = this.trainingSessions.slice(-100);
    const recentSystemMetrics = this.systemMetrics.slice(-100);
    const latestPerformance = this.performanceHistory[this.performanceHistory.length - 1];

    return {
      totalSessions: this.trainingSessions.length,
      recentSuccessRate: recentSessions.length > 0 
        ? Math.round((recentSessions.filter(s => s.success).length / recentSessions.length) * 100)
        : 0,
      averageSessionDuration: recentSessions.length > 0
        ? Math.round(recentSessions.reduce((sum, s) => sum + s.duration, 0) / recentSessions.length / 1000)
        : 0,
      currentMemoryUsage: this.getCurrentMemoryUsage(),
      performance: latestPerformance || {
        successRate: 0,
        averageDuration: 0,
        improvementTrend: 0,
        efficiencyScore: 0,
        learningVelocity: 0
      },
      trends: this.calculateTrends()
    };
  }

  private calculateTrends(): any {
    if (this.performanceHistory.length < 10) {
      return { insufficient_data: true };
    }

    const recent = this.performanceHistory.slice(-10);
    const older = this.performanceHistory.slice(-20, -10);

    const recentAvg = {
      successRate: recent.reduce((sum, p) => sum + p.successRate, 0) / recent.length,
      efficiency: recent.reduce((sum, p) => sum + p.efficiencyScore, 0) / recent.length,
      learningVelocity: recent.reduce((sum, p) => sum + p.learningVelocity, 0) / recent.length
    };

    const olderAvg = {
      successRate: older.reduce((sum, p) => sum + p.successRate, 0) / older.length,
      efficiency: older.reduce((sum, p) => sum + p.efficiencyScore, 0) / older.length,
      learningVelocity: older.reduce((sum, p) => sum + p.learningVelocity, 0) / older.length
    };

    return {
      successRateTrend: recentAvg.successRate - olderAvg.successRate,
      efficiencyTrend: recentAvg.efficiency - olderAvg.efficiency,
      learningVelocityTrend: recentAvg.learningVelocity - olderAvg.learningVelocity,
      overallTrend: (recentAvg.successRate - olderAvg.successRate) + 
                   (recentAvg.efficiency - olderAvg.efficiency) + 
                   (recentAvg.learningVelocity - olderAvg.learningVelocity)
    };
  }

  public getDetailedReport(): any {
    return {
      summary: this.getOverallMetrics(),
      recentSessions: this.trainingSessions.slice(-20).map(session => ({
        id: session.id,
        type: session.type,
        duration: Math.round(session.duration / 1000),
        success: session.success,
        aspects: session.aspects,
        timestamp: session.endTime
      })),
      systemHealth: {
        currentMemory: this.getCurrentMemoryUsage(),
        averageMemory: this.systemMetrics.length > 0
          ? Math.round(this.systemMetrics.slice(-100).reduce((sum, m) => sum + m.memoryUsage, 0) / Math.min(100, this.systemMetrics.length))
          : 0,
        metricsCollected: this.systemMetrics.length
      },
      recommendations: this.generateRecommendations()
    };
  }

  private generateRecommendations(): string[] {
    const recommendations: string[] = [];
    const latestPerformance = this.performanceHistory[this.performanceHistory.length - 1];
    
    if (!latestPerformance) {
      recommendations.push('Insufficient data for recommendations. Continue training to gather metrics.');
      return recommendations;
    }

    if (latestPerformance.successRate < 0.7) {
      recommendations.push('Success rate is below 70%. Consider reducing training intensity or checking system resources.');
    }

    if (latestPerformance.improvementTrend < 0) {
      recommendations.push('Performance trend is declining. Review recent changes and consider adjusting training parameters.');
    }

    if (latestPerformance.efficiencyScore < 0.5) {
      recommendations.push('Training efficiency is low. Consider optimizing training duration or frequency.');
    }

    if (this.getCurrentMemoryUsage() > 500) {
      recommendations.push('High memory usage detected. Consider reducing training intensity or restarting the daemon.');
    }

    if (recommendations.length === 0) {
      recommendations.push('Training performance is optimal. Continue current configuration.');
    }

    return recommendations;
  }
}
