{"version": 3, "file": "index.esm.js", "sources": ["../src/index.js"], "sourcesContent": ["const candidateSelectors = [\n  'input',\n  'select',\n  'textarea',\n  'a[href]',\n  'button',\n  '[tabindex]:not(slot)',\n  'audio[controls]',\n  'video[controls]',\n  '[contenteditable]:not([contenteditable=\"false\"])',\n  'details>summary:first-of-type',\n  'details',\n];\nconst candidateSelector = /* #__PURE__ */ candidateSelectors.join(',');\n\nconst NoElement = typeof Element === 'undefined';\n\nconst matches = NoElement\n  ? function () {}\n  : Element.prototype.matches ||\n    Element.prototype.msMatchesSelector ||\n    Element.prototype.webkitMatchesSelector;\n\nconst getRootNode =\n  !NoElement && Element.prototype.getRootNode\n    ? (element) => element.getRootNode()\n    : (element) => element.ownerDocument;\n\n/**\n * @param {Element} el container to check in\n * @param {boolean} includeContainer add container to check\n * @param {(node: Element) => boolean} filter filter candidates\n * @returns {Element[]}\n */\nconst getCandidates = function (el, includeContainer, filter) {\n  let candidates = Array.prototype.slice.apply(\n    el.querySelectorAll(candidateSelector)\n  );\n  if (includeContainer && matches.call(el, candidateSelector)) {\n    candidates.unshift(el);\n  }\n  candidates = candidates.filter(filter);\n  return candidates;\n};\n\n/**\n * @callback GetShadowRoot\n * @param {Element} element to check for shadow root\n * @returns {ShadowRoot|boolean} ShadowRoot if available or boolean indicating if a shadowRoot is attached but not available.\n */\n\n/**\n * @callback ShadowRootFilter\n * @param {Element} shadowHostNode the element which contains shadow content\n * @returns {boolean} true if a shadow root could potentially contain valid candidates.\n */\n\n/**\n * @typedef {Object} CandidatesScope\n * @property {Element} scope contains inner candidates\n * @property {Element[]} candidates\n */\n\n/**\n * @typedef {Object} IterativeOptions\n * @property {GetShadowRoot|boolean} getShadowRoot true if shadow support is enabled; falsy if not;\n *  if a function, implies shadow support is enabled and either returns the shadow root of an element\n *  or a boolean stating if it has an undisclosed shadow root\n * @property {(node: Element) => boolean} filter filter candidates\n * @property {boolean} flatten if true then result will flatten any CandidatesScope into the returned list\n * @property {ShadowRootFilter} shadowRootFilter filter shadow roots;\n */\n\n/**\n * @param {Element[]} elements list of element containers to match candidates from\n * @param {boolean} includeContainer add container list to check\n * @param {IterativeOptions} options\n * @returns {Array.<Element|CandidatesScope>}\n */\nconst getCandidatesIteratively = function (\n  elements,\n  includeContainer,\n  options\n) {\n  const candidates = [];\n  const elementsToCheck = Array.from(elements);\n  while (elementsToCheck.length) {\n    const element = elementsToCheck.shift();\n    if (element.tagName === 'SLOT') {\n      // add shadow dom slot scope (slot itself cannot be focusable)\n      const assigned = element.assignedElements();\n      const content = assigned.length ? assigned : element.children;\n      const nestedCandidates = getCandidatesIteratively(content, true, options);\n      if (options.flatten) {\n        candidates.push(...nestedCandidates);\n      } else {\n        candidates.push({\n          scope: element,\n          candidates: nestedCandidates,\n        });\n      }\n    } else {\n      // check candidate element\n      const validCandidate = matches.call(element, candidateSelector);\n      if (\n        validCandidate &&\n        options.filter(element) &&\n        (includeContainer || !elements.includes(element))\n      ) {\n        candidates.push(element);\n      }\n\n      // iterate over shadow content if possible\n      const shadowRoot =\n        element.shadowRoot ||\n        // check for an undisclosed shadow\n        (typeof options.getShadowRoot === 'function' &&\n          options.getShadowRoot(element));\n\n      const validShadowRoot =\n        !options.shadowRootFilter || options.shadowRootFilter(element);\n\n      if (shadowRoot && validShadowRoot) {\n        // add shadow dom scope IIF a shadow root node was given; otherwise, an undisclosed\n        //  shadow exists, so look at light dom children as fallback BUT create a scope for any\n        //  child candidates found because they're likely slotted elements (elements that are\n        //  children of the web component element (which has the shadow), in the light dom, but\n        //  slotted somewhere _inside_ the undisclosed shadow) -- the scope is created below,\n        //  _after_ we return from this recursive call\n        const nestedCandidates = getCandidatesIteratively(\n          shadowRoot === true ? element.children : shadowRoot.children,\n          true,\n          options\n        );\n\n        if (options.flatten) {\n          candidates.push(...nestedCandidates);\n        } else {\n          candidates.push({\n            scope: element,\n            candidates: nestedCandidates,\n          });\n        }\n      } else {\n        // there's not shadow so just dig into the element's (light dom) children\n        //  __without__ giving the element special scope treatment\n        elementsToCheck.unshift(...element.children);\n      }\n    }\n  }\n  return candidates;\n};\n\nconst getTabindex = function (node, isScope) {\n  if (node.tabIndex < 0) {\n    // in Chrome, <details/>, <audio controls/> and <video controls/> elements get a default\n    // `tabIndex` of -1 when the 'tabindex' attribute isn't specified in the DOM,\n    // yet they are still part of the regular tab order; in FF, they get a default\n    // `tabIndex` of 0; since Chrome still puts those elements in the regular tab\n    // order, consider their tab index to be 0.\n    // Also browsers do not return `tabIndex` correctly for contentEditable nodes;\n    // so if they don't have a tabindex attribute specifically set, assume it's 0.\n    //\n    // isScope is positive for custom element with shadow root or slot that by default\n    // have tabIndex -1, but need to be sorted by document order in order for their\n    // content to be inserted in the correct position\n    if (\n      (isScope ||\n        /^(AUDIO|VIDEO|DETAILS)$/.test(node.tagName) ||\n        node.isContentEditable) &&\n      isNaN(parseInt(node.getAttribute('tabindex'), 10))\n    ) {\n      return 0;\n    }\n  }\n\n  return node.tabIndex;\n};\n\nconst sortOrderedTabbables = function (a, b) {\n  return a.tabIndex === b.tabIndex\n    ? a.documentOrder - b.documentOrder\n    : a.tabIndex - b.tabIndex;\n};\n\nconst isInput = function (node) {\n  return node.tagName === 'INPUT';\n};\n\nconst isHiddenInput = function (node) {\n  return isInput(node) && node.type === 'hidden';\n};\n\nconst isDetailsWithSummary = function (node) {\n  const r =\n    node.tagName === 'DETAILS' &&\n    Array.prototype.slice\n      .apply(node.children)\n      .some((child) => child.tagName === 'SUMMARY');\n  return r;\n};\n\nconst getCheckedRadio = function (nodes, form) {\n  for (let i = 0; i < nodes.length; i++) {\n    if (nodes[i].checked && nodes[i].form === form) {\n      return nodes[i];\n    }\n  }\n};\n\nconst isTabbableRadio = function (node) {\n  if (!node.name) {\n    return true;\n  }\n  const radioScope = node.form || getRootNode(node);\n  const queryRadios = function (name) {\n    return radioScope.querySelectorAll(\n      'input[type=\"radio\"][name=\"' + name + '\"]'\n    );\n  };\n\n  let radioSet;\n  if (\n    typeof window !== 'undefined' &&\n    typeof window.CSS !== 'undefined' &&\n    typeof window.CSS.escape === 'function'\n  ) {\n    radioSet = queryRadios(window.CSS.escape(node.name));\n  } else {\n    try {\n      radioSet = queryRadios(node.name);\n    } catch (err) {\n      // eslint-disable-next-line no-console\n      console.error(\n        'Looks like you have a radio button with a name attribute containing invalid CSS selector characters and need the CSS.escape polyfill: %s',\n        err.message\n      );\n      return false;\n    }\n  }\n\n  const checked = getCheckedRadio(radioSet, node.form);\n  return !checked || checked === node;\n};\n\nconst isRadio = function (node) {\n  return isInput(node) && node.type === 'radio';\n};\n\nconst isNonTabbableRadio = function (node) {\n  return isRadio(node) && !isTabbableRadio(node);\n};\n\nconst isZeroArea = function (node) {\n  const { width, height } = node.getBoundingClientRect();\n  return width === 0 && height === 0;\n};\nconst isHidden = function (node, { displayCheck, getShadowRoot }) {\n  // NOTE: visibility will be `undefined` if node is detached from the document\n  //  (see notes about this further down), which means we will consider it visible\n  //  (this is legacy behavior from a very long way back)\n  // NOTE: we check this regardless of `displayCheck=\"none\"` because this is a\n  //  _visibility_ check, not a _display_ check\n  if (getComputedStyle(node).visibility === 'hidden') {\n    return true;\n  }\n\n  const isDirectSummary = matches.call(node, 'details>summary:first-of-type');\n  const nodeUnderDetails = isDirectSummary ? node.parentElement : node;\n  if (matches.call(nodeUnderDetails, 'details:not([open]) *')) {\n    return true;\n  }\n\n  // The root node is the shadow root if the node is in a shadow DOM; some document otherwise\n  //  (but NOT _the_ document; see second 'If' comment below for more).\n  // If rootNode is shadow root, it'll have a host, which is the element to which the shadow\n  //  is attached, and the one we need to check if it's in the document or not (because the\n  //  shadow, and all nodes it contains, is never considered in the document since shadows\n  //  behave like self-contained DOMs; but if the shadow's HOST, which is part of the document,\n  //  is hidden, or is not in the document itself but is detached, it will affect the shadow's\n  //  visibility, including all the nodes it contains). The host could be any normal node,\n  //  or a custom element (i.e. web component). Either way, that's the one that is considered\n  //  part of the document, not the shadow root, nor any of its children (i.e. the node being\n  //  tested).\n  // If rootNode is not a shadow root, it won't have a host, and so rootNode should be the\n  //  document (per the docs) and while it's a Document-type object, that document does not\n  //  appear to be the same as the node's `ownerDocument` for some reason, so it's safer\n  //  to ignore the rootNode at this point, and use `node.ownerDocument`. Otherwise,\n  //  using `rootNode.contains(node)` will _always_ be true we'll get false-positives when\n  //  node is actually detached.\n  const nodeRootHost = getRootNode(node).host;\n  const nodeIsAttached =\n    nodeRootHost?.ownerDocument.contains(nodeRootHost) ||\n    node.ownerDocument.contains(node);\n\n  if (!displayCheck || displayCheck === 'full') {\n    if (typeof getShadowRoot === 'function') {\n      // figure out if we should consider the node to be in an undisclosed shadow and use the\n      //  'non-zero-area' fallback\n      const originalNode = node;\n      while (node) {\n        const parentElement = node.parentElement;\n        const rootNode = getRootNode(node);\n        if (\n          parentElement &&\n          !parentElement.shadowRoot &&\n          getShadowRoot(parentElement) === true // check if there's an undisclosed shadow\n        ) {\n          // node has an undisclosed shadow which means we can only treat it as a black box, so we\n          //  fall back to a non-zero-area test\n          return isZeroArea(node);\n        } else if (node.assignedSlot) {\n          // iterate up slot\n          node = node.assignedSlot;\n        } else if (!parentElement && rootNode !== node.ownerDocument) {\n          // cross shadow boundary\n          node = rootNode.host;\n        } else {\n          // iterate up normal dom\n          node = parentElement;\n        }\n      }\n\n      node = originalNode;\n    }\n    // else, `getShadowRoot` might be true, but all that does is enable shadow DOM support\n    //  (i.e. it does not also presume that all nodes might have undisclosed shadows); or\n    //  it might be a falsy value, which means shadow DOM support is disabled\n\n    // Since we didn't find it sitting in an undisclosed shadow (or shadows are disabled)\n    //  now we can just test to see if it would normally be visible or not, provided it's\n    //  attached to the main document.\n    // NOTE: We must consider case where node is inside a shadow DOM and given directly to\n    //  `isTabbable()` or `isFocusable()` -- regardless of `getShadowRoot` option setting.\n\n    if (nodeIsAttached) {\n      // this works wherever the node is: if there's at least one client rect, it's\n      //  somehow displayed; it also covers the CSS 'display: contents' case where the\n      //  node itself is hidden in place of its contents; and there's no need to search\n      //  up the hierarchy either\n      return !node.getClientRects().length;\n    }\n\n    // Else, the node isn't attached to the document, which means the `getClientRects()`\n    //  API will __always__ return zero rects (this can happen, for example, if React\n    //  is used to render nodes onto a detached tree, as confirmed in this thread:\n    //  https://github.com/facebook/react/issues/9117#issuecomment-284228870)\n    //\n    // It also means that even window.getComputedStyle(node).display will return `undefined`\n    //  because styles are only computed for nodes that are in the document.\n    //\n    // NOTE: THIS HAS BEEN THE CASE FOR YEARS. It is not new, nor is it caused by tabbable\n    //  somehow. Though it was never stated officially, anyone who has ever used tabbable\n    //  APIs on nodes in detached containers has actually implicitly used tabbable in what\n    //  was later (as of v5.2.0 on Apr 9, 2021) called `displayCheck=\"none\"` mode -- essentially\n    //  considering __everything__ to be visible because of the innability to determine styles.\n  } else if (displayCheck === 'non-zero-area') {\n    // NOTE: Even though this tests that the node's client rect is non-zero to determine\n    //  whether it's displayed, and that a detached node will __always__ have a zero-area\n    //  client rect, we don't special-case for whether the node is attached or not. In\n    //  this mode, we do want to consider nodes that have a zero area to be hidden at all\n    //  times, and that includes attached or not.\n    return isZeroArea(node);\n  }\n\n  // visible, as far as we can tell, or per current `displayCheck` mode\n  return false;\n};\n\n// form fields (nested) inside a disabled fieldset are not focusable/tabbable\n//  unless they are in the _first_ <legend> element of the top-most disabled\n//  fieldset\nconst isDisabledFromFieldset = function (node) {\n  if (/^(INPUT|BUTTON|SELECT|TEXTAREA)$/.test(node.tagName)) {\n    let parentNode = node.parentElement;\n    // check if `node` is contained in a disabled <fieldset>\n    while (parentNode) {\n      if (parentNode.tagName === 'FIELDSET' && parentNode.disabled) {\n        // look for the first <legend> among the children of the disabled <fieldset>\n        for (let i = 0; i < parentNode.children.length; i++) {\n          const child = parentNode.children.item(i);\n          // when the first <legend> (in document order) is found\n          if (child.tagName === 'LEGEND') {\n            // if its parent <fieldset> is not nested in another disabled <fieldset>,\n            // return whether `node` is a descendant of its first <legend>\n            return matches.call(parentNode, 'fieldset[disabled] *')\n              ? true\n              : !child.contains(node);\n          }\n        }\n        // the disabled <fieldset> containing `node` has no <legend>\n        return true;\n      }\n      parentNode = parentNode.parentElement;\n    }\n  }\n\n  // else, node's tabbable/focusable state should not be affected by a fieldset's\n  //  enabled/disabled state\n  return false;\n};\n\nconst isNodeMatchingSelectorFocusable = function (options, node) {\n  if (\n    node.disabled ||\n    isHiddenInput(node) ||\n    isHidden(node, options) ||\n    // For a details element with a summary, the summary element gets the focus\n    isDetailsWithSummary(node) ||\n    isDisabledFromFieldset(node)\n  ) {\n    return false;\n  }\n  return true;\n};\n\nconst isNodeMatchingSelectorTabbable = function (options, node) {\n  if (\n    isNonTabbableRadio(node) ||\n    getTabindex(node) < 0 ||\n    !isNodeMatchingSelectorFocusable(options, node)\n  ) {\n    return false;\n  }\n  return true;\n};\n\nconst isValidShadowRootTabbable = function (shadowHostNode) {\n  const tabIndex = parseInt(shadowHostNode.getAttribute('tabindex'), 10);\n  if (isNaN(tabIndex) || tabIndex >= 0) {\n    return true;\n  }\n  // If a custom element has an explicit negative tabindex,\n  // browsers will not allow tab targeting said element's children.\n  return false;\n};\n\n/**\n * @param {Array.<Element|CandidatesScope>} candidates\n * @returns Element[]\n */\nconst sortByOrder = function (candidates) {\n  const regularTabbables = [];\n  const orderedTabbables = [];\n  candidates.forEach(function (item, i) {\n    const isScope = !!item.scope;\n    const element = isScope ? item.scope : item;\n    const candidateTabindex = getTabindex(element, isScope);\n    const elements = isScope ? sortByOrder(item.candidates) : element;\n    if (candidateTabindex === 0) {\n      isScope\n        ? regularTabbables.push(...elements)\n        : regularTabbables.push(element);\n    } else {\n      orderedTabbables.push({\n        documentOrder: i,\n        tabIndex: candidateTabindex,\n        item: item,\n        isScope: isScope,\n        content: elements,\n      });\n    }\n  });\n\n  return orderedTabbables\n    .sort(sortOrderedTabbables)\n    .reduce((acc, sortable) => {\n      sortable.isScope\n        ? acc.push(...sortable.content)\n        : acc.push(sortable.content);\n      return acc;\n    }, [])\n    .concat(regularTabbables);\n};\n\nconst tabbable = function (el, options) {\n  options = options || {};\n\n  let candidates;\n  if (options.getShadowRoot) {\n    candidates = getCandidatesIteratively([el], options.includeContainer, {\n      filter: isNodeMatchingSelectorTabbable.bind(null, options),\n      flatten: false,\n      getShadowRoot: options.getShadowRoot,\n      shadowRootFilter: isValidShadowRootTabbable,\n    });\n  } else {\n    candidates = getCandidates(\n      el,\n      options.includeContainer,\n      isNodeMatchingSelectorTabbable.bind(null, options)\n    );\n  }\n  return sortByOrder(candidates);\n};\n\nconst focusable = function (el, options) {\n  options = options || {};\n\n  let candidates;\n  if (options.getShadowRoot) {\n    candidates = getCandidatesIteratively([el], options.includeContainer, {\n      filter: isNodeMatchingSelectorFocusable.bind(null, options),\n      flatten: true,\n      getShadowRoot: options.getShadowRoot,\n    });\n  } else {\n    candidates = getCandidates(\n      el,\n      options.includeContainer,\n      isNodeMatchingSelectorFocusable.bind(null, options)\n    );\n  }\n\n  return candidates;\n};\n\nconst isTabbable = function (node, options) {\n  options = options || {};\n  if (!node) {\n    throw new Error('No node provided');\n  }\n  if (matches.call(node, candidateSelector) === false) {\n    return false;\n  }\n  return isNodeMatchingSelectorTabbable(options, node);\n};\n\nconst focusableCandidateSelector = /* #__PURE__ */ candidateSelectors\n  .concat('iframe')\n  .join(',');\n\nconst isFocusable = function (node, options) {\n  options = options || {};\n  if (!node) {\n    throw new Error('No node provided');\n  }\n  if (matches.call(node, focusableCandidateSelector) === false) {\n    return false;\n  }\n  return isNodeMatchingSelectorFocusable(options, node);\n};\n\nexport { tabbable, focusable, isTabbable, isFocusable };\n"], "names": ["candidateSelectors", "candidate<PERSON><PERSON><PERSON>", "join", "NoElement", "Element", "matches", "prototype", "msMatchesSelector", "webkitMatchesSelector", "getRootNode", "element", "ownerDocument", "getCandidates", "el", "<PERSON><PERSON><PERSON><PERSON>", "filter", "candidates", "Array", "slice", "apply", "querySelectorAll", "call", "unshift", "getCandidatesIteratively", "elements", "options", "elementsToCheck", "from", "length", "shift", "tagName", "assigned", "assignedElements", "content", "children", "nestedCandidates", "flatten", "push", "scope", "validCandidate", "includes", "shadowRoot", "getShadowRoot", "validShadowRoot", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getTabindex", "node", "isScope", "tabIndex", "test", "isContentEditable", "isNaN", "parseInt", "getAttribute", "sortOrderedTabbables", "a", "b", "documentOrder", "isInput", "isHiddenInput", "type", "isDetailsWithSummary", "r", "some", "child", "getCheckedRadio", "nodes", "form", "i", "checked", "isTabbableRadio", "name", "radioScope", "queryRadios", "radioSet", "window", "CSS", "escape", "err", "console", "error", "message", "isRadio", "isNonTabbableRadio", "isZeroArea", "getBoundingClientRect", "width", "height", "isHidden", "displayCheck", "getComputedStyle", "visibility", "isDirectSummary", "nodeUnderDetails", "parentElement", "nodeRootHost", "host", "nodeIsAttached", "contains", "originalNode", "rootNode", "assignedSlot", "getClientRects", "isDisabledFromFieldset", "parentNode", "disabled", "item", "isNodeMatchingSelectorFocusable", "isNodeMatchingSelectorTabbable", "isValidShadowRootTabbable", "shadowHostNode", "sortByOrder", "regularTabbables", "orderedTabbables", "for<PERSON>ach", "candidateTabindex", "sort", "reduce", "acc", "sortable", "concat", "tabbable", "bind", "focusable", "isTabbable", "Error", "focusableCandidateSelector", "isFocusable"], "mappings": ";;;;AAAA,IAAMA,kBAAkB,GAAG,CACzB,OADyB,EAEzB,QAFyB,EAGzB,UAHyB,EAIzB,SAJyB,EAKzB,QALyB,EAMzB,sBANyB,EAOzB,iBAPyB,EAQzB,iBARyB,EASzB,kDATyB,EAUzB,+BAVyB,EAWzB,SAXyB,CAA3B,CAAA;AAaA,IAAMC,iBAAiB,kBAAmBD,kBAAkB,CAACE,IAAnB,CAAwB,GAAxB,CAA1C,CAAA;AAEA,IAAMC,SAAS,GAAG,OAAOC,OAAP,KAAmB,WAArC,CAAA;AAEA,IAAMC,OAAO,GAAGF,SAAS,GACrB,YAAY,EADS,GAErBC,OAAO,CAACE,SAAR,CAAkBD,OAAlB,IACAD,OAAO,CAACE,SAAR,CAAkBC,iBADlB,IAEAH,OAAO,CAACE,SAAR,CAAkBE,qBAJtB,CAAA;AAMA,IAAMC,WAAW,GACf,CAACN,SAAD,IAAcC,OAAO,CAACE,SAAR,CAAkBG,WAAhC,GACI,UAACC,OAAD,EAAA;EAAA,OAAaA,OAAO,CAACD,WAAR,EAAb,CAAA;AAAA,CADJ,GAEI,UAACC,OAAD,EAAA;EAAA,OAAaA,OAAO,CAACC,aAArB,CAAA;AAAA,CAHN,CAAA;AAKA;AACA;AACA;AACA;AACA;AACA;;AACA,IAAMC,aAAa,GAAG,SAAhBA,aAAgB,CAAUC,EAAV,EAAcC,gBAAd,EAAgCC,MAAhC,EAAwC;AAC5D,EAAA,IAAIC,UAAU,GAAGC,KAAK,CAACX,SAAN,CAAgBY,KAAhB,CAAsBC,KAAtB,CACfN,EAAE,CAACO,gBAAH,CAAoBnB,iBAApB,CADe,CAAjB,CAAA;;EAGA,IAAIa,gBAAgB,IAAIT,OAAO,CAACgB,IAAR,CAAaR,EAAb,EAAiBZ,iBAAjB,CAAxB,EAA6D;IAC3De,UAAU,CAACM,OAAX,CAAmBT,EAAnB,CAAA,CAAA;AACD,GAAA;;AACDG,EAAAA,UAAU,GAAGA,UAAU,CAACD,MAAX,CAAkBA,MAAlB,CAAb,CAAA;AACA,EAAA,OAAOC,UAAP,CAAA;AACD,CATD,CAAA;AAWA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AACA,IAAMO,wBAAwB,GAAG,SAA3BA,wBAA2B,CAC/BC,QAD+B,EAE/BV,gBAF+B,EAG/BW,OAH+B,EAI/B;EACA,IAAMT,UAAU,GAAG,EAAnB,CAAA;AACA,EAAA,IAAMU,eAAe,GAAGT,KAAK,CAACU,IAAN,CAAWH,QAAX,CAAxB,CAAA;;EACA,OAAOE,eAAe,CAACE,MAAvB,EAA+B;AAC7B,IAAA,IAAMlB,OAAO,GAAGgB,eAAe,CAACG,KAAhB,EAAhB,CAAA;;AACA,IAAA,IAAInB,OAAO,CAACoB,OAAR,KAAoB,MAAxB,EAAgC;AAC9B;AACA,MAAA,IAAMC,QAAQ,GAAGrB,OAAO,CAACsB,gBAAR,EAAjB,CAAA;MACA,IAAMC,OAAO,GAAGF,QAAQ,CAACH,MAAT,GAAkBG,QAAlB,GAA6BrB,OAAO,CAACwB,QAArD,CAAA;MACA,IAAMC,gBAAgB,GAAGZ,wBAAwB,CAACU,OAAD,EAAU,IAAV,EAAgBR,OAAhB,CAAjD,CAAA;;MACA,IAAIA,OAAO,CAACW,OAAZ,EAAqB;AACnBpB,QAAAA,UAAU,CAACqB,IAAX,CAAA,KAAA,CAAArB,UAAU,EAASmB,gBAAT,CAAV,CAAA;AACD,OAFD,MAEO;QACLnB,UAAU,CAACqB,IAAX,CAAgB;AACdC,UAAAA,KAAK,EAAE5B,OADO;AAEdM,UAAAA,UAAU,EAAEmB,gBAAAA;SAFd,CAAA,CAAA;AAID,OAAA;AACF,KAbD,MAaO;AACL;MACA,IAAMI,cAAc,GAAGlC,OAAO,CAACgB,IAAR,CAAaX,OAAb,EAAsBT,iBAAtB,CAAvB,CAAA;;AACA,MAAA,IACEsC,cAAc,IACdd,OAAO,CAACV,MAAR,CAAeL,OAAf,CADA,KAECI,gBAAgB,IAAI,CAACU,QAAQ,CAACgB,QAAT,CAAkB9B,OAAlB,CAFtB,CADF,EAIE;QACAM,UAAU,CAACqB,IAAX,CAAgB3B,OAAhB,CAAA,CAAA;AACD,OATI;;;AAYL,MAAA,IAAM+B,UAAU,GACd/B,OAAO,CAAC+B,UAAR;MAEC,OAAOhB,OAAO,CAACiB,aAAf,KAAiC,UAAjC,IACCjB,OAAO,CAACiB,aAAR,CAAsBhC,OAAtB,CAJJ,CAAA;AAMA,MAAA,IAAMiC,eAAe,GACnB,CAAClB,OAAO,CAACmB,gBAAT,IAA6BnB,OAAO,CAACmB,gBAAR,CAAyBlC,OAAzB,CAD/B,CAAA;;MAGA,IAAI+B,UAAU,IAAIE,eAAlB,EAAmC;AACjC;AACA;AACA;AACA;AACA;AACA;AACA,QAAA,IAAMR,iBAAgB,GAAGZ,wBAAwB,CAC/CkB,UAAU,KAAK,IAAf,GAAsB/B,OAAO,CAACwB,QAA9B,GAAyCO,UAAU,CAACP,QADL,EAE/C,IAF+C,EAG/CT,OAH+C,CAAjD,CAAA;;QAMA,IAAIA,OAAO,CAACW,OAAZ,EAAqB;AACnBpB,UAAAA,UAAU,CAACqB,IAAX,CAAA,KAAA,CAAArB,UAAU,EAASmB,iBAAT,CAAV,CAAA;AACD,SAFD,MAEO;UACLnB,UAAU,CAACqB,IAAX,CAAgB;AACdC,YAAAA,KAAK,EAAE5B,OADO;AAEdM,YAAAA,UAAU,EAAEmB,iBAAAA;WAFd,CAAA,CAAA;AAID,SAAA;AACF,OArBD,MAqBO;AACL;AACA;QACAT,eAAe,CAACJ,OAAhB,CAAAI,KAAAA,CAAAA,eAAe,EAAYhB,OAAO,CAACwB,QAApB,CAAf,CAAA;AACD,OAAA;AACF,KAAA;AACF,GAAA;;AACD,EAAA,OAAOlB,UAAP,CAAA;AACD,CAxED,CAAA;;AA0EA,IAAM6B,WAAW,GAAG,SAAdA,WAAc,CAAUC,IAAV,EAAgBC,OAAhB,EAAyB;AAC3C,EAAA,IAAID,IAAI,CAACE,QAAL,GAAgB,CAApB,EAAuB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAA,IACE,CAACD,OAAO,IACN,yBAAA,CAA0BE,IAA1B,CAA+BH,IAAI,CAAChB,OAApC,CADD,IAECgB,IAAI,CAACI,iBAFP,KAGAC,KAAK,CAACC,QAAQ,CAACN,IAAI,CAACO,YAAL,CAAkB,UAAlB,CAAD,EAAgC,EAAhC,CAAT,CAJP,EAKE;AACA,MAAA,OAAO,CAAP,CAAA;AACD,KAAA;AACF,GAAA;;EAED,OAAOP,IAAI,CAACE,QAAZ,CAAA;AACD,CAxBD,CAAA;;AA0BA,IAAMM,oBAAoB,GAAG,SAAvBA,oBAAuB,CAAUC,CAAV,EAAaC,CAAb,EAAgB;EAC3C,OAAOD,CAAC,CAACP,QAAF,KAAeQ,CAAC,CAACR,QAAjB,GACHO,CAAC,CAACE,aAAF,GAAkBD,CAAC,CAACC,aADjB,GAEHF,CAAC,CAACP,QAAF,GAAaQ,CAAC,CAACR,QAFnB,CAAA;AAGD,CAJD,CAAA;;AAMA,IAAMU,OAAO,GAAG,SAAVA,OAAU,CAAUZ,IAAV,EAAgB;AAC9B,EAAA,OAAOA,IAAI,CAAChB,OAAL,KAAiB,OAAxB,CAAA;AACD,CAFD,CAAA;;AAIA,IAAM6B,aAAa,GAAG,SAAhBA,aAAgB,CAAUb,IAAV,EAAgB;EACpC,OAAOY,OAAO,CAACZ,IAAD,CAAP,IAAiBA,IAAI,CAACc,IAAL,KAAc,QAAtC,CAAA;AACD,CAFD,CAAA;;AAIA,IAAMC,oBAAoB,GAAG,SAAvBA,oBAAuB,CAAUf,IAAV,EAAgB;EAC3C,IAAMgB,CAAC,GACLhB,IAAI,CAAChB,OAAL,KAAiB,SAAjB,IACAb,KAAK,CAACX,SAAN,CAAgBY,KAAhB,CACGC,KADH,CACS2B,IAAI,CAACZ,QADd,CAEG6B,CAAAA,IAFH,CAEQ,UAACC,KAAD,EAAA;AAAA,IAAA,OAAWA,KAAK,CAAClC,OAAN,KAAkB,SAA7B,CAAA;AAAA,GAFR,CAFF,CAAA;AAKA,EAAA,OAAOgC,CAAP,CAAA;AACD,CAPD,CAAA;;AASA,IAAMG,eAAe,GAAG,SAAlBA,eAAkB,CAAUC,KAAV,EAAiBC,IAAjB,EAAuB;AAC7C,EAAA,KAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,KAAK,CAACtC,MAA1B,EAAkCwC,CAAC,EAAnC,EAAuC;AACrC,IAAA,IAAIF,KAAK,CAACE,CAAD,CAAL,CAASC,OAAT,IAAoBH,KAAK,CAACE,CAAD,CAAL,CAASD,IAAT,KAAkBA,IAA1C,EAAgD;MAC9C,OAAOD,KAAK,CAACE,CAAD,CAAZ,CAAA;AACD,KAAA;AACF,GAAA;AACF,CAND,CAAA;;AAQA,IAAME,eAAe,GAAG,SAAlBA,eAAkB,CAAUxB,IAAV,EAAgB;AACtC,EAAA,IAAI,CAACA,IAAI,CAACyB,IAAV,EAAgB;AACd,IAAA,OAAO,IAAP,CAAA;AACD,GAAA;;EACD,IAAMC,UAAU,GAAG1B,IAAI,CAACqB,IAAL,IAAa1D,WAAW,CAACqC,IAAD,CAA3C,CAAA;;AACA,EAAA,IAAM2B,WAAW,GAAG,SAAdA,WAAc,CAAUF,IAAV,EAAgB;IAClC,OAAOC,UAAU,CAACpD,gBAAX,CACL,+BAA+BmD,IAA/B,GAAsC,IADjC,CAAP,CAAA;GADF,CAAA;;AAMA,EAAA,IAAIG,QAAJ,CAAA;;EACA,IACE,OAAOC,MAAP,KAAkB,WAAlB,IACA,OAAOA,MAAM,CAACC,GAAd,KAAsB,WADtB,IAEA,OAAOD,MAAM,CAACC,GAAP,CAAWC,MAAlB,KAA6B,UAH/B,EAIE;AACAH,IAAAA,QAAQ,GAAGD,WAAW,CAACE,MAAM,CAACC,GAAP,CAAWC,MAAX,CAAkB/B,IAAI,CAACyB,IAAvB,CAAD,CAAtB,CAAA;AACD,GAND,MAMO;IACL,IAAI;AACFG,MAAAA,QAAQ,GAAGD,WAAW,CAAC3B,IAAI,CAACyB,IAAN,CAAtB,CAAA;KADF,CAEE,OAAOO,GAAP,EAAY;AACZ;AACAC,MAAAA,OAAO,CAACC,KAAR,CACE,0IADF,EAEEF,GAAG,CAACG,OAFN,CAAA,CAAA;AAIA,MAAA,OAAO,KAAP,CAAA;AACD,KAAA;AACF,GAAA;;EAED,IAAMZ,OAAO,GAAGJ,eAAe,CAACS,QAAD,EAAW5B,IAAI,CAACqB,IAAhB,CAA/B,CAAA;AACA,EAAA,OAAO,CAACE,OAAD,IAAYA,OAAO,KAAKvB,IAA/B,CAAA;AACD,CAjCD,CAAA;;AAmCA,IAAMoC,OAAO,GAAG,SAAVA,OAAU,CAAUpC,IAAV,EAAgB;EAC9B,OAAOY,OAAO,CAACZ,IAAD,CAAP,IAAiBA,IAAI,CAACc,IAAL,KAAc,OAAtC,CAAA;AACD,CAFD,CAAA;;AAIA,IAAMuB,kBAAkB,GAAG,SAArBA,kBAAqB,CAAUrC,IAAV,EAAgB;EACzC,OAAOoC,OAAO,CAACpC,IAAD,CAAP,IAAiB,CAACwB,eAAe,CAACxB,IAAD,CAAxC,CAAA;AACD,CAFD,CAAA;;AAIA,IAAMsC,UAAU,GAAG,SAAbA,UAAa,CAAUtC,IAAV,EAAgB;EACjC,IAA0BA,qBAAAA,GAAAA,IAAI,CAACuC,qBAAL,EAA1B;MAAQC,KAAR,yBAAQA,KAAR;MAAeC,MAAf,yBAAeA,MAAf,CAAA;;AACA,EAAA,OAAOD,KAAK,KAAK,CAAV,IAAeC,MAAM,KAAK,CAAjC,CAAA;AACD,CAHD,CAAA;;AAIA,IAAMC,QAAQ,GAAG,SAAXA,QAAW,CAAU1C,IAAV,EAAiD,IAAA,EAAA;EAAA,IAA/B2C,YAA+B,QAA/BA,YAA+B;MAAjB/C,aAAiB,QAAjBA,aAAiB,CAAA;;AAChE;AACA;AACA;AACA;AACA;EACA,IAAIgD,gBAAgB,CAAC5C,IAAD,CAAhB,CAAuB6C,UAAvB,KAAsC,QAA1C,EAAoD;AAClD,IAAA,OAAO,IAAP,CAAA;AACD,GAAA;;EAED,IAAMC,eAAe,GAAGvF,OAAO,CAACgB,IAAR,CAAayB,IAAb,EAAmB,+BAAnB,CAAxB,CAAA;EACA,IAAM+C,gBAAgB,GAAGD,eAAe,GAAG9C,IAAI,CAACgD,aAAR,GAAwBhD,IAAhE,CAAA;;EACA,IAAIzC,OAAO,CAACgB,IAAR,CAAawE,gBAAb,EAA+B,uBAA/B,CAAJ,EAA6D;AAC3D,IAAA,OAAO,IAAP,CAAA;AACD,GAd+D;AAiBhE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,EAAA,IAAME,YAAY,GAAGtF,WAAW,CAACqC,IAAD,CAAX,CAAkBkD,IAAvC,CAAA;EACA,IAAMC,cAAc,GAClB,CAAAF,YAAY,KAAA,IAAZ,IAAAA,YAAY,KAAA,KAAA,CAAZ,GAAAA,KAAAA,CAAAA,GAAAA,YAAY,CAAEpF,aAAd,CAA4BuF,QAA5B,CAAqCH,YAArC,CAAA,KACAjD,IAAI,CAACnC,aAAL,CAAmBuF,QAAnB,CAA4BpD,IAA5B,CAFF,CAAA;;AAIA,EAAA,IAAI,CAAC2C,YAAD,IAAiBA,YAAY,KAAK,MAAtC,EAA8C;AAC5C,IAAA,IAAI,OAAO/C,aAAP,KAAyB,UAA7B,EAAyC;AACvC;AACA;MACA,IAAMyD,YAAY,GAAGrD,IAArB,CAAA;;AACA,MAAA,OAAOA,IAAP,EAAa;AACX,QAAA,IAAMgD,aAAa,GAAGhD,IAAI,CAACgD,aAA3B,CAAA;AACA,QAAA,IAAMM,QAAQ,GAAG3F,WAAW,CAACqC,IAAD,CAA5B,CAAA;;AACA,QAAA,IACEgD,aAAa,IACb,CAACA,aAAa,CAACrD,UADf,IAEAC,aAAa,CAACoD,aAAD,CAAb,KAAiC,IAHnC;UAIE;AACA;AACA;UACA,OAAOV,UAAU,CAACtC,IAAD,CAAjB,CAAA;AACD,SARD,MAQO,IAAIA,IAAI,CAACuD,YAAT,EAAuB;AAC5B;UACAvD,IAAI,GAAGA,IAAI,CAACuD,YAAZ,CAAA;SAFK,MAGA,IAAI,CAACP,aAAD,IAAkBM,QAAQ,KAAKtD,IAAI,CAACnC,aAAxC,EAAuD;AAC5D;UACAmC,IAAI,GAAGsD,QAAQ,CAACJ,IAAhB,CAAA;AACD,SAHM,MAGA;AACL;AACAlD,UAAAA,IAAI,GAAGgD,aAAP,CAAA;AACD,SAAA;AACF,OAAA;;AAEDhD,MAAAA,IAAI,GAAGqD,YAAP,CAAA;AACD,KA7B2C;AA+B5C;AACA;AAEA;AACA;AACA;AACA;AACA;;;AAEA,IAAA,IAAIF,cAAJ,EAAoB;AAClB;AACA;AACA;AACA;AACA,MAAA,OAAO,CAACnD,IAAI,CAACwD,cAAL,GAAsB1E,MAA9B,CAAA;AACD,KA9C2C;AAiD5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACD,GA7DD,MA6DO,IAAI6D,YAAY,KAAK,eAArB,EAAsC;AAC3C;AACA;AACA;AACA;AACA;IACA,OAAOL,UAAU,CAACtC,IAAD,CAAjB,CAAA;AACD,GA1G+D;;;AA6GhE,EAAA,OAAO,KAAP,CAAA;AACD,CA9GD;AAiHA;AACA;;;AACA,IAAMyD,sBAAsB,GAAG,SAAzBA,sBAAyB,CAAUzD,IAAV,EAAgB;AAC7C,EAAA,IAAI,mCAAmCG,IAAnC,CAAwCH,IAAI,CAAChB,OAA7C,CAAJ,EAA2D;AACzD,IAAA,IAAI0E,UAAU,GAAG1D,IAAI,CAACgD,aAAtB,CADyD;;AAGzD,IAAA,OAAOU,UAAP,EAAmB;MACjB,IAAIA,UAAU,CAAC1E,OAAX,KAAuB,UAAvB,IAAqC0E,UAAU,CAACC,QAApD,EAA8D;AAC5D;AACA,QAAA,KAAK,IAAIrC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGoC,UAAU,CAACtE,QAAX,CAAoBN,MAAxC,EAAgDwC,CAAC,EAAjD,EAAqD;UACnD,IAAMJ,KAAK,GAAGwC,UAAU,CAACtE,QAAX,CAAoBwE,IAApB,CAAyBtC,CAAzB,CAAd,CADmD;;AAGnD,UAAA,IAAIJ,KAAK,CAAClC,OAAN,KAAkB,QAAtB,EAAgC;AAC9B;AACA;AACA,YAAA,OAAOzB,OAAO,CAACgB,IAAR,CAAamF,UAAb,EAAyB,sBAAzB,CAAA,GACH,IADG,GAEH,CAACxC,KAAK,CAACkC,QAAN,CAAepD,IAAf,CAFL,CAAA;AAGD,WAAA;AACF,SAZ2D;;;AAc5D,QAAA,OAAO,IAAP,CAAA;AACD,OAAA;;MACD0D,UAAU,GAAGA,UAAU,CAACV,aAAxB,CAAA;AACD,KAAA;AACF,GAvB4C;AA0B7C;;;AACA,EAAA,OAAO,KAAP,CAAA;AACD,CA5BD,CAAA;;AA8BA,IAAMa,+BAA+B,GAAG,SAAlCA,+BAAkC,CAAUlF,OAAV,EAAmBqB,IAAnB,EAAyB;AAC/D,EAAA,IACEA,IAAI,CAAC2D,QAAL,IACA9C,aAAa,CAACb,IAAD,CADb,IAEA0C,QAAQ,CAAC1C,IAAD,EAAOrB,OAAP,CAFR;EAIAoC,oBAAoB,CAACf,IAAD,CAJpB,IAKAyD,sBAAsB,CAACzD,IAAD,CANxB,EAOE;AACA,IAAA,OAAO,KAAP,CAAA;AACD,GAAA;;AACD,EAAA,OAAO,IAAP,CAAA;AACD,CAZD,CAAA;;AAcA,IAAM8D,8BAA8B,GAAG,SAAjCA,8BAAiC,CAAUnF,OAAV,EAAmBqB,IAAnB,EAAyB;AAC9D,EAAA,IACEqC,kBAAkB,CAACrC,IAAD,CAAlB,IACAD,WAAW,CAACC,IAAD,CAAX,GAAoB,CADpB,IAEA,CAAC6D,+BAA+B,CAAClF,OAAD,EAAUqB,IAAV,CAHlC,EAIE;AACA,IAAA,OAAO,KAAP,CAAA;AACD,GAAA;;AACD,EAAA,OAAO,IAAP,CAAA;AACD,CATD,CAAA;;AAWA,IAAM+D,yBAAyB,GAAG,SAA5BA,yBAA4B,CAAUC,cAAV,EAA0B;AAC1D,EAAA,IAAM9D,QAAQ,GAAGI,QAAQ,CAAC0D,cAAc,CAACzD,YAAf,CAA4B,UAA5B,CAAD,EAA0C,EAA1C,CAAzB,CAAA;;EACA,IAAIF,KAAK,CAACH,QAAD,CAAL,IAAmBA,QAAQ,IAAI,CAAnC,EAAsC;AACpC,IAAA,OAAO,IAAP,CAAA;AACD,GAJyD;AAM1D;;;AACA,EAAA,OAAO,KAAP,CAAA;AACD,CARD,CAAA;AAUA;AACA;AACA;AACA;;;AACA,IAAM+D,WAAW,GAAG,SAAdA,WAAc,CAAU/F,UAAV,EAAsB;EACxC,IAAMgG,gBAAgB,GAAG,EAAzB,CAAA;EACA,IAAMC,gBAAgB,GAAG,EAAzB,CAAA;AACAjG,EAAAA,UAAU,CAACkG,OAAX,CAAmB,UAAUR,IAAV,EAAgBtC,CAAhB,EAAmB;AACpC,IAAA,IAAMrB,OAAO,GAAG,CAAC,CAAC2D,IAAI,CAACpE,KAAvB,CAAA;IACA,IAAM5B,OAAO,GAAGqC,OAAO,GAAG2D,IAAI,CAACpE,KAAR,GAAgBoE,IAAvC,CAAA;AACA,IAAA,IAAMS,iBAAiB,GAAGtE,WAAW,CAACnC,OAAD,EAAUqC,OAAV,CAArC,CAAA;IACA,IAAMvB,QAAQ,GAAGuB,OAAO,GAAGgE,WAAW,CAACL,IAAI,CAAC1F,UAAN,CAAd,GAAkCN,OAA1D,CAAA;;IACA,IAAIyG,iBAAiB,KAAK,CAA1B,EAA6B;AAC3BpE,MAAAA,OAAO,GACHiE,gBAAgB,CAAC3E,IAAjB,OAAA2E,gBAAgB,EAASxF,QAAT,CADb,GAEHwF,gBAAgB,CAAC3E,IAAjB,CAAsB3B,OAAtB,CAFJ,CAAA;AAGD,KAJD,MAIO;MACLuG,gBAAgB,CAAC5E,IAAjB,CAAsB;AACpBoB,QAAAA,aAAa,EAAEW,CADK;AAEpBpB,QAAAA,QAAQ,EAAEmE,iBAFU;AAGpBT,QAAAA,IAAI,EAAEA,IAHc;AAIpB3D,QAAAA,OAAO,EAAEA,OAJW;AAKpBd,QAAAA,OAAO,EAAET,QAAAA;OALX,CAAA,CAAA;AAOD,KAAA;GAjBH,CAAA,CAAA;AAoBA,EAAA,OAAOyF,gBAAgB,CACpBG,IADI,CACC9D,oBADD,CAAA,CAEJ+D,MAFI,CAEG,UAACC,GAAD,EAAMC,QAAN,EAAmB;IACzBA,QAAQ,CAACxE,OAAT,GACIuE,GAAG,CAACjF,IAAJ,CAAA,KAAA,CAAAiF,GAAG,EAASC,QAAQ,CAACtF,OAAlB,CADP,GAEIqF,GAAG,CAACjF,IAAJ,CAASkF,QAAQ,CAACtF,OAAlB,CAFJ,CAAA;AAGA,IAAA,OAAOqF,GAAP,CAAA;AACD,GAPI,EAOF,EAPE,CAAA,CAQJE,MARI,CAQGR,gBARH,CAAP,CAAA;AASD,CAhCD,CAAA;;AAkCMS,IAAAA,QAAQ,GAAG,SAAXA,QAAW,CAAU5G,EAAV,EAAcY,OAAd,EAAuB;EACtCA,OAAO,GAAGA,OAAO,IAAI,EAArB,CAAA;AAEA,EAAA,IAAIT,UAAJ,CAAA;;EACA,IAAIS,OAAO,CAACiB,aAAZ,EAA2B;IACzB1B,UAAU,GAAGO,wBAAwB,CAAC,CAACV,EAAD,CAAD,EAAOY,OAAO,CAACX,gBAAf,EAAiC;MACpEC,MAAM,EAAE6F,8BAA8B,CAACc,IAA/B,CAAoC,IAApC,EAA0CjG,OAA1C,CAD4D;AAEpEW,MAAAA,OAAO,EAAE,KAF2D;MAGpEM,aAAa,EAAEjB,OAAO,CAACiB,aAH6C;AAIpEE,MAAAA,gBAAgB,EAAEiE,yBAAAA;AAJkD,KAAjC,CAArC,CAAA;AAMD,GAPD,MAOO;AACL7F,IAAAA,UAAU,GAAGJ,aAAa,CACxBC,EADwB,EAExBY,OAAO,CAACX,gBAFgB,EAGxB8F,8BAA8B,CAACc,IAA/B,CAAoC,IAApC,EAA0CjG,OAA1C,CAHwB,CAA1B,CAAA;AAKD,GAAA;;EACD,OAAOsF,WAAW,CAAC/F,UAAD,CAAlB,CAAA;AACD,EAnBD;;AAqBM2G,IAAAA,SAAS,GAAG,SAAZA,SAAY,CAAU9G,EAAV,EAAcY,OAAd,EAAuB;EACvCA,OAAO,GAAGA,OAAO,IAAI,EAArB,CAAA;AAEA,EAAA,IAAIT,UAAJ,CAAA;;EACA,IAAIS,OAAO,CAACiB,aAAZ,EAA2B;IACzB1B,UAAU,GAAGO,wBAAwB,CAAC,CAACV,EAAD,CAAD,EAAOY,OAAO,CAACX,gBAAf,EAAiC;MACpEC,MAAM,EAAE4F,+BAA+B,CAACe,IAAhC,CAAqC,IAArC,EAA2CjG,OAA3C,CAD4D;AAEpEW,MAAAA,OAAO,EAAE,IAF2D;MAGpEM,aAAa,EAAEjB,OAAO,CAACiB,aAAAA;AAH6C,KAAjC,CAArC,CAAA;AAKD,GAND,MAMO;AACL1B,IAAAA,UAAU,GAAGJ,aAAa,CACxBC,EADwB,EAExBY,OAAO,CAACX,gBAFgB,EAGxB6F,+BAA+B,CAACe,IAAhC,CAAqC,IAArC,EAA2CjG,OAA3C,CAHwB,CAA1B,CAAA;AAKD,GAAA;;AAED,EAAA,OAAOT,UAAP,CAAA;AACD,EAnBD;;AAqBM4G,IAAAA,UAAU,GAAG,SAAbA,UAAa,CAAU9E,IAAV,EAAgBrB,OAAhB,EAAyB;EAC1CA,OAAO,GAAGA,OAAO,IAAI,EAArB,CAAA;;EACA,IAAI,CAACqB,IAAL,EAAW;AACT,IAAA,MAAM,IAAI+E,KAAJ,CAAU,kBAAV,CAAN,CAAA;AACD,GAAA;;EACD,IAAIxH,OAAO,CAACgB,IAAR,CAAayB,IAAb,EAAmB7C,iBAAnB,CAA0C,KAAA,KAA9C,EAAqD;AACnD,IAAA,OAAO,KAAP,CAAA;AACD,GAAA;;AACD,EAAA,OAAO2G,8BAA8B,CAACnF,OAAD,EAAUqB,IAAV,CAArC,CAAA;AACD,EATD;;AAWA,IAAMgF,0BAA0B,kBAAmB9H,kBAAkB,CAClEwH,MADgD,CACzC,QADyC,CAEhDtH,CAAAA,IAFgD,CAE3C,GAF2C,CAAnD,CAAA;;AAIM6H,IAAAA,WAAW,GAAG,SAAdA,WAAc,CAAUjF,IAAV,EAAgBrB,OAAhB,EAAyB;EAC3CA,OAAO,GAAGA,OAAO,IAAI,EAArB,CAAA;;EACA,IAAI,CAACqB,IAAL,EAAW;AACT,IAAA,MAAM,IAAI+E,KAAJ,CAAU,kBAAV,CAAN,CAAA;AACD,GAAA;;EACD,IAAIxH,OAAO,CAACgB,IAAR,CAAayB,IAAb,EAAmBgF,0BAAnB,CAAmD,KAAA,KAAvD,EAA8D;AAC5D,IAAA,OAAO,KAAP,CAAA;AACD,GAAA;;AACD,EAAA,OAAOnB,+BAA+B,CAAClF,OAAD,EAAUqB,IAAV,CAAtC,CAAA;AACD;;;;"}