import { Logger } from '../utils/Logger';
import { SelfTrainingOrchestrator } from './SelfTrainingOrchestrator';

export interface TrainingAspect {
  name: string;
  enabled: boolean;
  priority: number;
  lastTraining: Date | null;
  successRate: number;
  trainingCount: number;
}

export interface AspectTrainingPlan {
  aspects: string[];
  duration: number;
  intensity: string;
  parallelExecution: boolean;
  dependencies: string[];
}

export class MultiAspectTrainingCoordinator {
  private logger: Logger;
  private orchestrator: SelfTrainingOrchestrator;
  private aspects: Map<string, TrainingAspect> = new Map();
  private trainingHistory: Array<{ aspect: string; timestamp: Date; success: boolean; duration: number }> = [];

  constructor(logger: Logger, orchestrator: SelfTrainingOrchestrator) {
    this.logger = logger;
    this.orchestrator = orchestrator;
  }

  public async initialize(aspectsConfig: any): Promise<void> {
    this.logger.debug('Initializing multi-aspect training coordinator...');
    
    this.initializeAspects(aspectsConfig);
    this.logger.debug(`✅ Initialized ${this.aspects.size} training aspects`);
  }

  private initializeAspects(aspectsConfig: any): void {
    const aspectDefinitions = [
      { name: 'coding', priority: 1, description: 'Code analysis, generation, and optimization' },
      { name: 'webResearch', priority: 3, description: 'Web browsing and information gathering' },
      { name: 'performance', priority: 2, description: 'System performance and efficiency optimization' },
      { name: 'adaptiveBehavior', priority: 4, description: 'Behavioral adaptation and learning' },
      { name: 'knowledgeExpansion', priority: 3, description: 'Knowledge base expansion and integration' },
      { name: 'curiosityDriven', priority: 5, description: 'Autonomous exploration and discovery' }
    ];

    for (const def of aspectDefinitions) {
      this.aspects.set(def.name, {
        name: def.name,
        enabled: aspectsConfig[def.name] || false,
        priority: def.priority,
        lastTraining: null,
        successRate: 1.0,
        trainingCount: 0
      });
    }
  }

  public async executeQuickTraining(intensity: string): Promise<void> {
    this.logger.debug('🚀 Executing quick training session...');
    
    const plan = this.createQuickTrainingPlan(intensity);
    await this.executePlan(plan);
  }

  public async executeDeepTraining(intensity: string): Promise<void> {
    this.logger.debug('🔍 Executing deep training session...');
    
    const plan = this.createDeepTrainingPlan(intensity);
    await this.executePlan(plan);
  }

  private createQuickTrainingPlan(intensity: string): AspectTrainingPlan {
    const enabledAspects = Array.from(this.aspects.values())
      .filter(aspect => aspect.enabled)
      .sort((a, b) => a.priority - b.priority);

    // For quick training, focus on high-priority aspects
    const selectedAspects = enabledAspects
      .slice(0, 2)
      .map(aspect => aspect.name);

    const intensityDurations = {
      minimal: 30000,  // 30 seconds
      low: 60000,      // 1 minute
      medium: 120000,  // 2 minutes
      high: 180000,    // 3 minutes
      maximum: 300000  // 5 minutes
    };

    return {
      aspects: selectedAspects,
      duration: intensityDurations[intensity as keyof typeof intensityDurations] || 60000,
      intensity,
      parallelExecution: true,
      dependencies: []
    };
  }

  private createDeepTrainingPlan(intensity: string): AspectTrainingPlan {
    const enabledAspects = Array.from(this.aspects.values())
      .filter(aspect => aspect.enabled)
      .sort((a, b) => {
        // Sort by priority and last training time
        const priorityDiff = a.priority - b.priority;
        if (priorityDiff !== 0) return priorityDiff;
        
        const aTime = a.lastTraining?.getTime() || 0;
        const bTime = b.lastTraining?.getTime() || 0;
        return aTime - bTime;
      });

    // For deep training, include more aspects
    const selectedAspects = enabledAspects
      .slice(0, 4)
      .map(aspect => aspect.name);

    const intensityDurations = {
      minimal: 300000,   // 5 minutes
      low: 600000,       // 10 minutes
      medium: 1200000,   // 20 minutes
      high: 1800000,     // 30 minutes
      maximum: 3600000   // 60 minutes
    };

    return {
      aspects: selectedAspects,
      duration: intensityDurations[intensity as keyof typeof intensityDurations] || 600000,
      intensity,
      parallelExecution: false, // Sequential for deep training
      dependencies: this.getDependencies(selectedAspects)
    };
  }

  private getDependencies(aspects: string[]): string[] {
    const dependencies: string[] = [];
    
    // Define aspect dependencies
    if (aspects.includes('webResearch') && aspects.includes('knowledgeExpansion')) {
      dependencies.push('webResearch->knowledgeExpansion');
    }
    
    if (aspects.includes('performance') && aspects.includes('adaptiveBehavior')) {
      dependencies.push('performance->adaptiveBehavior');
    }

    return dependencies;
  }

  private async executePlan(plan: AspectTrainingPlan): Promise<void> {
    this.logger.debug(`Executing training plan: ${plan.aspects.join(', ')}`);
    
    const startTime = Date.now();
    const timePerAspect = plan.duration / plan.aspects.length;

    try {
      if (plan.parallelExecution) {
        await this.executeAspectsInParallel(plan.aspects, timePerAspect);
      } else {
        await this.executeAspectsSequentially(plan.aspects, timePerAspect, plan.dependencies);
      }
      
      this.logger.debug(`✅ Training plan completed in ${Date.now() - startTime}ms`);
    } catch (error) {
      this.logger.error(`❌ Training plan failed: ${error}`);
      throw error;
    }
  }

  private async executeAspectsInParallel(aspects: string[], timePerAspect: number): Promise<void> {
    const promises = aspects.map(aspect => this.trainAspect(aspect, timePerAspect));
    await Promise.allSettled(promises);
  }

  private async executeAspectsSequentially(aspects: string[], timePerAspect: number, dependencies: string[]): Promise<void> {
    // Sort aspects based on dependencies
    const sortedAspects = this.sortAspectsByDependencies(aspects, dependencies);
    
    for (const aspect of sortedAspects) {
      await this.trainAspect(aspect, timePerAspect);
    }
  }

  private sortAspectsByDependencies(aspects: string[], dependencies: string[]): string[] {
    const dependencyMap = new Map<string, string[]>();
    
    // Parse dependencies
    for (const dep of dependencies) {
      const [from, to] = dep.split('->');
      if (!dependencyMap.has(to)) {
        dependencyMap.set(to, []);
      }
      dependencyMap.get(to)!.push(from);
    }

    // Simple topological sort
    const sorted: string[] = [];
    const visited = new Set<string>();
    
    const visit = (aspect: string) => {
      if (visited.has(aspect)) return;
      visited.add(aspect);
      
      const deps = dependencyMap.get(aspect) || [];
      for (const dep of deps) {
        if (aspects.includes(dep)) {
          visit(dep);
        }
      }
      
      sorted.push(aspect);
    };

    for (const aspect of aspects) {
      visit(aspect);
    }

    return sorted;
  }

  private async trainAspect(aspectName: string, duration: number): Promise<void> {
    const aspect = this.aspects.get(aspectName);
    if (!aspect) return;

    this.logger.debug(`🎯 Training aspect: ${aspectName} (${duration}ms)`);
    const startTime = Date.now();
    let success = false;

    try {
      switch (aspectName) {
        case 'coding':
          await this.trainCodingAspect(duration);
          break;
        case 'webResearch':
          await this.trainWebResearchAspect(duration);
          break;
        case 'performance':
          await this.trainPerformanceAspect(duration);
          break;
        case 'adaptiveBehavior':
          await this.trainAdaptiveBehaviorAspect(duration);
          break;
        case 'knowledgeExpansion':
          await this.trainKnowledgeExpansionAspect(duration);
          break;
        case 'curiosityDriven':
          await this.trainCuriosityDrivenAspect(duration);
          break;
        default:
          this.logger.warn(`Unknown aspect: ${aspectName}`);
          return;
      }
      
      success = true;
      this.logger.debug(`✅ Completed training: ${aspectName}`);
    } catch (error) {
      this.logger.error(`❌ Failed training aspect ${aspectName}: ${error}`);
    } finally {
      const actualDuration = Date.now() - startTime;
      this.recordTrainingResult(aspectName, success, actualDuration);
    }
  }

  private async trainCodingAspect(duration: number): Promise<void> {
    // Run focused training session for coding aspects
    await this.orchestrator.startSelfTraining();
  }

  private async trainWebResearchAspect(duration: number): Promise<void> {
    // Run focused training session for web research aspects
    await this.orchestrator.startSelfTraining();
  }

  private async trainPerformanceAspect(duration: number): Promise<void> {
    // Run focused training session for performance aspects
    await this.orchestrator.startSelfTraining();
  }

  private async trainAdaptiveBehaviorAspect(duration: number): Promise<void> {
    // Run focused training session for adaptive behavior aspects
    await this.orchestrator.startSelfTraining();
  }

  private async trainKnowledgeExpansionAspect(duration: number): Promise<void> {
    // Run focused training session for knowledge expansion aspects
    await this.orchestrator.startSelfTraining();
  }

  private async trainCuriosityDrivenAspect(duration: number): Promise<void> {
    // Run focused training session for curiosity-driven aspects
    await this.orchestrator.startSelfTraining();
  }

  private recordTrainingResult(aspectName: string, success: boolean, duration: number): void {
    const aspect = this.aspects.get(aspectName);
    if (!aspect) return;

    // Update aspect statistics
    aspect.lastTraining = new Date();
    aspect.trainingCount++;
    
    // Update success rate using exponential moving average
    const alpha = 0.1;
    aspect.successRate = alpha * (success ? 1 : 0) + (1 - alpha) * aspect.successRate;

    // Record in history
    this.trainingHistory.push({
      aspect: aspectName,
      timestamp: new Date(),
      success,
      duration
    });

    // Keep only recent history
    if (this.trainingHistory.length > 1000) {
      this.trainingHistory = this.trainingHistory.slice(-1000);
    }
  }

  public getAspectStatus(): any {
    const aspects = Array.from(this.aspects.values()).map(aspect => ({
      name: aspect.name,
      enabled: aspect.enabled,
      priority: aspect.priority,
      lastTraining: aspect.lastTraining,
      successRate: Math.round(aspect.successRate * 100),
      trainingCount: aspect.trainingCount
    }));

    const recentHistory = this.trainingHistory.slice(-50);
    const overallSuccessRate = recentHistory.length > 0
      ? recentHistory.filter(h => h.success).length / recentHistory.length
      : 0;

    return {
      aspects,
      overallSuccessRate: Math.round(overallSuccessRate * 100),
      totalTrainingSessions: this.trainingHistory.length,
      recentActivity: recentHistory.slice(-10)
    };
  }
}
