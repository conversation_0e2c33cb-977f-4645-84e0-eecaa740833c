{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "declaration": true, "declarationMap": false, "sourceMap": false, "resolveJsonModule": true, "moduleResolution": "node", "allowSyntheticDefaultImports": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "removeComments": true, "noUnusedLocals": false, "noUnusedParameters": false}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "scripts", "**/*.test.ts", "**/*.spec.ts"]}