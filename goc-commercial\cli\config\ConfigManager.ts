import fs from 'fs';
import path from 'path';
import os from 'os';
import yaml from 'yaml';
import crypto from 'crypto';

export interface AIProviderConfig {
  name: string;
  baseUrl?: string;
  apiKey?: string;
  defaultModel?: string;
  enabled: boolean;
}

export interface UserGuidelines {
  codingStyle: {
    language: string;
    framework: string;
    conventions: string[];
    patterns: string[];
  };
  preferences: {
    testingFramework: string;
    documentationStyle: string;
    errorHandling: string;
    logging: boolean;
    backwardCompatibility: boolean;
  };
  projectRules: {
    fileNaming: string;
    directoryStructure: string[];
    dependencies: string[];
    excludeFiles: string[];
  };
  customInstructions: string[];
}

export interface ProjectContext {
  name: string;
  path: string;
  type: 'laravel' | 'react' | 'node' | 'python' | 'generic';
  framework: string;
  version: string;
  lastAccessed: Date;
  guidelines: Partial<UserGuidelines>;
}

export interface ConfigValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  suggestions: string[];
}

export interface ConfigMigration {
  version: string;
  description: string;
  migrate: (config: any) => any;
}

export interface Config {
  version: string;
  defaultProvider: string;
  providers: {
    ollama: AIProviderConfig;
    openai: AIProviderConfig;
    groq: AIProviderConfig;
    gemini: AIProviderConfig;
  };
  backend: {
    url: string;
    enabled: boolean;
    timeout: number;
  };
  auth: {
    token?: string;
    user?: any;
  };
  codebase: {
    excludePatterns: string[];
    includePatterns: string[];
    maxFileSize: number;
    maxFiles: number;
  };
  contextEngine: {
    provider: 'augment' | 'intelligent' | 'tree-sitter' | 'codebert' | 'basic';
    enabled: boolean;
    fallbackToBasic: boolean;
    cacheEnabled: boolean;
    cacheTTL: number;
    maxSemanticDepth: number;
    maxContextNodes: number;
    apiConfig?: {
      baseUrl?: string;
      apiKey?: string;
      timeout?: number;
    };
    localConfig?: {
      modelPath?: string;
      embeddingDimensions?: number;
    };
  };
  ui: {
    theme: 'dark' | 'light';
    verbose: boolean;
  };
  userGuidelines: UserGuidelines;
  projects: ProjectContext[];
  currentProject?: string;
  security: {
    encryptApiKeys: boolean;
    configChecksum?: string;
  };
}

export class ConfigManager {
  private static instance: ConfigManager;
  private configPath: string;
  private config: Config;
  private readonly currentVersion = '1.2.0';
  private migrations: ConfigMigration[] = [];

  constructor() {
    this.configPath = path.join(os.homedir(), '.goc-agent', 'config.yaml');
    this.initializeMigrations();
    this.config = this.loadConfig();
  }

  public static getInstance(): ConfigManager {
    if (!ConfigManager.instance) {
      ConfigManager.instance = new ConfigManager();
    }
    return ConfigManager.instance;
  }

  private getDefaultConfig(): Config {
    return {
      version: this.currentVersion,
      defaultProvider: 'ollama',
      providers: {
        ollama: {
          name: 'Ollama',
          baseUrl: 'http://localhost:11434',
          defaultModel: 'deepseek-coder:latest',
          enabled: true
        },
        openai: {
          name: 'OpenAI',
          baseUrl: 'https://api.openai.com/v1',
          defaultModel: 'gpt-4',
          enabled: false
        },
        groq: {
          name: 'Groq',
          baseUrl: 'https://api.groq.com/openai/v1',
          defaultModel: 'llama-3.1-70b-versatile',
          enabled: false
        },
        gemini: {
          name: 'Gemini',
          baseUrl: 'https://generativelanguage.googleapis.com/v1beta',
          defaultModel: 'gemini-pro',
          enabled: false
        }
      },
      backend: {
        url: 'http://localhost:8000/api',
        enabled: true,
        timeout: 120000,
      },
      auth: {},
      codebase: {
        excludePatterns: [
          'node_modules/**',
          '.git/**',
          'dist/**',
          'build/**',
          '*.log',
          '.env*',
          '*.min.js',
          '*.map'
        ],
        includePatterns: [
          '**/*.ts',
          '**/*.js',
          '**/*.tsx',
          '**/*.jsx',
          '**/*.py',
          '**/*.java',
          '**/*.cpp',
          '**/*.c',
          '**/*.h',
          '**/*.cs',
          '**/*.php',
          '**/*.rb',
          '**/*.go',
          '**/*.rs',
          '**/*.md',
          '**/*.json',
          '**/*.yaml',
          '**/*.yml'
        ],
        maxFileSize: 1024 * 1024, // 1MB
        maxFiles: 300
      },
      contextEngine: {
        provider: 'intelligent',
        enabled: true,
        fallbackToBasic: true,
        cacheEnabled: true,
        cacheTTL: 300, // 5 minutes
        maxSemanticDepth: 2,
        maxContextNodes: 50,
        apiConfig: {
          timeout: 30000
        }
      },
      ui: {
        theme: 'dark',
        verbose: false
      },
      userGuidelines: {
        codingStyle: {
          language: 'typescript',
          framework: 'laravel',
          conventions: ['camelCase', 'PascalCase for classes'],
          patterns: ['MVC', 'Repository Pattern']
        },
        preferences: {
          testingFramework: 'phpunit',
          documentationStyle: 'phpdoc',
          errorHandling: 'exceptions',
          logging: false,
          backwardCompatibility: false
        },
        projectRules: {
          fileNaming: 'kebab-case',
          directoryStructure: ['app/', 'resources/', 'database/'],
          dependencies: [],
          excludeFiles: ['*.log', '.env*']
        },
        customInstructions: [
          'Always use latest Laravel 12 features',
          'Implement with Alpine.js for frontend',
          'Use Tailwind CSS for styling',
          'No backward compatibility needed'
        ]
      },
      projects: [],
      currentProject: undefined,
      security: {
        encryptApiKeys: false,
        configChecksum: undefined
      }
    };
  }

  private loadConfig(): Config {
    try {
      if (!fs.existsSync(this.configPath)) {
        const defaultConfig = this.getDefaultConfig();
        this.saveConfig(defaultConfig);
        return defaultConfig;
      }

      const configContent = fs.readFileSync(this.configPath, 'utf8');
      const loadedConfig = yaml.parse(configContent) as Config;
      
      // Merge with defaults to ensure all properties exist
      return this.mergeWithDefaults(loadedConfig);
    } catch (error) {
      console.warn('Failed to load config, using defaults:', error);
      return this.getDefaultConfig();
    }
  }

  private mergeWithDefaults(loadedConfig: Partial<Config>): Config {
    const defaultConfig = this.getDefaultConfig();
    return {
      ...defaultConfig,
      ...loadedConfig,
      providers: {
        ...defaultConfig.providers,
        ...loadedConfig.providers
      },
      codebase: {
        ...defaultConfig.codebase,
        ...loadedConfig.codebase
      },
      contextEngine: {
        ...defaultConfig.contextEngine,
        ...loadedConfig.contextEngine
      },
      ui: {
        ...defaultConfig.ui,
        ...loadedConfig.ui
      }
    };
  }

  private saveConfig(config: Config): void {
    try {
      const configDir = path.dirname(this.configPath);
      if (!fs.existsSync(configDir)) {
        fs.mkdirSync(configDir, { recursive: true });
      }

      const yamlContent = yaml.stringify(config, { indent: 2 });
      fs.writeFileSync(this.configPath, yamlContent, 'utf8');
    } catch (error) {
      throw new Error(`Failed to save config: ${error}`);
    }
  }

  public getConfig(): Config {
    return { ...this.config };
  }

  public updateConfig(updates: Partial<Config>): void {
    this.config = this.mergeWithDefaults({ ...this.config, ...updates });
    this.saveConfig(this.config);
  }

  public getProvider(name: string): AIProviderConfig | undefined {
    return this.config.providers[name as keyof typeof this.config.providers];
  }

  public setProviderApiKey(providerName: string, apiKey: string): void {
    const provider = this.getProvider(providerName);
    if (provider) {
      provider.apiKey = apiKey;
      this.saveConfig(this.config);
    }
  }

  public enableProvider(providerName: string, enabled: boolean = true): void {
    const provider = this.getProvider(providerName);
    if (provider) {
      provider.enabled = enabled;
      this.saveConfig(this.config);
    }
  }

  public setProviderDefaultModel(providerName: string, model: string): void {
    const provider = this.getProvider(providerName);
    if (provider) {
      provider.defaultModel = model;
      this.saveConfig(this.config);
    }
  }

  public setDefaultProvider(providerName: string): void {
    this.config.defaultProvider = providerName;
    this.saveConfig(this.config);
  }

  public getConfigPath(): string {
    return this.configPath;
  }

  // Backend Configuration
  public setBackendUrl(url: string): void {
    this.config.backend.url = url;
    this.saveConfig(this.config);
  }

  public enableBackend(enabled: boolean = true): void {
    this.config.backend.enabled = enabled;
    this.saveConfig(this.config);
  }

  public setAuthToken(token: string): void {
    this.config.auth.token = token;
    this.saveConfig(this.config);
  }

  public clearAuthToken(): void {
    delete this.config.auth.token;
    delete this.config.auth.user;
    this.saveConfig(this.config);
  }

  public setAuthUser(user: any): void {
    this.config.auth.user = user;
    this.saveConfig(this.config);
  }

  public isBackendEnabled(): boolean {
    return this.config.backend.enabled;
  }

  public getBackendUrl(): string {
    return this.config.backend.url;
  }

  public getAuthToken(): string | undefined {
    return this.config.auth.token;
  }

  public getAuthUser(): any {
    return this.config.auth.user;
  }

  // Generic get/set methods for nested properties
  public get(path: string): any {
    const keys = path.split('.');
    let current = this.config as any;

    for (const key of keys) {
      if (current && typeof current === 'object' && key in current) {
        current = current[key];
      } else {
        return undefined;
      }
    }

    return current;
  }

  public set(path: string, value: any): void {
    const keys = path.split('.');
    const lastKey = keys.pop()!;
    let current = this.config as any;

    for (const key of keys) {
      if (!current[key] || typeof current[key] !== 'object') {
        current[key] = {};
      }
      current = current[key];
    }

    current[lastKey] = value;
    this.saveConfig(this.config);
  }

  public delete(path: string): void {
    const keys = path.split('.');
    const lastKey = keys.pop()!;
    let current = this.config as any;

    for (const key of keys) {
      if (!current[key] || typeof current[key] !== 'object') {
        return; // Path doesn't exist
      }
      current = current[key];
    }

    delete current[lastKey];
    this.saveConfig(this.config);
  }

  // Project Management
  public addProject(project: Omit<ProjectContext, 'lastAccessed'>): void {
    const newProject: ProjectContext = {
      ...project,
      lastAccessed: new Date()
    };

    // Remove existing project with same path
    this.config.projects = this.config.projects.filter(p => p.path !== project.path);
    this.config.projects.push(newProject);
    this.saveConfig(this.config);
  }

  public setCurrentProject(projectPath: string): void {
    const project = this.config.projects.find(p => p.path === projectPath);
    if (project) {
      project.lastAccessed = new Date();
      this.config.currentProject = projectPath;
      this.saveConfig(this.config);
    }
  }

  public getCurrentProject(): ProjectContext | undefined {
    if (!this.config.currentProject) return undefined;
    return this.config.projects.find(p => p.path === this.config.currentProject);
  }

  public getRecentProjects(limit: number = 5): ProjectContext[] {
    return this.config.projects
      .sort((a, b) => b.lastAccessed.getTime() - a.lastAccessed.getTime())
      .slice(0, limit);
  }

  // Guidelines Management
  public updateUserGuidelines(guidelines: Partial<UserGuidelines>): void {
    this.config.userGuidelines = { ...this.config.userGuidelines, ...guidelines };
    this.saveConfig(this.config);
  }

  public getEffectiveGuidelines(): UserGuidelines {
    const currentProject = this.getCurrentProject();
    if (currentProject?.guidelines) {
      return {
        ...this.config.userGuidelines,
        ...currentProject.guidelines
      };
    }
    return this.config.userGuidelines;
  }

  public addCustomInstruction(instruction: string): void {
    if (!this.config.userGuidelines.customInstructions.includes(instruction)) {
      this.config.userGuidelines.customInstructions.push(instruction);
      this.saveConfig(this.config);
    }
  }

  public removeCustomInstruction(instruction: string): void {
    this.config.userGuidelines.customInstructions =
      this.config.userGuidelines.customInstructions.filter(i => i !== instruction);
    this.saveConfig(this.config);
  }

  // Configuration validation and migration
  private initializeMigrations(): void {
    this.migrations = [
      {
        version: '1.1.0',
        description: 'Add security settings',
        migrate: (config: any) => ({
          ...config,
          version: '1.1.0',
          security: {
            encryptApiKeys: false,
            configChecksum: undefined
          }
        })
      },
      {
        version: '1.2.0',
        description: 'Enhanced context engine configuration',
        migrate: (config: any) => ({
          ...config,
          version: '1.2.0',
          contextEngine: {
            ...config.contextEngine,
            maxSemanticDepth: config.contextEngine.maxSemanticDepth || 2,
            maxContextNodes: config.contextEngine.maxContextNodes || 50
          }
        })
      }
    ];
  }

  public validateConfig(config?: Config): ConfigValidationResult {
    const configToValidate = config || this.config;
    const result: ConfigValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
      suggestions: []
    };

    // Validate version
    if (!configToValidate.version) {
      result.errors.push('Configuration version is missing');
      result.isValid = false;
    }

    // Validate providers
    const enabledProviders = Object.values(configToValidate.providers).filter(p => p.enabled);
    if (enabledProviders.length === 0) {
      result.errors.push('No AI providers are enabled');
      result.isValid = false;
    }

    // Validate API keys for enabled providers
    for (const [name, provider] of Object.entries(configToValidate.providers)) {
      if (provider.enabled && !provider.apiKey && name !== 'ollama') {
        result.warnings.push(`${name} provider is enabled but has no API key`);
      }
    }

    // Validate default provider
    if (!configToValidate.providers[configToValidate.defaultProvider as keyof typeof configToValidate.providers]) {
      result.errors.push(`Default provider '${configToValidate.defaultProvider}' is not configured`);
      result.isValid = false;
    }

    // Validate codebase settings
    if (configToValidate.codebase.maxFileSize < 1024) {
      result.warnings.push('Max file size is very small, may exclude important files');
    }

    if (configToValidate.codebase.maxFiles < 10) {
      result.warnings.push('Max files limit is very low');
    }

    // Validate context engine
    if (configToValidate.contextEngine.enabled && !configToValidate.contextEngine.provider) {
      result.errors.push('Context engine is enabled but no provider is specified');
      result.isValid = false;
    }

    // Security validation
    if (configToValidate.security?.encryptApiKeys) {
      result.suggestions.push('API key encryption is enabled - ensure you have a secure master password');
    }

    // Performance suggestions
    if (configToValidate.contextEngine.cacheTTL < 60) {
      result.suggestions.push('Consider increasing context cache TTL for better performance');
    }

    return result;
  }

  public migrateConfig(config: any): Config {
    let migratedConfig = { ...config };
    const currentVersion = config.version || '1.0.0';

    // Apply migrations in order
    for (const migration of this.migrations) {
      if (this.isVersionNewer(migration.version, currentVersion)) {
        console.log(`Applying migration: ${migration.description}`);
        migratedConfig = migration.migrate(migratedConfig);
      }
    }

    return migratedConfig;
  }

  private isVersionNewer(version1: string, version2: string): boolean {
    const v1Parts = version1.split('.').map(Number);
    const v2Parts = version2.split('.').map(Number);

    for (let i = 0; i < Math.max(v1Parts.length, v2Parts.length); i++) {
      const v1Part = v1Parts[i] || 0;
      const v2Part = v2Parts[i] || 0;

      if (v1Part > v2Part) return true;
      if (v1Part < v2Part) return false;
    }

    return false;
  }

  public backupConfig(): string {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupPath = path.join(path.dirname(this.configPath), `config-backup-${timestamp}.yaml`);

    try {
      fs.copyFileSync(this.configPath, backupPath);
      return backupPath;
    } catch (error) {
      throw new Error(`Failed to backup config: ${error}`);
    }
  }

  public restoreConfig(backupPath: string): void {
    try {
      if (!fs.existsSync(backupPath)) {
        throw new Error('Backup file does not exist');
      }

      fs.copyFileSync(backupPath, this.configPath);
      this.config = this.loadConfig();
    } catch (error) {
      throw new Error(`Failed to restore config: ${error}`);
    }
  }

  public generateConfigChecksum(): string {
    const configContent = fs.readFileSync(this.configPath, 'utf8');
    return crypto.createHash('sha256').update(configContent).digest('hex');
  }

  public verifyConfigIntegrity(): boolean {
    if (!this.config.security?.configChecksum) {
      return true; // No checksum to verify
    }

    const currentChecksum = this.generateConfigChecksum();
    return currentChecksum === this.config.security.configChecksum;
  }

  public updateConfigChecksum(): void {
    const checksum = this.generateConfigChecksum();
    this.config.security = {
      ...this.config.security,
      configChecksum: checksum
    };
    this.saveConfig(this.config);
  }

  public exportConfig(exportPath?: string): string {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const defaultPath = exportPath || path.join(process.cwd(), `goc-config-export-${timestamp}.yaml`);

    try {
      // Create a sanitized version without sensitive data
      const sanitizedConfig = {
        ...this.config,
        providers: Object.fromEntries(
          Object.entries(this.config.providers).map(([name, provider]) => [
            name,
            {
              ...provider,
              apiKey: provider.apiKey ? '[REDACTED]' : undefined
            }
          ])
        )
      };

      const yamlContent = yaml.stringify(sanitizedConfig, { indent: 2 });
      fs.writeFileSync(defaultPath, yamlContent, 'utf8');
      return defaultPath;
    } catch (error) {
      throw new Error(`Failed to export config: ${error}`);
    }
  }

  public importConfig(importPath: string, mergeMode: boolean = false): void {
    try {
      if (!fs.existsSync(importPath)) {
        throw new Error('Import file does not exist');
      }

      const importContent = fs.readFileSync(importPath, 'utf8');
      const importedConfig = yaml.parse(importContent) as Partial<Config>;

      if (mergeMode) {
        // Merge with existing config
        this.config = this.mergeWithDefaults({ ...this.config, ...importedConfig });
      } else {
        // Replace entire config
        this.config = this.mergeWithDefaults(importedConfig);
      }

      this.saveConfig(this.config);
    } catch (error) {
      throw new Error(`Failed to import config: ${error}`);
    }
  }

  public resetToDefaults(): void {
    const backupPath = this.backupConfig();
    console.log(`Config backed up to: ${backupPath}`);

    this.config = this.getDefaultConfig();
    this.saveConfig(this.config);
  }

  public getConfigStats(): {
    version: string;
    enabledProviders: number;
    totalProjects: number;
    configSize: number;
    lastModified: Date;
  } {
    const stats = fs.statSync(this.configPath);
    const enabledProviders = Object.values(this.config.providers).filter(p => p.enabled).length;

    return {
      version: this.config.version,
      enabledProviders,
      totalProjects: this.config.projects.length,
      configSize: stats.size,
      lastModified: stats.mtime
    };
  }
}
