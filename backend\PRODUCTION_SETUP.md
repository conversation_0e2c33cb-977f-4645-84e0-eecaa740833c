# GOC Agent - Production Setup Guide

This guide covers the complete setup and deployment of the GOC Agent Laravel backend for production environments.

## 🏗️ Architecture Overview

The GOC Agent backend is a production-ready Laravel 12 application with:

- **Authentication**: <PERSON><PERSON>ze + <PERSON>A<PERSON> (Google, GitHub)
- **Subscriptions**: Stripe integration with multiple plans
- **API Management**: Secure API key generation and usage tracking
- **Admin Panel**: Complete customer and subscription management
- **Caching**: Redis for sessions, cache, and queues
- **Database**: PostgreSQL (recommended) or MySQL
- **Frontend**: Alpine.js + Tailwind CSS

## 📋 Prerequisites

### Server Requirements
- PHP 8.2+
- Composer 2.x
- Node.js 18+ & NPM
- PostgreSQL 14+ or MySQL 8.0+
- Redis 6.0+
- Nginx or Apache
- SSL Certificate

### Required PHP Extensions
```bash
php-fpm php-cli php-mysql php-pgsql php-redis php-curl php-json php-mbstring php-xml php-zip php-gd php-intl
```

## 🚀 Installation Steps

### 1. Clone Repository
```bash
git clone https://github.com/your-org/goc-agent.git
cd goc-agent/backend
```

### 2. Install Dependencies
```bash
composer install --no-dev --optimize-autoloader
npm ci --production
npm run build
```

### 3. Environment Configuration
```bash
cp .env.example .env
php artisan key:generate
```

### 4. Configure Environment Variables
```env
# Application
APP_NAME="GOC Agent"
APP_ENV=production
APP_DEBUG=false
APP_URL=https://your-domain.com

# Database
DB_CONNECTION=pgsql
DB_HOST=127.0.0.1
DB_PORT=5432
DB_DATABASE=goc_agent
DB_USERNAME=goc_agent_user
DB_PASSWORD=secure_password

# Redis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

# OAuth
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
GOOGLE_REDIRECT_URI=https://your-domain.com/auth/google/callback

GITHUB_CLIENT_ID=your_github_client_id
GITHUB_CLIENT_SECRET=your_github_client_secret
GITHUB_REDIRECT_URI=https://your-domain.com/auth/github/callback

# Stripe
STRIPE_KEY=pk_live_...
STRIPE_SECRET=sk_live_...
STRIPE_WEBHOOK_SECRET=whsec_...

# Mail
MAIL_MAILER=smtp
MAIL_HOST=smtp.mailgun.org
MAIL_PORT=587
MAIL_USERNAME=your_username
MAIL_PASSWORD=your_password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="GOC Agent"

# Queues
QUEUE_CONNECTION=redis

# Caching
CACHE_DRIVER=redis
SESSION_DRIVER=redis
```

### 5. Database Setup
```bash
# Create database and user
sudo -u postgres psql
CREATE DATABASE goc_agent;
CREATE USER goc_agent_user WITH PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE goc_agent TO goc_agent_user;
\q

# Run migrations and seeders
php artisan migrate --force
php artisan db:seed --class=SubscriptionPlanSeeder
php artisan db:seed --class=AdminUserSeeder
```

### 6. Optimize Application
```bash
php artisan config:cache
php artisan route:cache
php artisan view:cache
php artisan event:cache
```

### 7. Set Permissions
```bash
sudo chown -R www-data:www-data /var/www/goc-agent
sudo chmod -R 755 /var/www/goc-agent
sudo chmod -R 775 /var/www/goc-agent/storage
sudo chmod -R 775 /var/www/goc-agent/bootstrap/cache
```

## 🔧 Server Configuration

### Nginx Configuration
```nginx
server {
    listen 80;
    listen [::]:80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name your-domain.com;
    root /var/www/goc-agent/public;

    # SSL Configuration
    ssl_certificate /path/to/ssl/cert.pem;
    ssl_certificate_key /path/to/ssl/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;

    # Security Headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    index index.php;
    charset utf-8;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location = /favicon.ico { access_log off; log_not_found off; }
    location = /robots.txt  { access_log off; log_not_found off; }

    error_page 404 /index.php;

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.2-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }

    location ~ /\.(?!well-known).* {
        deny all;
    }
}
```

### Queue Worker (Supervisor)
```ini
[program:goc-agent-worker]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/goc-agent/artisan queue:work redis --sleep=3 --tries=3 --max-time=3600
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=www-data
numprocs=4
redirect_stderr=true
stdout_logfile=/var/www/goc-agent/storage/logs/worker.log
stopwaitsecs=3600
```

## 🔐 Security Checklist

- [ ] SSL certificate installed and configured
- [ ] Environment variables secured
- [ ] Database credentials rotated
- [ ] API keys and secrets secured
- [ ] File permissions set correctly
- [ ] Security headers configured
- [ ] Rate limiting enabled
- [ ] Firewall configured
- [ ] Regular backups scheduled
- [ ] Monitoring and alerting set up

## 📊 Monitoring & Maintenance

### Health Check Endpoint
```bash
curl https://your-domain.com/health
```

### Log Monitoring
```bash
tail -f storage/logs/laravel.log
```

### Performance Monitoring
- Monitor response times
- Track database query performance
- Monitor Redis memory usage
- Track API usage patterns

### Regular Maintenance
- Update dependencies monthly
- Rotate API keys quarterly
- Review and clean logs weekly
- Monitor disk usage
- Backup database daily

## 🚀 Deployment

Use the provided deployment script:
```bash
chmod +x deploy.sh
./deploy.sh
```

## 📞 Support

For production support and issues:
- Check logs in `storage/logs/`
- Review error monitoring dashboard
- Contact development team

## 🔄 Rollback Procedure

In case of deployment issues:
```bash
# Stop services
sudo systemctl stop nginx php8.2-fpm

# Restore from backup
cd /var/backups/goc-agent
tar -xzf goc-agent-backup-TIMESTAMP.tar.gz -C /var/www/goc-agent

# Restart services
sudo systemctl start php8.2-fpm nginx
```
