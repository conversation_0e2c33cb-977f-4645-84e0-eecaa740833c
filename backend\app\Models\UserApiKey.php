<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserApiKey extends Model
{
    protected $fillable = [
        'user_id',
        'name',
        'key_hash',
        'key_prefix',
        'permissions',
        'requests_today',
        'requests_month',
        'total_requests',
        'last_used_at',
        'last_used_ip',
        'is_active',
        'expires_at',
    ];

    protected function casts(): array
    {
        return [
            'permissions' => 'array',
            'is_active' => 'boolean',
            'last_used_at' => 'datetime',
            'expires_at' => 'datetime',
        ];
    }

    /**
     * Get the user that owns the API key.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Check if the API key is active.
     */
    public function isActive(): bool
    {
        return $this->is_active &&
               (!$this->expires_at || $this->expires_at->isFuture());
    }

    /**
     * Check if the API key has a specific permission.
     */
    public function hasPermission(string $permission): bool
    {
        if (!$this->permissions) {
            return true; // No restrictions
        }

        return in_array($permission, $this->permissions);
    }

    /**
     * Get usage statistics.
     */
    public function getUsageStats(): array
    {
        return [
            'requests_today' => $this->requests_today,
            'requests_month' => $this->requests_month,
            'total_requests' => $this->total_requests,
            'last_used_at' => $this->last_used_at,
            'last_used_ip' => $this->last_used_ip,
        ];
    }
}
