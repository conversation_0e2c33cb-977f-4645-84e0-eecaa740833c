// Hybrid Context Builder - Combines GOC's fast scanning with external semantic intelligence

import { ContextBuilder } from '../commands/ContextBuilder';
import { CodebaseAnalyzer, FileInfo } from '../codebase/CodebaseAnalyzer';
import { Logger } from '../utils/Logger';
import { IContextEngine, ContextQuery, EnhancedContext, ContextEngineConfig } from './IContextEngine';
import { ContextEngineFactory } from './ContextEngineFactory';

export interface HybridContextConfig {
  useSemanticEngine: boolean;
  fallbackToBasic: boolean;
  semanticThreshold: number; // When to use semantic vs basic (based on query complexity)
  maxSemanticProcessingTime: number; // ms
  cacheEnabled: boolean;
}

export class HybridContextBuilder extends ContextBuilder {
  private semanticEngine?: IContextEngine;
  private hybridConfig: HybridContextConfig;
  private contextCache: Map<string, { context: string; timestamp: Date }> = new Map();
  private factory: ContextEngineFactory;

  constructor(
    codebaseAnalyzer: CodebaseAnalyzer,
    logger: Logger,
    hybridConfig: HybridContextConfig,
    semanticConfig?: ContextEngineConfig
  ) {
    super(codebaseAnalyzer, logger);
    this.hybridConfig = hybridConfig;
    this.factory = new ContextEngineFactory(logger, codebaseAnalyzer);
    
    if (hybridConfig.useSemanticEngine && semanticConfig) {
      this.initializeSemanticEngine(semanticConfig);
    }
  }

  private async initializeSemanticEngine(config: ContextEngineConfig): Promise<void> {
    try {
      this.logger.info('🧠 Initializing semantic context engine...');
      this.semanticEngine = await this.factory.createEngine(config.provider, config);
      
      if (await this.semanticEngine.isAvailable()) {
        this.logger.success('✅ Semantic context engine ready');
      } else {
        this.logger.warn('⚠️ Semantic engine not available, falling back to basic context');
        this.semanticEngine = undefined;
      }
    } catch (error) {
      this.logger.error(`❌ Failed to initialize semantic engine: ${error}`);
      if (!this.hybridConfig.fallbackToBasic) {
        throw error;
      }
    }
  }

  // Enhanced context building with semantic intelligence
  public async buildEnhancedContext(query?: string, taskType?: string): Promise<string> {
    const startTime = Date.now();
    
    // Determine if we should use semantic engine
    const useSemanticEngine = this.shouldUseSemanticEngine(query, taskType);
    
    if (useSemanticEngine && this.semanticEngine) {
      try {
        return await this.buildSemanticContext(query, taskType);
      } catch (error) {
        this.logger.warn(`Semantic context failed, falling back to basic: ${error}`);
        if (!this.hybridConfig.fallbackToBasic) {
          throw error;
        }
      }
    }
    
    // Fallback to basic context
    const basicContext = await this.buildContext();
    this.logger.debug(`Context built in ${Date.now() - startTime}ms (basic)`);
    return basicContext;
  }

  private async buildSemanticContext(query?: string, taskType?: string): Promise<string> {
    const startTime = Date.now();
    
    if (!this.semanticEngine) {
      throw new Error('Semantic engine not available');
    }

    // Check cache first
    const cacheKey = this.generateCacheKey(query, taskType);
    if (this.hybridConfig.cacheEnabled && this.contextCache.has(cacheKey)) {
      const cached = this.contextCache.get(cacheKey)!;
      const age = Date.now() - cached.timestamp.getTime();
      if (age < 300000) { // 5 minutes cache
        this.logger.debug('📋 Using cached semantic context');
        return cached.context;
      }
    }

    // Build semantic query
    const contextQuery: ContextQuery = {
      type: this.determineQueryType(query, taskType),
      query: query || 'general codebase context',
      taskContext: taskType,
      maxResults: 20,
      includeRelated: true,
      semanticDepth: 2
    };

    // Get enhanced context from semantic engine
    const enhancedContext = await this.semanticEngine.buildEnhancedContext(contextQuery);
    
    // Combine with basic context structure
    const finalContext = await this.combineContexts(enhancedContext, query, taskType);
    
    // Cache the result
    if (this.hybridConfig.cacheEnabled) {
      this.contextCache.set(cacheKey, {
        context: finalContext,
        timestamp: new Date()
      });
    }

    const processingTime = Date.now() - startTime;
    this.logger.debug(`Semantic context built in ${processingTime}ms`);
    this.logger.debug(`Context includes ${enhancedContext.semanticContext.relatedFunctions.length} functions, ${enhancedContext.semanticContext.relatedClasses.length} classes`);
    
    return finalContext;
  }

  private async combineContexts(enhancedContext: EnhancedContext, query?: string, taskType?: string): Promise<string> {
    const { primaryContent, semanticContext, relevanceScores } = enhancedContext;
    
    let context = `You are an intelligent coding assistant with deep semantic understanding of this codebase:

## Codebase Overview
${primaryContent}

## Semantic Analysis
- **Architectural Patterns**: ${semanticContext.architecturalPatterns.join(', ') || 'None detected'}
- **Code Patterns**: ${semanticContext.codePatterns.join(', ') || 'Standard patterns'}
- **Related Functions**: ${semanticContext.relatedFunctions.length} identified
- **Related Classes**: ${semanticContext.relatedClasses.length} identified
- **Dependencies**: ${semanticContext.dependencies.length} analyzed
`;

    // Add most relevant files with semantic scoring
    if (relevanceScores.length > 0) {
      context += `\n## Most Relevant Files (Semantic Analysis)\n`;
      
      for (const score of relevanceScores.slice(0, 10)) {
        context += `\n### ${score.filePath} (Relevance: ${(score.score * 100).toFixed(1)}%)\n`;
        context += `**Why relevant**: ${score.reasons.join(', ')}\n`;
        
        if (score.semanticMatches.length > 0) {
          context += `**Semantic matches**: ${score.semanticMatches.length} found\n`;
        }
      }
    }

    // Add semantic relationships
    if (semanticContext.relatedFunctions.length > 0) {
      context += `\n## Key Functions\n`;
      for (const func of semanticContext.relatedFunctions.slice(0, 5)) {
        context += `- **${func.name}** in ${func.path}:${func.startLine}\n`;
      }
    }

    if (semanticContext.relatedClasses.length > 0) {
      context += `\n## Key Classes\n`;
      for (const cls of semanticContext.relatedClasses.slice(0, 5)) {
        context += `- **${cls.name}** in ${cls.path}:${cls.startLine}\n`;
      }
    }

    // Add task-specific context
    if (query || taskType) {
      context += `\n## Task Context\n`;
      if (taskType) context += `**Task Type**: ${taskType}\n`;
      if (query) context += `**Query**: ${query}\n`;
    }

    return context;
  }

  private shouldUseSemanticEngine(query?: string, taskType?: string): boolean {
    if (!this.hybridConfig.useSemanticEngine || !this.semanticEngine) {
      return false;
    }

    // Use semantic engine for complex queries
    if (query && query.length > 50) return true;
    if (taskType && ['refactor', 'analyze', 'optimize', 'debug'].includes(taskType)) return true;
    
    // Use for architectural questions
    if (query && /\b(architecture|pattern|design|structure|relationship)\b/i.test(query)) return true;
    
    return false;
  }

  private determineQueryType(query?: string, taskType?: string): 'general' | 'file-specific' | 'task-specific' | 'semantic-search' {
    if (taskType) return 'task-specific';
    if (query && query.includes('.')) return 'file-specific';
    if (query && query.length > 20) return 'semantic-search';
    return 'general';
  }

  private generateCacheKey(query?: string, taskType?: string): string {
    return `${query || 'general'}_${taskType || 'none'}_${Date.now().toString().slice(0, -5)}`;
  }

  // Enhanced file context with semantic relationships
  public async buildSemanticFileContext(filePath: string): Promise<string> {
    if (!this.semanticEngine) {
      return super.buildFileContext(filePath);
    }

    try {
      const query: ContextQuery = {
        type: 'file-specific',
        query: `analyze file ${filePath}`,
        filePath,
        includeRelated: true,
        semanticDepth: 1
      };

      const enhancedContext = await this.semanticEngine.buildEnhancedContext(query);
      return this.combineContexts(enhancedContext, `file analysis: ${filePath}`, 'file-analysis');
    } catch (error) {
      this.logger.warn(`Semantic file context failed: ${error}`);
      return super.buildFileContext(filePath);
    }
  }

  // Get semantic engine stats
  public async getSemanticStats(): Promise<any> {
    if (!this.semanticEngine) {
      return { available: false };
    }

    try {
      const stats = await this.semanticEngine.getStats();
      return {
        available: true,
        ...stats,
        cacheSize: this.contextCache.size
      };
    } catch (error) {
      return { available: false, error: error instanceof Error ? error.message : String(error) };
    }
  }

  // Clear all caches
  public async clearCaches(): Promise<void> {
    this.contextCache.clear();
    if (this.semanticEngine) {
      await this.semanticEngine.clearCache();
    }
  }
}
