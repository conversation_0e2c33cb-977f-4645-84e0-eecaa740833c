import fs from 'fs';
import path from 'path';
import os from 'os';
import { Logger } from '../utils/Logger';
import { MemoryManager, Experience } from '../agent/MemoryManager';
import { TrainingManager } from './TrainingManager';
import { CodebaseAnalyzer } from '../codebase/CodebaseAnalyzer';
import { ChangeTracker } from '../utils/ChangeTracker';

export interface IntelligenceMetrics {
  totalExperiences: number;
  successRate: number;
  averageTaskCompletionTime: number;
  mostSuccessfulPatterns: string[];
  improvementAreas: string[];
  learningVelocity: number;
  adaptabilityScore: number;
}

export interface LearningGoal {
  id: string;
  description: string;
  targetMetric: string;
  currentValue: number;
  targetValue: number;
  deadline?: Date;
  priority: 'low' | 'medium' | 'high';
  status: 'active' | 'completed' | 'paused';
}

export interface KnowledgeArea {
  domain: string;
  proficiencyLevel: number; // 0-100
  experienceCount: number;
  lastUpdated: Date;
  keyPatterns: string[];
  improvementSuggestions: string[];
}

export class IntelligenceEngine {
  private logger: Logger;
  private memoryManager: MemoryManager;
  private trainingManager: TrainingManager;
  private codebaseAnalyzer: CodebaseAnalyzer;
  private changeTracker: ChangeTracker;
  private intelligencePath: string;
  private learningGoals: LearningGoal[] = [];
  private knowledgeAreas: Map<string, KnowledgeArea> = new Map();
  private metrics: IntelligenceMetrics;

  constructor(
    logger: Logger,
    memoryManager: MemoryManager,
    trainingManager: TrainingManager,
    codebaseAnalyzer: CodebaseAnalyzer,
    changeTracker: ChangeTracker
  ) {
    this.logger = logger;
    this.memoryManager = memoryManager;
    this.trainingManager = trainingManager;
    this.codebaseAnalyzer = codebaseAnalyzer;
    this.changeTracker = changeTracker;
    this.intelligencePath = path.join(os.homedir(), '.goc-agent', 'intelligence');
    this.ensureIntelligenceDirectory();
    this.loadIntelligenceData();
    this.metrics = this.calculateCurrentMetrics();
  }

  public async startContinuousLearning(): Promise<void> {
    // Debug: Starting continuous learning engine
    // this.logger.info('🧠 Starting continuous learning engine...');

    // Analyze recent experiences to identify learning patterns
    await this.analyzeRecentExperiences();

    // Update knowledge areas based on new experiences
    await this.updateKnowledgeAreas();

    // Optimize learning goals for better performance
    await this.optimizeLearningGoals();

    // Generate improvement strategies based on analysis
    await this.generateImprovementStrategies();

    // Update intelligence metrics for tracking progress
    this.metrics = this.calculateCurrentMetrics();

    // Save intelligence data for persistence
    this.saveIntelligenceData();

    // Debug: Continuous learning cycle completed
    // this.logger.success('✅ Continuous learning cycle completed');
  }

  public async analyzePerformanceTrends(): Promise<void> {
    this.logger.info('📊 Analyzing performance trends...');
    
    const experiences = this.memoryManager.getRecentExperiences(100);
    const timeWindows = this.groupExperiencesByTimeWindow(experiences);
    
    for (const [period, periodExperiences] of timeWindows) {
      const successRate = this.calculateSuccessRate(periodExperiences);
      const avgTime = this.calculateAverageTime(periodExperiences);
      
      this.logger.info(`📈 ${period}: ${successRate.toFixed(1)}% success, ${avgTime.toFixed(1)}s avg time`);
    }
    
    // Identify trends
    const trends = this.identifyPerformanceTrends(timeWindows);
    this.logger.info('🔍 Performance trends:');
    trends.forEach(trend => this.logger.info(`  • ${trend}`));
  }

  public async adaptBehaviorBasedOnContext(): Promise<void> {
    this.logger.info('🎯 Adapting behavior based on context...');
    
    // Analyze current project context
    const projectContext = await this.analyzeProjectContext();
    
    // Get relevant knowledge area
    const relevantKnowledge = this.getRelevantKnowledgeArea(projectContext);
    
    // Adapt strategies based on context
    const adaptations = this.generateContextualAdaptations(projectContext, relevantKnowledge);
    
    this.logger.info('🔧 Behavioral adaptations:');
    adaptations.forEach(adaptation => this.logger.info(`  • ${adaptation}`));
    
    // Apply adaptations
    await this.applyBehavioralAdaptations(adaptations);
  }

  public async generateIntelligenceReport(): Promise<string> {
    this.logger.info('📋 Generating intelligence report...');
    
    const report = {
      timestamp: new Date(),
      metrics: this.metrics,
      knowledgeAreas: Array.from(this.knowledgeAreas.entries()),
      learningGoals: this.learningGoals,
      recentImprovements: this.getRecentImprovements(),
      recommendations: this.generateRecommendations()
    };
    
    const reportPath = path.join(this.intelligencePath, `intelligence-report-${Date.now()}.json`);
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    this.logger.success(`📊 Intelligence report saved: ${reportPath}`);
    return reportPath;
  }

  public async setLearningGoal(description: string, targetMetric: string, targetValue: number, priority: 'low' | 'medium' | 'high' = 'medium'): Promise<void> {
    const goal: LearningGoal = {
      id: this.generateId(),
      description,
      targetMetric,
      currentValue: this.getCurrentMetricValue(targetMetric),
      targetValue,
      priority,
      status: 'active'
    };
    
    this.learningGoals.push(goal);
    this.saveIntelligenceData();
    
    this.logger.success(`🎯 Learning goal set: ${description}`);
  }

  public displayIntelligenceStatus(): void {
    this.logger.info('\n🧠 Intelligence Status:');
    this.logger.info(`📊 Success Rate: ${this.metrics.successRate.toFixed(1)}%`);
    this.logger.info(`⚡ Learning Velocity: ${this.metrics.learningVelocity.toFixed(2)}`);
    this.logger.info(`🎯 Adaptability Score: ${this.metrics.adaptabilityScore.toFixed(1)}`);
    this.logger.info(`📚 Knowledge Areas: ${this.knowledgeAreas.size}`);
    this.logger.info(`🎯 Active Goals: ${this.learningGoals.filter(g => g.status === 'active').length}`);
    
    // Show top knowledge areas
    const topAreas = Array.from(this.knowledgeAreas.entries())
      .sort(([,a], [,b]) => b.proficiencyLevel - a.proficiencyLevel)
      .slice(0, 3);
    
    this.logger.info('\n🏆 Top Knowledge Areas:');
    topAreas.forEach(([domain, area]) => {
      this.logger.info(`  • ${domain}: ${area.proficiencyLevel}% proficiency`);
    });
  }

  private async analyzeRecentExperiences(): Promise<void> {
    const recentExperiences = this.memoryManager.getRecentExperiences(50);
    
    for (const experience of recentExperiences) {
      // Extract learning from each experience
      await this.extractLearningFromExperience(experience);
      
      // Update knowledge areas
      this.updateKnowledgeAreaFromExperience(experience);
    }
  }

  private async updateKnowledgeAreas(): Promise<void> {
    const experiences = this.memoryManager.getAllExperiences();
    const domains = this.extractDomains(experiences);
    
    for (const domain of domains) {
      const domainExperiences = experiences.filter(exp => this.belongsToDomain(exp, domain));
      const proficiency = this.calculateDomainProficiency(domainExperiences);
      
      this.knowledgeAreas.set(domain, {
        domain,
        proficiencyLevel: proficiency,
        experienceCount: domainExperiences.length,
        lastUpdated: new Date(),
        keyPatterns: this.extractKeyPatterns(domainExperiences),
        improvementSuggestions: this.generateDomainImprovements(domainExperiences)
      });
    }
  }

  private calculateCurrentMetrics(): IntelligenceMetrics {
    const experiences = this.memoryManager.getAllExperiences();
    const recentExperiences = this.memoryManager.getRecentExperiences(100);
    
    return {
      totalExperiences: experiences.length,
      successRate: this.calculateSuccessRate(experiences),
      averageTaskCompletionTime: this.calculateAverageTime(experiences),
      mostSuccessfulPatterns: this.getMostSuccessfulPatterns(),
      improvementAreas: this.identifyImprovementAreas(),
      learningVelocity: this.calculateLearningVelocity(recentExperiences),
      adaptabilityScore: this.calculateAdaptabilityScore()
    };
  }

  private ensureIntelligenceDirectory(): void {
    if (!fs.existsSync(this.intelligencePath)) {
      fs.mkdirSync(this.intelligencePath, { recursive: true });
    }
  }

  private loadIntelligenceData(): void {
    try {
      const dataPath = path.join(this.intelligencePath, 'intelligence.json');
      if (fs.existsSync(dataPath)) {
        const data = JSON.parse(fs.readFileSync(dataPath, 'utf8'));
        this.learningGoals = data.learningGoals || [];
        this.knowledgeAreas = new Map(data.knowledgeAreas || []);
      }
    } catch (error) {
      this.logger.debug(`Failed to load intelligence data: ${error}`);
    }
  }

  private saveIntelligenceData(): void {
    try {
      const data = {
        learningGoals: this.learningGoals,
        knowledgeAreas: Array.from(this.knowledgeAreas.entries()),
        lastUpdated: new Date()
      };
      
      const dataPath = path.join(this.intelligencePath, 'intelligence.json');
      fs.writeFileSync(dataPath, JSON.stringify(data, null, 2));
    } catch (error) {
      this.logger.error(`Failed to save intelligence data: ${error}`);
    }
  }

  // Helper methods for calculations
  private calculateSuccessRate(experiences: Experience[]): number {
    if (experiences.length === 0) return 0;
    const successes = experiences.filter(exp => exp.outcome === 'success').length;
    return (successes / experiences.length) * 100;
  }

  private calculateAverageTime(experiences: Experience[]): number {
    // This would need to be implemented based on how you track timing
    return 0; // Placeholder
  }

  private generateId(): string {
    return Math.random().toString(36).substr(2, 9);
  }

  // Additional helper methods would be implemented here...
  private groupExperiencesByTimeWindow(experiences: Experience[]): Map<string, Experience[]> {
    // Implementation for grouping experiences by time windows
    return new Map();
  }

  private identifyPerformanceTrends(timeWindows: Map<string, Experience[]>): string[] {
    // Implementation for identifying trends
    return [];
  }

  private async analyzeProjectContext(): Promise<any> {
    // Implementation for analyzing current project context
    return {};
  }

  private getRelevantKnowledgeArea(context: any): KnowledgeArea | undefined {
    // Implementation for finding relevant knowledge area
    return undefined;
  }

  private generateContextualAdaptations(context: any, knowledge?: KnowledgeArea): string[] {
    // Implementation for generating adaptations
    return [];
  }

  private async applyBehavioralAdaptations(adaptations: string[]): Promise<void> {
    // Implementation for applying adaptations
  }

  private getRecentImprovements(): string[] {
    // Implementation for getting recent improvements
    return [];
  }

  private generateRecommendations(): string[] {
    // Implementation for generating recommendations
    return [];
  }

  private getCurrentMetricValue(metric: string): number {
    // Implementation for getting current metric value
    return 0;
  }

  private async extractLearningFromExperience(experience: Experience): Promise<void> {
    // Implementation for extracting learning
  }

  private updateKnowledgeAreaFromExperience(experience: Experience): void {
    // Implementation for updating knowledge areas
  }

  private extractDomains(experiences: Experience[]): string[] {
    // Implementation for extracting domains
    return [];
  }

  private belongsToDomain(experience: Experience, domain: string): boolean {
    // Implementation for checking domain membership
    return false;
  }

  private calculateDomainProficiency(experiences: Experience[]): number {
    // Implementation for calculating proficiency
    return 0;
  }

  private extractKeyPatterns(experiences: Experience[]): string[] {
    // Implementation for extracting patterns
    return [];
  }

  private generateDomainImprovements(experiences: Experience[]): string[] {
    // Implementation for generating improvements
    return [];
  }

  private getMostSuccessfulPatterns(): string[] {
    // Implementation for getting successful patterns
    return [];
  }

  private identifyImprovementAreas(): string[] {
    // Implementation for identifying improvement areas
    return [];
  }

  private calculateLearningVelocity(experiences: Experience[]): number {
    // Implementation for calculating learning velocity
    return 0;
  }

  private calculateAdaptabilityScore(): number {
    // Implementation for calculating adaptability
    return 0;
  }

  private async optimizeLearningGoals(): Promise<void> {
    // Check progress on active learning goals
    const activeGoals = this.learningGoals.filter(goal => goal.status === 'active');

    for (const goal of activeGoals) {
      const currentValue = this.getCurrentMetricValue(goal.targetMetric);
      goal.currentValue = currentValue;

      // Check if goal is completed
      if (currentValue >= goal.targetValue) {
        goal.status = 'completed';
        this.logger.success(`🎯 Learning goal completed: ${goal.description}`);
      }
    }
  }

  private async generateImprovementStrategies(): Promise<void> {
    // Analyze areas that need improvement
    const improvementAreas = this.identifyImprovementAreas();

    for (const area of improvementAreas) {
      // Generate specific strategies for each area
      const strategies = this.generateStrategiesForArea(area);
      this.logger.info(`💡 Improvement strategies for ${area}:`);
      strategies.forEach(strategy => this.logger.info(`  • ${strategy}`));
    }
  }

  private generateStrategiesForArea(area: string): string[] {
    // Generate specific improvement strategies based on the area
    const strategies: string[] = [];

    switch (area.toLowerCase()) {
      case 'success rate':
        strategies.push('Analyze failed experiences for common patterns');
        strategies.push('Improve error handling and validation');
        strategies.push('Enhance context understanding');
        break;
      case 'task completion time':
        strategies.push('Optimize action selection algorithms');
        strategies.push('Cache frequently used patterns');
        strategies.push('Improve planning efficiency');
        break;
      case 'learning velocity':
        strategies.push('Increase experience sampling frequency');
        strategies.push('Enhance pattern recognition algorithms');
        strategies.push('Improve feedback processing');
        break;
      default:
        strategies.push('Collect more data in this area');
        strategies.push('Analyze successful patterns');
        break;
    }

    return strategies;
  }
}
