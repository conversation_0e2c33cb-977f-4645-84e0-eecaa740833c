import inquirer from 'inquirer';
import chalk from 'chalk';
import boxen from 'boxen';
import fs from 'fs';
import Table from 'cli-table3';
import path from 'path';
import { ConfigManager, UserGuidelines, ProjectContext } from '../config/ConfigManager';
import { CodebaseAnalyzer } from '../codebase/CodebaseAnalyzer';
import { AIProviderManager, ChatMessage } from '../ai/AIProviderManager';
import { Logger } from '../utils/Logger';
import { ClaudeUI } from '../utils/ClaudeUI';
import { ContextBuilder } from './ContextBuilder';
import { HybridContextBuilder } from '../context/HybridContextBuilder';
import { FileEditor } from './FileEditor';
import { ChangeTracker } from '../utils/ChangeTracker';
import { ChangeManager } from './ChangeManager';
import { SelfTrainingFactory, SelfTrainingComponents } from '../training/SelfTrainingFactory';
import { MemoryManager } from '../agent/MemoryManager';
import { IntelligenceEngine } from '../training/IntelligenceEngine';
import { AutoTrainingEngine } from '../training/AutoTrainingEngine';
import { TrainingCommandHandler } from './TrainingCommandHandler';
import { WebBrowser } from '../web/WebBrowser';
import { WebLearningEngine } from '../web/WebLearningEngine';
import { AgentCore } from '../agent/AgentCore';
import { TrainingManager } from '../training/TrainingManager';
import { GuidelinesManager } from '../agent/GuidelinesManager';
import { PerformanceOptimizer } from '../utils/PerformanceOptimizer';
import { TestFramework } from '../testing/TestFramework';
import { ErrorRecoveryManager } from '../utils/ErrorRecoveryManager';
import { DocumentationGenerator } from '../documentation/DocumentationGenerator';
import { CodebaseHealthMonitor } from '../monitoring/CodebaseHealthMonitor';
import { TechnologyTrainingCommand, TechnologyTrainingConfig } from './TechnologyTrainingCommand';

export class CommandHandler {
  private config: ConfigManager;
  private codebaseAnalyzer: CodebaseAnalyzer;
  private aiManager: AIProviderManager;
  private logger: Logger;
  private ui: ClaudeUI;
  private contextBuilder: ContextBuilder;
  private hybridContextBuilder?: HybridContextBuilder;
  private fileEditor: FileEditor;
  private changeTracker: ChangeTracker;
  private changeManager: ChangeManager;
  private intelligenceEngine: IntelligenceEngine;
  private autoTrainingEngine: AutoTrainingEngine;
  private trainingCommandHandler: TrainingCommandHandler;
  private webBrowser: WebBrowser;
  private webLearningEngine: WebLearningEngine;
  private agentCore: AgentCore;
  private memoryManager: MemoryManager;
  private trainingManager: TrainingManager;
  private guidelinesManager: GuidelinesManager;
  private performanceOptimizer: PerformanceOptimizer;
  private testFramework: TestFramework;
  private errorRecoveryManager: ErrorRecoveryManager;
  private documentationGenerator: DocumentationGenerator;
  private healthMonitor: CodebaseHealthMonitor;
  private selfTrainingComponents?: SelfTrainingComponents;
  private technologyTrainingCommand: TechnologyTrainingCommand;

  // Session tracking to avoid redundant session info display
  private lastSessionInfo: { provider?: string; model?: string; operation?: string; contextMode?: string } = {};

  // Dynamic context mode tracking
  private currentContextMode: 'Basic' | 'Hybrid-Semantic' | 'Hybrid-Basic' = 'Basic';

  constructor(
    config: ConfigManager,
    codebaseAnalyzer: CodebaseAnalyzer,
    aiManager: AIProviderManager,
    logger: Logger
  ) {
    this.config = config;
    this.codebaseAnalyzer = codebaseAnalyzer;
    this.aiManager = aiManager;
    this.logger = logger;
    this.ui = new ClaudeUI();

    // Initialize memory manager
    this.memoryManager = new MemoryManager(logger);

    // Initialize change tracking
    this.changeTracker = new ChangeTracker(logger);
    this.changeManager = new ChangeManager(this.changeTracker, logger);

    // Initialize self-training by default
    this.initializeSelfTraining();

    this.contextBuilder = new ContextBuilder(codebaseAnalyzer, logger);

    // Don't initialize hybrid context builder here - do it lazily when needed
    this.fileEditor = new FileEditor(logger, this.changeTracker);
    this.trainingManager = new TrainingManager(logger, this.memoryManager, codebaseAnalyzer);
    this.guidelinesManager = new GuidelinesManager(config, logger);

    // Initialize performance optimizer and testing
    this.performanceOptimizer = new PerformanceOptimizer(logger);
    this.testFramework = new TestFramework(logger, config, aiManager, codebaseAnalyzer);

    // Initialize advanced features
    this.errorRecoveryManager = new ErrorRecoveryManager(logger, aiManager, config);
    this.documentationGenerator = new DocumentationGenerator(
      logger,
      codebaseAnalyzer,
      aiManager,
      {
        outputDir: './docs',
        format: 'markdown',
        includePrivate: false,
        includeTests: true,
        generateIndex: true,
        autoUpdate: false
      }
    );
    this.healthMonitor = new CodebaseHealthMonitor(logger, codebaseAnalyzer, aiManager);

    // Initialize intelligence engine
    this.intelligenceEngine = new IntelligenceEngine(
      logger,
      this.memoryManager,
      this.trainingManager,
      codebaseAnalyzer,
      this.changeTracker
    );

    // Initialize auto-training engine
    this.autoTrainingEngine = new AutoTrainingEngine(
      logger,
      this.intelligenceEngine,
      this.memoryManager,
      this.trainingManager
    );

    this.trainingCommandHandler = new TrainingCommandHandler(
      logger,
      this.intelligenceEngine,
      this.trainingManager,
      this.memoryManager
    );

    // Initialize web browsing and learning
    this.webBrowser = new WebBrowser(logger);
    this.webLearningEngine = new WebLearningEngine(
      logger,
      this.webBrowser,
      this.autoTrainingEngine,
      this.memoryManager,
      aiManager
    );

    // Connect auto-training to change tracker
    this.changeTracker.setAutoTrainingCallback(
      (operation, filePath, success) => this.autoTrainingEngine.onFileOperation(operation, filePath, success)
    );

    this.agentCore = new AgentCore(aiManager, codebaseAnalyzer, this.contextBuilder, this.fileEditor, logger);

    // Initialize technology training command
    this.technologyTrainingCommand = new TechnologyTrainingCommand(logger);
  }

  private showSessionInfo(operation?: string, providerName?: string, model?: string): void {
    // Get session info from AI manager or use provided parameters
    const sessionInfo = this.aiManager.getSessionInfo();
    const config = this.config.getConfig();

    // Determine provider
    let provider = sessionInfo.provider || providerName;
    if (!provider) {
      provider = config.defaultProvider;
    }

    // Determine model
    let actualModel = sessionInfo.model || model;
    if (!actualModel && provider) {
      const providerConfig = config.providers[provider as keyof typeof config.providers];
      actualModel = providerConfig?.defaultModel || 'Default';
    }

    if (provider) {
      // Determine current context mode display
      const contextDisplay = this.getContextModeDisplay();

      // Check if session info has changed or if this is the first time
      const currentSessionInfo = {
        provider,
        model: actualModel || 'Default',
        operation: operation || 'Unknown',
        contextMode: contextDisplay
      };

      const hasChanged =
        this.lastSessionInfo.provider !== currentSessionInfo.provider ||
        this.lastSessionInfo.model !== currentSessionInfo.model ||
        this.lastSessionInfo.contextMode !== currentSessionInfo.contextMode ||
        !this.lastSessionInfo.provider; // First time

      // Only show full session info if provider/model/context changed or first time
      if (hasChanged) {
        console.log(this.ui.sessionInfo(
          provider,
          actualModel || 'Default',
          contextDisplay
        ));
      }

      // Always show the current operation
      if (operation) {
        console.log(this.ui.status('info', `Operation: ${operation}`));
        console.log('');
      }

      // Update last session info
      this.lastSessionInfo = currentSessionInfo;
    }
  }

  public async handleConfig(): Promise<void> {
    const choices = [
      'View current configuration',
      'Configure AI providers',
      'Set default provider',
      '🤖 Browse and select AI models',
      'Configure codebase settings',
      'Exit'
    ];

    const { action } = await inquirer.prompt([
      {
        type: 'list',
        name: 'action',
        message: 'What would you like to configure?',
        choices
      }
    ]);

    switch (action) {
      case 'View current configuration':
        await this.showCurrentConfig();
        break;
      case 'Configure AI providers':
        await this.configureProviders();
        break;
      case 'Set default provider':
        await this.setDefaultProvider();
        break;
      case '🤖 Browse and select AI models':
        await this.handleModelSelection();
        break;
      case 'Configure codebase settings':
        await this.configureCodebase();
        break;
      case 'Exit':
        process.exit(0);
        break;
    }
  }

  public async handleChat(options: any): Promise<void> {
    return await this.performanceOptimizer.withPerformanceTracking('chat_session', async () => {
      const providerName = options.provider;
      const model = options.model;

      // Enable quiet mode for professional chat experience
      this.logger.setSuppressInitLogs(true);
      this.logger.setUserFriendlyMode(true);

      // Check if provider is available
      if (providerName) {
        const provider = this.aiManager.getProvider(providerName);
        if (!provider) {
          console.log(this.ui.error('Provider Error', `Provider '${providerName}' not found or not configured`));
          return;
        }
      }

      // Show header
      console.log(this.ui.header('Interactive Chat Session', 'Type "exit" to quit, "help" for commands'));

      const messages: ChatMessage[] = [];

      // Start with a generic system prompt - we'll add context dynamically based on questions
      const systemPrompt = this.buildGOCAgentSystemPrompt(false);

      messages.push({
        role: 'system',
        content: systemPrompt
      });

      // Show session info
      this.showSessionInfo('Interactive Chat', providerName, model);

      // Keep logs suppressed during the entire chat session for cleaner UI
      // this.logger.setSuppressInitLogs(false);

    while (true) {
      const { input } = await inquirer.prompt([
        {
          type: 'input',
          name: 'input',
          message: chalk.cyan('You:'),
        }
      ]);

      if (input.toLowerCase() === 'exit') {
        break;
      }

      if (input.toLowerCase() === 'help') {
        this.showChatHelp();
        continue;
      }

      if (input.toLowerCase() === 'changes') {
        this.changeTracker.displayChangesSummary();
        continue;
      }

      if (input.trim() === '') {
        continue;
      }

      // Check for web content in user input
      const webResult = await this.webLearningEngine.processUserInput(input);

      let userMessage = input;
      if (webResult.hasWebContent && webResult.webData) {
        // Enhance user message with web context
        const webContext = webResult.webData.map(content =>
          `Web Source: ${content.title}\nURL: ${content.url}\nSummary: ${content.summary}`
        ).join('\n\n');

        userMessage = `${input}\n\nWeb Context:\n${webContext}`;

        // Clean web learning feedback - already shown above
      }

      // Determine optimal context mode for this question
      const optimalContextMode = this.determineOptimalContextMode(input);
      const needsCodebaseContext = this.needsCodebaseContext(input);

      // Update context mode and show if it changed
      const previousMode = this.currentContextMode;
      this.updateContextMode(optimalContextMode);

      // Show context mode change if it's different
      if (previousMode !== optimalContextMode) {
        console.log(this.ui.status('info', `Context: ${optimalContextMode}`));
      }

      // If this is the first code-related question, add codebase context
      if (needsCodebaseContext && !this.hasCodebaseContext(messages)) {
        let context: string;

        // Use appropriate context builder based on determined mode
        if (optimalContextMode.startsWith('Hybrid') && this.hybridContextBuilder) {
          const useSemanticEngine = optimalContextMode === 'Hybrid-Semantic';
          context = await this.performanceOptimizer.withCache(
            `context_build_${optimalContextMode}`,
            () => useSemanticEngine
              ? this.hybridContextBuilder!.buildEnhancedContext(input, 'chat')
              : this.hybridContextBuilder!.buildContext(),
            300000 // 5 minutes cache
          );
        } else {
          context = await this.performanceOptimizer.withCache(
            'context_build_basic',
            () => this.contextBuilder.buildContext(),
            300000 // 5 minutes cache
          );
        }

        const enhancedSystemPrompt = this.buildGOCAgentSystemPrompt(true, context);

        // Update the system message with codebase context
        messages[0].content = enhancedSystemPrompt;
      }

      messages.push({
        role: 'user',
        content: userMessage
      });

      try {
        this.logger.processing();

        // If we have web content, show a clean indicator
        if (webResult.hasWebContent && webResult.response) {
          console.log(this.ui.status('success', '🌐 Researched web sources'));
        }

        const response = await this.aiManager.chat(messages, providerName, model);

        // Validate and clean the response
        const cleanedResponse = this.validateAndCleanResponse(response.content);
        console.log(this.ui.chat('assistant', cleanedResponse));

        if (response.usage) {
          console.log(this.ui.status('info', `Tokens: ${response.usage.totalTokens} (${response.usage.promptTokens} + ${response.usage.completionTokens})`));
        }

        messages.push({
          role: 'assistant',
          content: response.content
        });

        // Auto-training: Learn from chat interaction (including web learning)
        await this.autoTrainingEngine.onChatInteraction(
          webResult.hasWebContent ? `${input} [with web data]` : input,
          response.content,
          true
        );

      } catch (error) {
        this.logger.error(`Chat error: ${error instanceof Error ? error.message : String(error)}`);

        // Auto-training: Learn from failed interaction
        await this.autoTrainingEngine.onChatInteraction(input, '', false);
      }
    }

    // Show session summary if there were changes
    if (this.changeTracker.hasChanges()) {
      console.log('\n');
      this.changeTracker.displayChangesSummary();
    }

    // Re-enable logs after chat session ends
    this.logger.setSuppressInitLogs(false);
    this.logger.setUserFriendlyMode(false);
    this.logger.info('Chat session ended');
    });
  }

  public async handleAnalyze(options: any): Promise<void> {
    this.logger.loading('Analyzing codebase...');
    
    try {
      const context = await this.codebaseAnalyzer.scanCodebase();
      
      this.logger.success(boxen(
        `Codebase Analysis\n\n` +
        `Files: ${context.totalFiles}\n` +
        `Total Size: ${this.formatBytes(context.totalSize)}\n` +
        `Languages: ${context.languages.join(', ')}\n`,
        { padding: 1, borderColor: 'blue' }
      ));

      if (options.files) {
        const files = await this.codebaseAnalyzer.searchFiles(options.files);
        this.logger.info(`\nMatching files (${files.length}):`);
        files.forEach(file => {
          this.logger.info(`  ${file.relativePath}`);
        });
      } else {
        this.logger.info('\nProject Structure:');
        this.logger.info(context.structure);
      }

    } catch (error) {
      this.logger.error(`Analysis failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  public async handleEdit(file: string, options: any): Promise<void> {
    if (!options.instruction) {
      const { instruction } = await inquirer.prompt([
        {
          type: 'input',
          name: 'instruction',
          message: 'What changes would you like to make?',
          validate: (input) => input.trim() !== '' || 'Please provide an instruction'
        }
      ]);
      options.instruction = instruction;
    }

    try {
      await this.fileEditor.editFile(file, options.instruction, this.aiManager, this.contextBuilder);
      this.logger.success(`File ${file} edited successfully`);
    } catch (error) {
      this.logger.error(`Edit failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  public async handleExplain(file: string, options: any): Promise<void> {
    try {
      const content = await this.codebaseAnalyzer.getFileContent(file);
      const context = await this.contextBuilder.buildFileContext(file);
      
      let prompt = `Please explain this code:\n\n${content}`;
      
      if (options.lines) {
        const [start, end] = options.lines.split('-').map(Number);
        const lines = content.split('\n');
        const selectedLines = lines.slice(start - 1, end).join('\n');
        prompt = `Please explain these lines (${start}-${end}) from ${file}:\n\n${selectedLines}`;
      }

      const messages: ChatMessage[] = [
        { role: 'system', content: context },
        { role: 'user', content: prompt }
      ];

      this.logger.loading('Analyzing code...');
      const response = await this.aiManager.chat(messages);
      
      this.logger.success(boxen(response.content, {
        padding: 1,
        borderColor: 'green',
        title: `Code Explanation: ${file}`
      }));

    } catch (error) {
      this.logger.error(`Explanation failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  public async handleSearch(query: string, options: any): Promise<void> {
    try {
      const searchType = options.type || 'text';
      const results = await this.codebaseAnalyzer.searchFiles(query, searchType);

      if (results.length === 0) {
        this.logger.info('No matches found');
        return;
      }

      console.log(`\nFound ${results.length} matches:`);
      results.forEach(file => {
        console.log(`  ${chalk.blue(file.relativePath)} (${file.size} bytes)`);
      });

    } catch (error) {
      this.logger.error(`Search failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  // Change tracking command handlers
  public async handleChanges(options: any = {}): Promise<void> {
    await this.changeManager.handleChangesCommand(options);
  }

  public async handleRevert(options: any = {}): Promise<void> {
    await this.changeManager.handleRevertCommand(options);
  }

  public async handleClearChanges(): Promise<void> {
    await this.changeManager.clearSession();
  }

  // Web search handler
  public async handleWebSearch(query: string, options: any = {}): Promise<void> {
    try {
      const maxResults = parseInt(options.num) || 3;

      this.logger.info(`🌐 Searching web for: "${query}"`);

      const result = await this.webLearningEngine.processSearchRequest(query);

      if (result.hasWebContent && result.webData) {
        console.log(chalk.blue('\n🌐 Web Search Results:'));
        console.log(chalk.gray('─'.repeat(50)));

        result.webData.forEach((content, index) => {
          console.log(chalk.cyan(`${index + 1}. ${content.title}`));
          console.log(chalk.gray(`   ${content.url}`));
          console.log(`   ${content.summary.substring(0, 200)}...`);
          console.log('');
        });

        console.log(chalk.green('\n✅ Web Learning Complete:'));
        console.log(result.response);

        // Clean feedback - details already shown above
      } else {
        this.logger.warn(`No results found for: "${query}"`);
      }

    } catch (error) {
      this.logger.error(`Web search failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  // Simple intelligence status handler
  public async handleIntelligence(options: any = {}): Promise<void> {
    if (options.status) {
      this.displaySimpleStatus();
    } else {
      // Show simple status by default
      this.displaySimpleStatus();
    }
  }

  private displaySimpleStatus(): void {
    const autoStatus = this.autoTrainingEngine.getAutoTrainingStatus();
    const experiences = this.memoryManager.getAllExperiences();
    const recentExperiences = this.memoryManager.getRecentExperiences(20);
    const successRate = recentExperiences.length > 0 ?
      (recentExperiences.filter(exp => exp.outcome === 'success').length / recentExperiences.length) * 100 : 0;

    console.log(boxen(
      `🤖 Your Agent Status\n\n` +
      `🧠 Intelligence: ${successRate.toFixed(1)}% success rate\n` +
      `📚 Total Experiences: ${experiences.length}\n` +
      `🔄 Auto-Learning: ${autoStatus.enabled ? '✅ Active' : '❌ Disabled'}\n` +
      `⚡ Currently Learning: ${autoStatus.isTraining ? 'Yes' : 'No'}\n\n` +
      `Your agent is ${autoStatus.enabled ? 'automatically getting smarter' : 'not learning'}!`,
      {
        padding: 1,
        borderColor: 'cyan',
        title: '🚀 Agent Intelligence'
      }
    ));

    if (experiences.length < 10) {
      this.logger.info('\n💡 Tip: Use your agent more to help it learn your patterns!');
    } else if (successRate > 90) {
      this.logger.info('\n🎉 Your agent is performing excellently!');
    } else if (successRate < 70) {
      this.logger.info('\n📈 Your agent is still learning - it will improve with more use!');
    }
  }



  public async handleAgent(options: any): Promise<void> {
    // Show header
    console.log(this.ui.header('Autonomous Agent', 'AI-powered task execution and code assistance'));

    // If auto mode is enabled, go straight to autonomous mode
    if (options.auto) {
      this.showSessionInfo('Autonomous Mode', options.provider, options.model);
      await this.handleAutoMode(options);
      process.exit(0);
      return;
    }

    // If task is provided, execute it directly
    if (options.task) {
      this.showSessionInfo('Agent Task Execution', options.provider, options.model);
      await this.executeAgentTask(options.task, options);
      process.exit(0);
      return;
    }

    // Otherwise, show agent menu with session info
    this.showSessionInfo('Agent Menu', options.provider, options.model);
    await this.showAgentMenu(options);
  }

  private async showAgentMenu(options: any): Promise<void> {
    const choices = [
      '💬 Chat about codebase',
      '🔍 Analyze and explain code',
      '✏️  Edit/improve code',
      '🏗️  Create new features',
      '🤖 Autonomous mode (with confirmation)',
      '🚀 Full auto mode (autonomous)',
      '⚙️  Configure settings',
      '❌ Exit'
    ];

    const { action } = await inquirer.prompt([
      {
        type: 'list',
        name: 'action',
        message: '🤖 What would you like me to help you with?',
        choices
      }
    ]);

    switch (action) {
      case '💬 Chat about codebase':
        await this.handleChat(options);
        break;
      case '🔍 Analyze and explain code':
        this.showSessionInfo('Code Analysis', options.provider, options.model);
        await this.handleAnalyzeMode(options);
        break;
      case '✏️  Edit/improve code':
        this.showSessionInfo('Code Editing', options.provider, options.model);
        await this.handleEditMode(options);
        break;
      case '🏗️  Create new features':
        this.showSessionInfo('Feature Creation', options.provider, options.model);
        await this.handleCreateMode(options);
        break;
      case '🤖 Autonomous mode (with confirmation)':
        this.showSessionInfo('Autonomous Mode', options.provider, options.model);
        await this.handleAgentMode(options);
        break;
      case '🚀 Full auto mode (autonomous)':
        this.showSessionInfo('Full Auto Mode', options.provider, options.model);
        await this.handleAutoMode(options);
        break;
      case '⚙️  Configure settings':
        await this.handleConfig();
        break;
      case '❌ Exit':
        this.logger.info('👋 Goodbye!');
        process.exit(0);
        break;
    }
  }

  private async handleAgentMode(options: any): Promise<void> {
    const task = await this.promptForTask();
    // Show session info for the specific task
    this.showSessionInfo('Agent Task', options.provider, options.model);
    await this.executeAgentTask(task, options);
    // Exit after task completion unless in interactive mode
    if (!options.interactive) {
      process.exit(0);
    }
  }

  private async handleAutoMode(options: any): Promise<void> {
    const goal = options.goal || await this.promptForGoal();

    const { confirm } = await inquirer.prompt([
      {
        type: 'confirm',
        name: 'confirm',
        message: chalk.yellow('⚠️  Auto mode will make autonomous changes to your codebase. Continue?'),
        default: false
      }
    ]);

    if (!confirm) {
      this.logger.info('Auto agent mode cancelled');
      process.exit(0);
      return;
    }

    try {
      // Enable user-friendly mode for cleaner output
      this.logger.setUserFriendlyMode(true);

      await this.agentCore.startAutoAgent(goal, options);
    } catch (error) {
      this.logger.error(`Auto agent execution failed: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      // Reset user-friendly mode
      this.logger.setUserFriendlyMode(false);
    }

    // Exit after auto mode completion
    process.exit(0);
  }

  private async executeAgentTask(task: string, options: any): Promise<void> {
    const taskStartTime = Date.now();

    try {
      // Determine optimal context mode for this task
      const optimalContextMode = this.determineOptimalContextMode(task);
      const previousMode = this.currentContextMode;
      this.updateContextMode(optimalContextMode);

      // Show context mode change if it's different
      if (previousMode !== optimalContextMode) {
        console.log(this.ui.status('info', `Context: ${optimalContextMode}`));
      }

      // Quick task classification
      const taskType = this.classifyTask(task);

      if (taskType === 'simple_question') {
        await this.handleSimpleQuestion(task, options);
        return;
      }

      // Show task start with timer
      console.log(this.ui.taskProgress('Starting task execution', taskStartTime));

      // For coding tasks, use the full agent with context mode information
      await this.agentCore.startAgent(task, options);

    } catch (error) {
      const duration = Date.now() - taskStartTime;
      this.logger.error(`Agent execution failed after ${this.formatDuration(duration)}: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  private needsCodebaseContext(input: string): boolean {
    const inputLower = input.toLowerCase().trim();

    // Identity questions don't need codebase context
    const identityPatterns = [
      /^(who|what)\s+(are|is)\s+you/,
      /^(tell me about yourself|introduce yourself)/,
      /^(what can you do|what are your capabilities)/
    ];

    // General questions that don't need codebase context
    const generalPatterns = [
      /^(hello|hi|hey|good morning|good afternoon|good evening)/,
      /^(how are you|how's it going)/,
      /^(help|info|information)$/,
      /^(status|version|about)$/,
      /^(thank you|thanks|bye|goodbye)/
    ];

    // Check for identity/general questions first
    for (const pattern of identityPatterns.concat(generalPatterns)) {
      if (pattern.test(inputLower)) {
        return false;
      }
    }

    // Code-related keywords that need context
    const codePatterns = [
      /\b(file|files|code|function|class|method|variable|component|module)\b/,
      /\b(fix|bug|error|implement|create|add|remove|delete|refactor|optimize)\b/,
      /\b(analyze|explain|review|understand|debug|test)\b.*\b(code|file|function)\b/,
      /\b(project|codebase|repository|structure|architecture)\b/,
      /\.(js|ts|py|java|cpp|html|css|json|md)(\s|$)/,
      /\b(install|deploy|build|compile|run|execute)\b/
    ];

    // Check for code-related patterns
    for (const pattern of codePatterns) {
      if (pattern.test(inputLower)) {
        return true;
      }
    }

    // Default to false for general conversation
    return false;
  }

  private hasCodebaseContext(messages: ChatMessage[]): boolean {
    if (messages.length === 0) return false;
    const systemMessage = messages[0];
    return systemMessage.role === 'system' && systemMessage.content.includes('Current codebase context:');
  }

  private determineOptimalContextMode(input: string): 'Basic' | 'Hybrid-Semantic' | 'Hybrid-Basic' {
    const config = this.config.getConfig();

    // If hybrid context is not enabled, always use basic
    if (!config.contextEngine.enabled || !this.hybridContextBuilder) {
      return 'Basic';
    }

    // Analyze the input to determine complexity and type
    const inputLower = input.toLowerCase().trim();
    const inputLength = input.length;

    // High complexity indicators that benefit from semantic processing
    const semanticIndicators = [
      // Architectural and design questions
      /\b(architecture|pattern|design|structure|relationship|dependency|coupling)\b/i,
      // Complex analysis requests
      /\b(analyze|explain|understand|review|refactor|optimize|improve)\b.*\b(code|codebase|project|system)\b/i,
      // Multi-file or cross-cutting concerns
      /\b(across|throughout|entire|whole|all|multiple)\b.*\b(files|components|modules|classes)\b/i,
      // Performance and quality questions
      /\b(performance|quality|best practices|anti-pattern|code smell)\b/i,
      // Complex debugging scenarios
      /\b(debug|troubleshoot|investigate|trace|root cause)\b/i,
      // Integration and workflow questions
      /\b(integration|workflow|pipeline|deployment|testing strategy)\b/i
    ];

    // Medium complexity indicators
    const hybridBasicIndicators = [
      // File-specific questions that might need some context
      /\b(file|function|class|method|component)\b.*\b(how|what|why|where)\b/i,
      // Code explanation requests
      /\b(explain|show|describe)\b.*\b(this|that|code|implementation)\b/i,
      // Simple refactoring
      /\b(change|modify|update|fix)\b/i
    ];

    // Check for high complexity (semantic processing)
    for (const pattern of semanticIndicators) {
      if (pattern.test(input)) {
        return 'Hybrid-Semantic';
      }
    }

    // Check for medium complexity or long queries
    if (inputLength > 50 || hybridBasicIndicators.some(pattern => pattern.test(input))) {
      return 'Hybrid-Basic';
    }

    // Simple questions use basic context
    return 'Basic';
  }

  private updateContextMode(newMode: 'Basic' | 'Hybrid-Semantic' | 'Hybrid-Basic'): void {
    this.currentContextMode = newMode;
  }

  private getContextModeDisplay(): string {
    const config = this.config.getConfig();

    // Show the intended context mode based on analysis, even if hybrid is not available
    // This demonstrates the dynamic context switching capability
    if (config.contextEngine?.enabled && this.hybridContextBuilder) {
      return this.currentContextMode;
    } else {
      // Show what mode would be used if hybrid context was available
      return `Basic (would use: ${this.currentContextMode})`;
    }
  }

  private classifyTask(task: string): 'simple_question' | 'coding_task' {
    // Type guard to ensure task is a string
    if (typeof task !== 'string') {
      this.logger.warn(`Task is not a string: ${typeof task}, converting to string`);
      task = String(task);
    }

    const taskLower = task.toLowerCase().trim();

    // Simple questions that don't need codebase analysis
    const questionPatterns = [
      /^(who|what|how|when|where|why)\s+(are|is|do|does|can|will|would)/,
      /^(tell me|explain|describe|what is|who is)/,
      /^(help|info|information)/,
      /^(status|version|about)/
    ];

    // Coding task indicators
    const codingPatterns = [
      /\b(fix|bug|error|implement|create|add|remove|delete|refactor|optimize|test|debug)\b/,
      /\b(function|class|method|variable|file|code|script|program)\b/,
      /\b(install|deploy|build|compile|run|execute)\b/
    ];

    // Check for simple questions first
    for (const pattern of questionPatterns) {
      if (pattern.test(taskLower)) {
        return 'simple_question';
      }
    }

    // Check for coding tasks
    for (const pattern of codingPatterns) {
      if (pattern.test(taskLower)) {
        return 'coding_task';
      }
    }

    // Default to simple question for short queries
    if (task.length < 50 && task.includes('?')) {
      return 'simple_question';
    }

    return 'coding_task';
  }

  private async getHybridContextBuilder(): Promise<HybridContextBuilder> {
    if (!this.hybridContextBuilder) {
      const contextConfig = this.config.getConfig().contextEngine;
      this.hybridContextBuilder = new HybridContextBuilder(
        this.codebaseAnalyzer,
        this.logger,
        {
          useSemanticEngine: contextConfig.enabled,
          fallbackToBasic: contextConfig.fallbackToBasic,
          semanticThreshold: 0.5,
          maxSemanticProcessingTime: contextConfig.apiConfig?.timeout || 30000,
          cacheEnabled: contextConfig.cacheEnabled
        },
        contextConfig.enabled ? contextConfig : undefined
      );
    }
    return this.hybridContextBuilder;
  }

  private async handleSimpleQuestion(task: string, options: any): Promise<void> {
    const messages: ChatMessage[] = [
      {
        role: 'system',
        content: `You are GOC Agent, an intelligent coding assistant. You are NOT Deepseek, Claude, or any other AI model - you are specifically GOC Agent.

Your identity and capabilities:
- Name: GOC Agent (not Deepseek, not Claude, not any other AI)
- Purpose: Help developers with their codebase through chat, analysis, and autonomous coding
- You are an intelligent coding assistant designed to understand and work with codebases
- AI Providers: You can use multiple AI providers (Ollama, OpenAI, Groq, Gemini) but YOU are GOC Agent

Key features:
- Interactive chat about codebases
- Autonomous coding with user confirmation
- Intelligent context understanding and semantic search
- Self-learning and adaptation from user interactions
- Web browsing and research capabilities
- Multiple operation modes (Chat, Agent, Auto)

Always identify yourself as GOC Agent. Answer the user's question directly and conversationally. Keep it brief and helpful.`
      },
      {
        role: 'user',
        content: task
      }
    ];

    try {
      const response = await this.aiManager.chat(messages, options.provider, options.model);
      console.log('\n' + response.content);
    } catch (error) {
      this.logger.error(`Failed to get response: ${error}`);
    }
  }

  private async handleAnalyzeMode(options: any): Promise<void> {
    const { choice } = await inquirer.prompt([
      {
        type: 'list',
        name: 'choice',
        message: 'What would you like me to analyze?',
        choices: [
          'Entire codebase overview',
          'Specific file or directory',
          'Code quality and improvements',
          'Architecture and patterns'
        ]
      }
    ]);

    switch (choice) {
      case 'Entire codebase overview':
        await this.handleAnalyze({});
        break;
      case 'Specific file or directory':
        const { filePath } = await inquirer.prompt([
          {
            type: 'input',
            name: 'filePath',
            message: 'Enter file or directory path:'
          }
        ]);
        if (filePath) {
          await this.handleExplain(filePath, {});
        }
        break;
      default:
        await this.executeAgentTask(`Analyze codebase for ${choice.toLowerCase()}`, options);
    }

    // Exit after analysis unless in interactive mode
    if (!options.interactive) {
      process.exit(0);
    }
  }

  private async handleEditMode(options: any): Promise<void> {
    const { filePath } = await inquirer.prompt([
      {
        type: 'input',
        name: 'filePath',
        message: 'Enter file path to edit:'
      }
    ]);

    if (filePath) {
      const { instruction } = await inquirer.prompt([
        {
          type: 'input',
          name: 'instruction',
          message: 'What changes would you like to make?',
          validate: (input) => input.trim() !== '' || 'Please provide an instruction'
        }
      ]);

      await this.handleEdit(filePath, { instruction });
    }

    // Exit after edit unless in interactive mode
    if (!options.interactive) {
      process.exit(0);
    }
  }

  private async handleCreateMode(options: any): Promise<void> {
    const { feature } = await inquirer.prompt([
      {
        type: 'input',
        name: 'feature',
        message: 'Describe the feature you want to create:',
        validate: (input) => input.trim() !== '' || 'Please describe the feature'
      }
    ]);

    await this.executeAgentTask(`Create new feature: ${feature}`, options);

    // Exit after creation unless in interactive mode
    if (!options.interactive) {
      process.exit(0);
    }
  }

  public async handleModelSelection(): Promise<void> {
    try {
      const availableProviders = await this.aiManager.getAvailableProviders();

      if (availableProviders.length === 0) {
        console.log(this.ui.error('No Providers', 'No AI providers are available.', 'Please configure at least one provider first using: goc config'));
        return;
      }

      // Show header
      console.log(this.ui.header('Model Management', 'Browse, test, and configure AI models'));

      // Enhanced model selection menu
      const { action } = await inquirer.prompt([
        {
          type: 'list',
          name: 'action',
          message: '🤖 Model Management:',
          choices: [
            '📋 Browse and select models',
            '🧪 Test model performance',
            '📊 Compare models',
            '⚡ Benchmark models',
            '🔧 Configure model settings',
            '❌ Exit'
          ]
        }
      ]);

      switch (action) {
        case '📋 Browse and select models':
          await this.browseAndSelectModels();
          break;
        case '🧪 Test model performance':
          await this.testModelPerformance();
          break;
        case '📊 Compare models':
          await this.compareModels();
          break;
        case '⚡ Benchmark models':
          await this.benchmarkModels();
          break;
        case '🔧 Configure model settings':
          await this.configureModelSettings();
          break;
        case '❌ Exit':
          process.exit(0);
          return;
      }
    } catch (error) {
      this.logger.error(`Model selection failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  private async browseAndSelectModels(): Promise<void> {
    const availableProviders = await this.aiManager.getAvailableProviders();

    // Select provider
    const { selectedProvider } = await inquirer.prompt([
      {
        type: 'list',
        name: 'selectedProvider',
        message: '🤖 Select AI Provider:',
        choices: availableProviders.map(provider => ({
          name: `${provider.charAt(0).toUpperCase() + provider.slice(1)}`,
          value: provider
        }))
      }
    ]);

    const provider = this.aiManager.getProvider(selectedProvider);
    if (!provider) {
      this.logger.error(`Provider ${selectedProvider} not found`);
      return;
    }

    try {
      // Get available models
      this.logger.info(`🔍 Loading models from ${selectedProvider}...`);
      const models = await provider.listModels();

      if (models.length === 0) {
        this.logger.warn(`No models found for ${selectedProvider}. ${selectedProvider === 'ollama' ? 'Try pulling models with: ollama pull <model-name>' : 'Check your API configuration.'}`);
        return;
      }

      // Display current configuration
      const config = this.config.getConfig();
      const currentDefault = config.providers[selectedProvider as keyof typeof config.providers]?.defaultModel;

      console.log(boxen(
        `📋 Available Models for ${selectedProvider.charAt(0).toUpperCase() + selectedProvider.slice(1)}\n\n` +
        `Current default: ${currentDefault || 'None set'}\n` +
        `Found ${models.length} models`,
        { padding: 1, borderColor: 'blue', title: '🤖 Model Selection' }
      ));

      // Select model
      const modelChoices = models.map(model => ({
        name: model === currentDefault ? `${model} (current default)` : model,
        value: model
      }));

      const { selectedModel } = await inquirer.prompt([
        {
          type: 'list',
          name: 'selectedModel',
          message: '🎯 Select Model:',
          choices: [
            ...modelChoices,
            new inquirer.Separator(),
            { name: '🔄 Refresh model list', value: '__refresh__' },
            { name: '⚙️ Set as default', value: '__set_default__' },
            { name: '❌ Cancel', value: '__cancel__' }
          ],
          pageSize: 15
        }
      ]);

      if (selectedModel === '__cancel__') {
        return;
      }

      if (selectedModel === '__refresh__') {
        return await this.handleModelSelection();
      }

      if (selectedModel === '__set_default__') {
        return await this.setDefaultModel(selectedProvider, models);
      }

      // Test the selected model
      await this.testModel(selectedProvider, selectedModel);

    } catch (error) {
      this.logger.error(`Failed to load models: ${error}`);
    }
  }

  private async setDefaultModel(provider: string, availableModels: string[]): Promise<void> {
    const { model } = await inquirer.prompt([
      {
        type: 'list',
        name: 'model',
        message: `🎯 Set default model for ${provider}:`,
        choices: availableModels
      }
    ]);

    // Update configuration
    this.config.setProviderDefaultModel(provider, model);
    this.logger.success(`✅ Default model for ${provider} set to: ${model}`);
  }

  private async testModel(provider: string, model: string): Promise<void> {
    try {
      this.logger.info(`🧪 Testing ${provider}:${model}...`);

      const messages: ChatMessage[] = [
        {
          role: 'user',
          content: 'Hello! Please respond with just "Hello from [your model name]" to confirm you are working.'
        }
      ];

      const response = await this.aiManager.chat(messages, provider, model);

      console.log(boxen(
        `✅ Model Test Successful!\n\n` +
        `Provider: ${provider}\n` +
        `Model: ${model}\n\n` +
        `Response: ${response.content}\n\n` +
        `Usage: ${response.usage?.totalTokens || 'N/A'} tokens`,
        { padding: 1, borderColor: 'green', title: '🎉 Model Test Result' }
      ));

      // Ask if user wants to set as default
      const { setAsDefault } = await inquirer.prompt([
        {
          type: 'confirm',
          name: 'setAsDefault',
          message: `Set ${model} as default for ${provider}?`,
          default: false
        }
      ]);

      if (setAsDefault) {
        this.config.setProviderDefaultModel(provider, model);
        this.logger.success(`✅ ${model} is now the default for ${provider}`);
      }

    } catch (error) {
      this.logger.error(`❌ Model test failed: ${error}`);
    }
  }



  private async showCurrentConfig(): Promise<void> {
    const config = this.config.getConfig();
    
    console.log(boxen(
      `Current Configuration\n\n` +
      `Default Provider: ${config.defaultProvider}\n` +
      `Config Path: ${this.config.getConfigPath()}\n`,
      { padding: 1, borderColor: 'cyan' }
    ));

    const table = new Table({
      head: ['Provider', 'Status', 'Model', 'URL'],
      colWidths: [12, 12, 20, 40]
    });

    for (const [name, provider] of Object.entries(config.providers)) {
      const status = provider.enabled ? 
        (provider.apiKey || name === 'ollama' ? chalk.green('✓ Ready') : chalk.yellow('⚠ No API Key')) :
        chalk.red('✗ Disabled');
      
      table.push([
        name,
        status,
        provider.defaultModel || 'N/A',
        provider.baseUrl || 'N/A'
      ]);
    }

    console.log(table.toString());
  }

  private async configureProviders(): Promise<void> {
    const config = this.config.getConfig();
    const providers = Object.keys(config.providers);

    const { provider } = await inquirer.prompt([
      {
        type: 'list',
        name: 'provider',
        message: 'Which provider would you like to configure?',
        choices: providers
      }
    ]);

    const providerConfig = config.providers[provider as keyof typeof config.providers];
    
    const { apiKey, enabled } = await inquirer.prompt([
      {
        type: 'password',
        name: 'apiKey',
        message: `Enter API key for ${provider}:`,
        when: provider !== 'ollama',
        default: providerConfig.apiKey
      },
      {
        type: 'confirm',
        name: 'enabled',
        message: `Enable ${provider}?`,
        default: providerConfig.enabled
      }
    ]);

    if (apiKey) {
      this.config.setProviderApiKey(provider, apiKey);
    }
    this.config.enableProvider(provider, enabled);

    this.logger.success(`${provider} configuration updated`);
  }

  private async setDefaultProvider(): Promise<void> {
    const availableProviders = await this.aiManager.getAvailableProviders();
    
    if (availableProviders.length === 0) {
      this.logger.error('No providers are available. Please configure at least one provider first.');
      return;
    }

    const { provider } = await inquirer.prompt([
      {
        type: 'list',
        name: 'provider',
        message: 'Select default provider:',
        choices: availableProviders
      }
    ]);

    this.config.updateConfig({ defaultProvider: provider });
    this.logger.success(`Default provider set to ${provider}`);
  }

  private async configureCodebase(): Promise<void> {
    this.logger.info('Codebase configuration not yet implemented');
  }

  private showChatHelp(): void {
    console.log(boxen(
      'Chat Commands:\n\n' +
      'exit - End the chat session\n' +
      'help - Show this help message\n' +
      'changes - Show current session changes\n\n' +
      'Web Features:\n' +
      '• Paste any URL - Agent will read and learn from it\n' +
      '• Ask "search for X" - Agent will search web and learn\n' +
      '• Ask "what is X" - Agent will research and respond\n',
      { padding: 1, borderColor: 'yellow', title: 'Help' }
    ));
  }

  private async promptForTask(): Promise<string> {
    const { task } = await inquirer.prompt([
      {
        type: 'input',
        name: 'task',
        message: 'What task would you like the agent to perform?',
        validate: (input) => input.trim() !== '' || 'Please provide a task description'
      }
    ]);
    return task;
  }

  private async promptForGoal(): Promise<string> {
    const { goal } = await inquirer.prompt([
      {
        type: 'input',
        name: 'goal',
        message: 'What is your high-level goal for the auto agent?',
        validate: (input) => input.trim() !== '' || 'Please provide a goal description'
      }
    ]);
    return goal;
  }





  public async handleGuidelines(options: any): Promise<void> {
    if (options.view) {
      await this.showCurrentGuidelines();
    } else if (options.set) {
      await this.setGuidelines(options.project);
    } else {
      await this.showGuidelinesMenu();
    }
  }

  public async handleProject(options: any): Promise<void> {
    if (options.add) {
      await this.addProject(options.add);
    } else if (options.set) {
      await this.setCurrentProject(options.set);
    } else if (options.list) {
      await this.listProjects();
    } else if (options.remove) {
      await this.removeProject(options.remove);
    } else {
      await this.showProjectMenu();
    }
  }

  private async showGuidelinesMenu(): Promise<void> {
    const choices = [
      'View current guidelines',
      'Set coding style preferences',
      'Set project rules',
      'Add custom instruction',
      'Remove custom instruction',
      'Set project-specific guidelines',
      'Exit'
    ];

    const { action } = await inquirer.prompt([
      {
        type: 'list',
        name: 'action',
        message: 'Guidelines Management',
        choices
      }
    ]);

    switch (action) {
      case 'View current guidelines':
        await this.showCurrentGuidelines();
        break;
      case 'Set coding style preferences':
        await this.setCodingStyle();
        break;
      case 'Set project rules':
        await this.setProjectRules();
        break;
      case 'Add custom instruction':
        await this.addCustomInstruction();
        break;
      case 'Remove custom instruction':
        await this.removeCustomInstruction();
        break;
      case 'Set project-specific guidelines':
        await this.setProjectGuidelines();
        break;
    }
  }

  private async showCurrentGuidelines(): Promise<void> {
    const guidelines = this.config.getEffectiveGuidelines();
    const currentProject = this.config.getCurrentProject();

    console.log(boxen(
      `Current Guidelines\n\n` +
      `Language: ${guidelines.codingStyle.language}\n` +
      `Framework: ${guidelines.codingStyle.framework}\n` +
      `Conventions: ${guidelines.codingStyle.conventions.join(', ')}\n` +
      `Patterns: ${guidelines.codingStyle.patterns.join(', ')}\n\n` +
      `Testing: ${guidelines.preferences.testingFramework}\n` +
      `Documentation: ${guidelines.preferences.documentationStyle}\n` +
      `Error Handling: ${guidelines.preferences.errorHandling}\n` +
      `Logging: ${guidelines.preferences.logging ? 'Enabled' : 'Disabled'}\n` +
      `Backward Compatibility: ${guidelines.preferences.backwardCompatibility ? 'Required' : 'Not Required'}\n\n` +
      `Custom Instructions:\n${guidelines.customInstructions.map(i => `• ${i}`).join('\n')}\n` +
      (currentProject ? `\nCurrent Project: ${currentProject.name}` : ''),
      { padding: 1, borderColor: 'green', title: 'User Guidelines' }
    ));
  }

  private async setCodingStyle(): Promise<void> {
    const current = this.config.getConfig().userGuidelines.codingStyle;

    const answers = await inquirer.prompt([
      {
        type: 'input',
        name: 'language',
        message: 'Primary programming language:',
        default: current.language
      },
      {
        type: 'input',
        name: 'framework',
        message: 'Primary framework:',
        default: current.framework
      },
      {
        type: 'input',
        name: 'conventions',
        message: 'Coding conventions (comma-separated):',
        default: current.conventions.join(', ')
      },
      {
        type: 'input',
        name: 'patterns',
        message: 'Preferred patterns (comma-separated):',
        default: current.patterns.join(', ')
      }
    ]);

    this.config.updateUserGuidelines({
      codingStyle: {
        language: answers.language,
        framework: answers.framework,
        conventions: answers.conventions.split(',').map((s: string) => s.trim()),
        patterns: answers.patterns.split(',').map((s: string) => s.trim())
      }
    });

    this.logger.success('Coding style preferences updated!');
  }

  private async addCustomInstruction(): Promise<void> {
    const { instruction } = await inquirer.prompt([
      {
        type: 'input',
        name: 'instruction',
        message: 'Enter custom instruction:',
        validate: (input) => input.trim() !== '' || 'Please provide an instruction'
      }
    ]);

    this.config.addCustomInstruction(instruction);
    this.logger.success('Custom instruction added!');
  }

  private async showProjectMenu(): Promise<void> {
    const choices = [
      'List recent projects',
      'Add current directory as project',
      'Set current project',
      'Remove project',
      'Auto-detect project info',
      'Exit'
    ];

    const { action } = await inquirer.prompt([
      {
        type: 'list',
        name: 'action',
        message: 'Project Management',
        choices
      }
    ]);

    switch (action) {
      case 'List recent projects':
        await this.listProjects();
        break;
      case 'Add current directory as project':
        await this.addProject(process.cwd());
        break;
      case 'Set current project':
        await this.selectCurrentProject();
        break;
      case 'Remove project':
        await this.selectAndRemoveProject();
        break;
      case 'Auto-detect project info':
        await this.autoDetectProject();
        break;
    }
  }

  private async addProject(projectPath: string): Promise<void> {
    const resolvedPath = path.resolve(projectPath);

    if (!fs.existsSync(resolvedPath)) {
      this.logger.error(`Path does not exist: ${resolvedPath}`);
      return;
    }

    const projectInfo = this.guidelinesManager.autoDetectProjectInfo(resolvedPath);

    console.log(boxen(
      `Detected Project Info:\n\n` +
      `Name: ${projectInfo.name}\n` +
      `Type: ${projectInfo.type}\n` +
      `Framework: ${projectInfo.framework}\n` +
      `Version: ${projectInfo.version}`,
      { padding: 1, borderColor: 'blue' }
    ));

    const { confirm } = await inquirer.prompt([
      {
        type: 'confirm',
        name: 'confirm',
        message: 'Add this project?',
        default: true
      }
    ]);

    if (confirm) {
      this.config.addProject(projectInfo);
      this.config.setCurrentProject(resolvedPath);
      this.logger.success(`Project added and set as current: ${projectInfo.name}`);
    }
  }

  private async listProjects(): Promise<void> {
    const projects = this.config.getRecentProjects();
    const currentProject = this.config.getCurrentProject();

    if (projects.length === 0) {
      this.logger.info('No projects found. Add a project first.');
      return;
    }

    const table = new Table({
      head: ['Name', 'Type', 'Framework', 'Path', 'Current'],
      colWidths: [20, 12, 15, 40, 10]
    });

    projects.forEach(project => {
      const isCurrent = currentProject?.path === project.path;
      table.push([
        project.name,
        project.type,
        project.framework,
        project.path,
        isCurrent ? chalk.green('✓') : ''
      ]);
    });

    console.log(table.toString());
  }

  private async setGuidelines(projectPath?: string): Promise<void> {
    await this.setCodingStyle();
  }

  private async setCurrentProject(projectPath: string): Promise<void> {
    this.config.setCurrentProject(projectPath);
    this.logger.success(`Current project set to: ${projectPath}`);
  }

  private async removeProject(projectPath: string): Promise<void> {
    const projects = this.config.getConfig().projects.filter(p => p.path !== projectPath);
    this.config.updateConfig({ projects });
    this.logger.success(`Project removed: ${projectPath}`);
  }

  private async setProjectRules(): Promise<void> {
    const current = this.config.getConfig().userGuidelines.projectRules;

    const answers = await inquirer.prompt([
      {
        type: 'input',
        name: 'fileNaming',
        message: 'File naming convention:',
        default: current.fileNaming
      },
      {
        type: 'input',
        name: 'directoryStructure',
        message: 'Directory structure (comma-separated):',
        default: current.directoryStructure.join(', ')
      }
    ]);

    this.config.updateUserGuidelines({
      projectRules: {
        ...current,
        fileNaming: answers.fileNaming,
        directoryStructure: answers.directoryStructure.split(',').map((s: string) => s.trim())
      }
    });

    this.logger.success('Project rules updated!');
  }

  private async removeCustomInstruction(): Promise<void> {
    const instructions = this.config.getConfig().userGuidelines.customInstructions;

    if (instructions.length === 0) {
      this.logger.info('No custom instructions to remove');
      return;
    }

    const { instruction } = await inquirer.prompt([
      {
        type: 'list',
        name: 'instruction',
        message: 'Select instruction to remove:',
        choices: instructions
      }
    ]);

    this.config.removeCustomInstruction(instruction);
    this.logger.success('Custom instruction removed!');
  }

  private async setProjectGuidelines(): Promise<void> {
    const currentProject = this.config.getCurrentProject();
    if (!currentProject) {
      this.logger.error('No current project set');
      return;
    }

    await this.setCodingStyle();
    this.logger.success('Project-specific guidelines updated!');
  }

  private async selectCurrentProject(): Promise<void> {
    const projects = this.config.getRecentProjects();

    if (projects.length === 0) {
      this.logger.info('No projects available');
      return;
    }

    const { project } = await inquirer.prompt([
      {
        type: 'list',
        name: 'project',
        message: 'Select current project:',
        choices: projects.map(p => ({ name: `${p.name} (${p.path})`, value: p.path }))
      }
    ]);

    this.config.setCurrentProject(project);
    this.logger.success(`Current project set to: ${project}`);
  }

  private async selectAndRemoveProject(): Promise<void> {
    const projects = this.config.getRecentProjects();

    if (projects.length === 0) {
      this.logger.info('No projects to remove');
      return;
    }

    const { project } = await inquirer.prompt([
      {
        type: 'list',
        name: 'project',
        message: 'Select project to remove:',
        choices: projects.map(p => ({ name: `${p.name} (${p.path})`, value: p.path }))
      }
    ]);

    await this.removeProject(project);
  }

  private async autoDetectProject(): Promise<void> {
    const projectInfo = this.guidelinesManager.autoDetectProjectInfo(process.cwd());

    console.log(boxen(
      `Auto-detected Project Info:\n\n` +
      `Name: ${projectInfo.name}\n` +
      `Type: ${projectInfo.type}\n` +
      `Framework: ${projectInfo.framework}\n` +
      `Version: ${projectInfo.version}`,
      { padding: 1, borderColor: 'blue' }
    ));

    const { confirm } = await inquirer.prompt([
      {
        type: 'confirm',
        name: 'confirm',
        message: 'Add this project with detected settings?',
        default: true
      }
    ]);

    if (confirm) {
      this.config.addProject(projectInfo);
      this.config.setCurrentProject(projectInfo.path);
      this.logger.success(`Project added: ${projectInfo.name}`);
    }
  }

  private validateAndCleanResponse(response: string): string {
    // Clean up the response and ensure it's meaningful
    const trimmed = response.trim();

    // If response is just "GOC Agent" or similar, provide a helpful fallback
    if (trimmed === 'GOC Agent' || trimmed === 'Assistant' || trimmed.length < 10) {
      return "I'm here to help! Please let me know what you'd like to know or what task you'd like me to assist with.";
    }

    // Remove any leading "GOC Agent:" or "Assistant:" prefixes that might be duplicated
    const cleanedResponse = trimmed
      .replace(/^(GOC Agent|Assistant):\s*/i, '')
      .replace(/^(GOC Agent|Assistant)\s*$/i, "I'm here to help! What would you like to know?");

    return cleanedResponse || "I'm ready to assist you. What can I help you with?";
  }

  private buildGOCAgentSystemPrompt(includeCodebaseContext: boolean = false, context?: string): string {
    const basePrompt = `You are GOC Agent, an intelligent coding assistant designed to help developers with their codebases through chat, analysis, and autonomous coding capabilities.

IMPORTANT RESPONSE GUIDELINES:
- Always provide helpful, detailed responses
- Never respond with just "GOC Agent" or single words
- Be conversational and informative
- If you don't understand something, ask for clarification
- Provide practical advice and examples when relevant

Key characteristics:
- You are GOC Agent, NOT any other AI assistant
- You are conversational, helpful, and can discuss both technical and general topics
- You maintain a professional but friendly tone
- You can answer general questions about yourself and your capabilities
- When discussing code, you provide practical, actionable advice

Your capabilities include:
- Interactive conversations about any topic (not just code)
- Code analysis and explanation when needed
- Autonomous coding with user confirmation
- Intelligent context understanding and semantic search
- Self-learning and adaptation from user interactions
- Web browsing and research capabilities

${includeCodebaseContext && context ? `\nCurrent codebase context:\n${context}` : ''}

Always identify yourself as GOC Agent when asked about your identity. For general questions, respond conversationally without technical jargon. For code-related questions, provide detailed technical assistance.`;

    return basePrompt;
  }

  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  // Testing command handlers
  public async handleTest(options: any = {}): Promise<void> {
    if (options.suite) {
      await this.runTestSuite(options.suite);
    } else if (options.generate) {
      await this.generateTestReport(options.output);
    } else {
      await this.runAllTests();
    }
  }

  private async runAllTests(): Promise<void> {
    this.logger.info('🧪 Running comprehensive test suite...');
    const report = await this.testFramework.runAllTests();

    if (report.failed > 0) {
      this.logger.warn(`⚠️ ${report.failed} tests failed out of ${report.totalTests}`);
    } else {
      this.logger.success(`🎉 All ${report.totalTests} tests passed!`);
    }
  }

  private async runTestSuite(suiteName: string): Promise<void> {
    this.logger.info(`🧪 Running test suite: ${suiteName}`);
    const results = await this.testFramework.runTestSuite(suiteName);
    const failed = results.filter(r => !r.passed).length;

    if (failed > 0) {
      this.logger.warn(`⚠️ ${failed} tests failed in suite ${suiteName}`);
    } else {
      this.logger.success(`✅ All tests passed in suite ${suiteName}`);
    }
  }

  private async generateTestReport(outputPath?: string): Promise<void> {
    const defaultPath = outputPath || `test-report-${Date.now()}.json`;
    await this.testFramework.generateTestReport(defaultPath);
  }

  // Performance monitoring command handlers
  public async handlePerformance(options: any = {}): Promise<void> {
    if (options.stats) {
      this.displayPerformanceStats();
    } else if (options.clear) {
      this.clearPerformanceCache();
    } else {
      this.displayPerformanceStats();
    }
  }

  private displayPerformanceStats(): void {
    const metrics = this.performanceOptimizer.getMetrics();
    const cacheStats = this.performanceOptimizer.getCacheStats();

    console.log(boxen(
      `🚀 Performance Metrics\n\n` +
      `Cache Hit Rate: ${(metrics.cacheHitRate * 100).toFixed(1)}%\n` +
      `Average Response Time: ${metrics.averageResponseTime.toFixed(0)}ms\n` +
      `Memory Usage: ${metrics.memoryUsage.toFixed(1)}MB\n` +
      `Cache Size: ${cacheStats.size} entries\n\n` +
      `Operation Counts:\n` +
      Array.from(metrics.operationCounts.entries())
        .map(([op, count]) => `  ${op}: ${count}`)
        .join('\n'),
      {
        padding: 1,
        borderColor: 'cyan',
        title: '📊 Performance Dashboard'
      }
    ));
  }

  private clearPerformanceCache(): void {
    this.performanceOptimizer.clearCache();
    this.logger.success('🧹 Performance cache cleared');
  }

  // Enhanced model selection methods
  private async testModelPerformance(): Promise<void> {
    const availableProviders = await this.aiManager.getAvailableProviders();

    const { selectedProvider } = await inquirer.prompt([
      {
        type: 'list',
        name: 'selectedProvider',
        message: '🤖 Select provider to test:',
        choices: availableProviders
      }
    ]);

    const provider = this.aiManager.getProvider(selectedProvider);
    if (!provider) return;

    try {
      this.logger.info(`🧪 Testing ${selectedProvider} models...`);
      const models = await provider.listModels();

      if (models.length === 0) {
        this.logger.warn(`No models available for ${selectedProvider}`);
        return;
      }

      const { selectedModel } = await inquirer.prompt([
        {
          type: 'list',
          name: 'selectedModel',
          message: 'Select model to test:',
          choices: models.map(model => ({
            name: model,
            value: model
          }))
        }
      ]);

      // Test model with standard prompts
      const testPrompts = [
        'Hello, please respond with "Test successful" if you can read this.',
        'What is 2+2?',
        'Explain what TypeScript is in one sentence.'
      ];

      const results = [];
      for (const prompt of testPrompts) {
        const startTime = Date.now();
        try {
          const response = await this.aiManager.chat([
            { role: 'user', content: prompt }
          ], selectedProvider, selectedModel);

          const duration = Date.now() - startTime;
          results.push({
            prompt: prompt.substring(0, 50) + '...',
            success: true,
            duration,
            responseLength: response.content.length,
            tokensUsed: response.usage?.totalTokens || 0
          });
        } catch (error) {
          results.push({
            prompt: prompt.substring(0, 50) + '...',
            success: false,
            duration: Date.now() - startTime,
            error: error instanceof Error ? error.message : String(error)
          });
        }
      }

      // Display results using new UI
      const resultData: Record<string, any> = {
        'Provider': selectedProvider,
        'Model': selectedModel,
        'Tests Passed': results.filter(r => r.success).length + '/' + results.length,
        'Average Duration': Math.round(results.reduce((sum, r) => sum + r.duration, 0) / results.length) + 'ms'
      };

      console.log(this.ui.result('🧪 Model Performance Test Results', resultData));

      // Show detailed test results
      console.log('\n' + this.ui.section('Test Details', ''));
      results.forEach((result, i) => {
        const status = result.success ? 'success' : 'error';
        const message = result.success
          ? `Test ${i + 1}: ${result.duration}ms, ${result.responseLength} chars, ${result.tokensUsed} tokens`
          : `Test ${i + 1}: ${result.error}`;
        console.log(this.ui.status(status, message));
      });

    } catch (error) {
      this.logger.error(`Model testing failed: ${error}`);
    }
  }

  private async compareModels(): Promise<void> {
    this.logger.info('🔍 Model Comparison Tool');

    const availableProviders = await this.aiManager.getAvailableProviders();
    const modelSelections = [];

    // Select multiple models to compare
    for (let i = 0; i < 3; i++) {
      const { addAnother } = await inquirer.prompt([
        {
          type: 'confirm',
          name: 'addAnother',
          message: i === 0 ? 'Select first model to compare:' : `Add model ${i + 1} for comparison?`,
          default: i < 2
        }
      ]);

      if (!addAnother && i > 0) break;

      const { provider } = await inquirer.prompt([
        {
          type: 'list',
          name: 'provider',
          message: `Select provider for model ${i + 1}:`,
          choices: availableProviders
        }
      ]);

      const providerInstance = this.aiManager.getProvider(provider);
      if (!providerInstance) continue;

      const models = await providerInstance.listModels();
      const { model } = await inquirer.prompt([
        {
          type: 'list',
          name: 'model',
          message: `Select model from ${provider}:`,
          choices: models.map(m => ({ name: m, value: m }))
        }
      ]);

      modelSelections.push({ provider, model });
    }

    if (modelSelections.length < 2) {
      this.logger.warn('Need at least 2 models to compare');
      return;
    }

    // Test prompt for comparison
    const { testPrompt } = await inquirer.prompt([
      {
        type: 'input',
        name: 'testPrompt',
        message: 'Enter test prompt for comparison:',
        default: 'Explain the concept of recursion in programming in simple terms.'
      }
    ]);

    this.logger.info('🔄 Running comparison tests...');

    const comparisonResults = [];
    for (const selection of modelSelections) {
      const startTime = Date.now();
      try {
        const response = await this.aiManager.chat([
          { role: 'user', content: testPrompt }
        ], selection.provider, selection.model);

        comparisonResults.push({
          provider: selection.provider,
          model: selection.model,
          duration: Date.now() - startTime,
          responseLength: response.content.length,
          tokensUsed: response.usage?.totalTokens || 0,
          response: response.content.substring(0, 200) + '...',
          success: true
        });
      } catch (error) {
        comparisonResults.push({
          provider: selection.provider,
          model: selection.model,
          duration: Date.now() - startTime,
          error: error instanceof Error ? error.message : String(error),
          success: false
        });
      }
    }

    // Display comparison
    console.log(boxen(
      `📊 Model Comparison Results\n\n` +
      `Test Prompt: "${testPrompt}"\n\n` +
      comparisonResults.map((result, i) =>
        `Model ${i + 1}: ${result.provider}/${result.model}\n` +
        `${result.success ? '✅' : '❌'} Duration: ${result.duration}ms\n` +
        `${result.success ?
          `Response Length: ${result.responseLength} chars\n` +
          `Tokens: ${result.tokensUsed}\n` +
          `Preview: ${result.response}` :
          `Error: ${result.error}`
        }`
      ).join('\n\n'),
      {
        padding: 1,
        borderColor: 'blue',
        title: '🔍 Model Comparison'
      }
    ));
  }

  private async benchmarkModels(): Promise<void> {
    this.logger.info('⚡ Model Benchmarking Tool');

    const { provider } = await inquirer.prompt([
      {
        type: 'list',
        name: 'provider',
        message: 'Select provider to benchmark:',
        choices: await this.aiManager.getAvailableProviders()
      }
    ]);

    const providerInstance = this.aiManager.getProvider(provider);
    if (!providerInstance) return;

    const models = await providerInstance.listModels();
    const { selectedModels } = await inquirer.prompt([
      {
        type: 'checkbox',
        name: 'selectedModels',
        message: 'Select models to benchmark:',
        choices: models.map(m => ({ name: m, value: m })),
        validate: (input) => input.length > 0 || 'Select at least one model'
      }
    ]);

    const benchmarkPrompts = [
      'Hello world',
      'Explain quantum computing',
      'Write a simple function to calculate fibonacci numbers',
      'What are the benefits of using TypeScript over JavaScript?',
      'Describe the Model-View-Controller (MVC) architectural pattern'
    ];

    this.logger.info(`🔄 Benchmarking ${selectedModels.length} models with ${benchmarkPrompts.length} prompts...`);

    const benchmarkResults = [];
    for (const model of selectedModels) {
      const modelResults = {
        model,
        totalDuration: 0,
        averageDuration: 0,
        successRate: 0,
        totalTokens: 0,
        averageResponseLength: 0,
        tests: [] as Array<{
          success: boolean;
          duration: number;
          responseLength?: number;
          tokensUsed?: number;
          error?: string;
        }>
      };

      for (const prompt of benchmarkPrompts) {
        const startTime = Date.now();
        try {
          const response = await this.aiManager.chat([
            { role: 'user', content: prompt }
          ], provider, model);

          const duration = Date.now() - startTime;
          modelResults.tests.push({
            success: true,
            duration,
            responseLength: response.content.length,
            tokensUsed: response.usage?.totalTokens || 0
          });
        } catch (error) {
          modelResults.tests.push({
            success: false,
            duration: Date.now() - startTime,
            error: error instanceof Error ? error.message : String(error)
          });
        }
      }

      // Calculate statistics
      const successfulTests = modelResults.tests.filter(t => t.success);
      modelResults.successRate = (successfulTests.length / modelResults.tests.length) * 100;
      modelResults.totalDuration = modelResults.tests.reduce((sum, t) => sum + t.duration, 0);
      modelResults.averageDuration = modelResults.totalDuration / modelResults.tests.length;
      modelResults.totalTokens = successfulTests.reduce((sum, t) => sum + (t.tokensUsed || 0), 0);
      modelResults.averageResponseLength = successfulTests.length > 0 ?
        successfulTests.reduce((sum, t) => sum + (t.responseLength || 0), 0) / successfulTests.length : 0;

      benchmarkResults.push(modelResults);
    }

    // Display benchmark results
    console.log(boxen(
      `⚡ Model Benchmark Results\n\n` +
      `Provider: ${provider}\n` +
      `Tests per model: ${benchmarkPrompts.length}\n\n` +
      benchmarkResults.map(result =>
        `📊 ${result.model}\n` +
        `  Success Rate: ${result.successRate.toFixed(1)}%\n` +
        `  Avg Duration: ${result.averageDuration.toFixed(0)}ms\n` +
        `  Total Tokens: ${result.totalTokens}\n` +
        `  Avg Response Length: ${result.averageResponseLength.toFixed(0)} chars`
      ).join('\n\n'),
      {
        padding: 1,
        borderColor: 'yellow',
        title: '⚡ Benchmark Results'
      }
    ));
  }

  private async configureModelSettings(): Promise<void> {
    this.logger.info('🔧 Model Configuration');

    const { action } = await inquirer.prompt([
      {
        type: 'list',
        name: 'action',
        message: 'What would you like to configure?',
        choices: [
          'Set default provider',
          'Set default model for provider',
          'Configure model parameters',
          'Set model preferences',
          'Back to main menu'
        ]
      }
    ]);

    switch (action) {
      case 'Set default provider':
        await this.setDefaultProvider();
        break;
      case 'Set default model for provider':
        await this.setDefaultModelForProvider();
        break;
      case 'Configure model parameters':
        await this.configureModelParameters();
        break;
      case 'Set model preferences':
        await this.setModelPreferences();
        break;
    }
  }



  private async configureModelParameters(): Promise<void> {
    this.logger.info('🔧 Model parameters configuration coming soon...');
    // TODO: Implement model parameter configuration
  }

  private async setModelPreferences(): Promise<void> {
    this.logger.info('⚙️ Model preferences configuration coming soon...');
    // TODO: Implement model preferences
  }

  private async setDefaultModelForProvider(): Promise<void> {
    const providers = await this.aiManager.getAvailableProviders();
    const { provider } = await inquirer.prompt([
      {
        type: 'list',
        name: 'provider',
        message: 'Select provider:',
        choices: providers
      }
    ]);

    const providerInstance = this.aiManager.getProvider(provider);
    if (!providerInstance) return;

    const models = await providerInstance.listModels();
    await this.setDefaultModel(provider, models);
  }

  // Documentation generation handlers
  public async handleDocumentation(options: any = {}): Promise<void> {
    if (options.generate) {
      await this.generateDocumentation();
    } else if (options.update) {
      await this.updateDocumentation();
    } else {
      await this.showDocumentationMenu();
    }
  }

  private async generateDocumentation(): Promise<void> {
    try {
      this.logger.info('📚 Generating comprehensive documentation...');
      const documentation = await this.documentationGenerator.generateDocumentation();

      console.log(boxen(
        `📚 Documentation Generated Successfully!\n\n` +
        `Files created:\n` +
        `• README.md\n` +
        `• API.md\n` +
        `• ARCHITECTURE.md\n` +
        `• CHANGELOG.md\n` +
        `• INDEX.md\n` +
        `• ${documentation.files.size} file documentation pages\n\n` +
        `Location: ./docs/`,
        {
          padding: 1,
          borderColor: 'green',
          title: '📚 Documentation Complete'
        }
      ));
    } catch (error) {
      this.logger.error(`Documentation generation failed: ${error}`);
    }
  }

  private async updateDocumentation(): Promise<void> {
    this.logger.info('📚 Updating existing documentation...');
    await this.generateDocumentation();
  }

  private async showDocumentationMenu(): Promise<void> {
    const { action } = await inquirer.prompt([
      {
        type: 'list',
        name: 'action',
        message: '📚 Documentation Management:',
        choices: [
          '📝 Generate full documentation',
          '🔄 Update existing documentation',
          '📋 View documentation status',
          '❌ Back to main menu'
        ]
      }
    ]);

    switch (action) {
      case '📝 Generate full documentation':
        await this.generateDocumentation();
        break;
      case '🔄 Update existing documentation':
        await this.updateDocumentation();
        break;
      case '📋 View documentation status':
        await this.showDocumentationStatus();
        break;
    }
  }

  private async showDocumentationStatus(): Promise<void> {
    const docsDir = './docs';
    const exists = fs.existsSync(docsDir);

    if (!exists) {
      this.logger.info('📚 No documentation found. Run generation to create documentation.');
      return;
    }

    const files = fs.readdirSync(docsDir);
    console.log(boxen(
      `📚 Documentation Status\n\n` +
      `Location: ${docsDir}\n` +
      `Files: ${files.length}\n\n` +
      `Available documentation:\n` +
      files.map(file => `• ${file}`).join('\n'),
      {
        padding: 1,
        borderColor: 'blue',
        title: '📚 Documentation Status'
      }
    ));
  }

  // Health monitoring handlers
  public async handleHealth(options: any = {}): Promise<void> {
    if (options.report) {
      await this.generateHealthReport();
    } else if (options.history) {
      await this.showHealthHistory();
    } else if (options.clear) {
      await this.clearHealthHistory();
    } else {
      await this.showHealthMenu();
    }
  }

  private async generateHealthReport(): Promise<void> {
    try {
      this.logger.info('🏥 Analyzing codebase health...');
      const report = await this.healthMonitor.generateHealthReport();

      console.log(boxen(
        `🏥 Codebase Health Report\n\n` +
        `Overall Score: ${report.overallScore}/100\n\n` +
        `Metrics:\n` +
        report.metrics.map(metric =>
          `${this.getStatusIcon(metric.status)} ${metric.name}: ${metric.value.toFixed(1)}/100\n` +
          `   ${metric.description}`
        ).join('\n\n') +
        `\n\nRecommendations:\n` +
        report.recommendations.map(rec => `• ${rec}`).join('\n'),
        {
          padding: 1,
          borderColor: report.overallScore >= 80 ? 'green' : report.overallScore >= 60 ? 'yellow' : 'red',
          title: '🏥 Health Report'
        }
      ));
    } catch (error) {
      this.logger.error(`Health report generation failed: ${error}`);
    }
  }

  private async showHealthHistory(): Promise<void> {
    const history = this.healthMonitor.getHealthHistory();

    if (history.length === 0) {
      this.logger.info('🏥 No health history available. Generate a report first.');
      return;
    }

    console.log(boxen(
      `🏥 Health History\n\n` +
      history.slice(-5).map((report, index) =>
        `Report ${index + 1}: ${report.overallScore}/100 (${report.timestamp.toLocaleDateString()})`
      ).join('\n'),
      {
        padding: 1,
        borderColor: 'cyan',
        title: '🏥 Health History'
      }
    ));
  }

  private async clearHealthHistory(): Promise<void> {
    this.healthMonitor.clearHealthHistory();
    this.logger.success('🏥 Health history cleared');
  }

  private async showHealthMenu(): Promise<void> {
    const { action } = await inquirer.prompt([
      {
        type: 'list',
        name: 'action',
        message: '🏥 Health Monitoring:',
        choices: [
          '📊 Generate health report',
          '📈 View health history',
          '🧹 Clear health history',
          '❌ Back to main menu'
        ]
      }
    ]);

    switch (action) {
      case '📊 Generate health report':
        await this.generateHealthReport();
        break;
      case '📈 View health history':
        await this.showHealthHistory();
        break;
      case '🧹 Clear health history':
        await this.clearHealthHistory();
        break;
    }
  }

  private getStatusIcon(status: string): string {
    switch (status) {
      case 'excellent': return '🟢';
      case 'good': return '🟡';
      case 'warning': return '🟠';
      case 'critical': return '🔴';
      default: return '⚪';
    }
  }

  // Configuration management handlers
  public async handleConfigManagement(options: any = {}): Promise<void> {
    if (options.validate) {
      await this.validateConfiguration();
    } else if (options.backup) {
      await this.backupConfiguration();
    } else if (options.restore) {
      await this.restoreConfiguration();
    } else if (options.export) {
      await this.exportConfiguration();
    } else if (options.import) {
      await this.importConfiguration();
    } else {
      await this.showConfigManagementMenu();
    }
  }

  private async validateConfiguration(): Promise<void> {
    const result = this.config.validateConfig();

    console.log(boxen(
      `🔧 Configuration Validation\n\n` +
      `Status: ${result.isValid ? '✅ Valid' : '❌ Invalid'}\n\n` +
      (result.errors.length > 0 ? `Errors:\n${result.errors.map(e => `• ${e}`).join('\n')}\n\n` : '') +
      (result.warnings.length > 0 ? `Warnings:\n${result.warnings.map(w => `• ${w}`).join('\n')}\n\n` : '') +
      (result.suggestions.length > 0 ? `Suggestions:\n${result.suggestions.map(s => `• ${s}`).join('\n')}` : ''),
      {
        padding: 1,
        borderColor: result.isValid ? 'green' : 'red',
        title: '🔧 Config Validation'
      }
    ));
  }

  private async backupConfiguration(): Promise<void> {
    try {
      const backupPath = this.config.backupConfig();
      this.logger.success(`🔧 Configuration backed up to: ${backupPath}`);
    } catch (error) {
      this.logger.error(`Backup failed: ${error}`);
    }
  }

  private async restoreConfiguration(): Promise<void> {
    const { backupPath } = await inquirer.prompt([
      {
        type: 'input',
        name: 'backupPath',
        message: 'Enter backup file path:',
        validate: (input) => input.trim() !== '' || 'Please provide a backup file path'
      }
    ]);

    try {
      this.config.restoreConfig(backupPath);
      this.logger.success('🔧 Configuration restored successfully');
    } catch (error) {
      this.logger.error(`Restore failed: ${error}`);
    }
  }

  private async exportConfiguration(): Promise<void> {
    try {
      const exportPath = this.config.exportConfig();
      this.logger.success(`🔧 Configuration exported to: ${exportPath}`);
    } catch (error) {
      this.logger.error(`Export failed: ${error}`);
    }
  }

  private async importConfiguration(): Promise<void> {
    const { importPath, mergeMode } = await inquirer.prompt([
      {
        type: 'input',
        name: 'importPath',
        message: 'Enter import file path:',
        validate: (input) => input.trim() !== '' || 'Please provide an import file path'
      },
      {
        type: 'confirm',
        name: 'mergeMode',
        message: 'Merge with existing config (vs replace entirely)?',
        default: true
      }
    ]);

    try {
      this.config.importConfig(importPath, mergeMode);
      this.logger.success('🔧 Configuration imported successfully');
    } catch (error) {
      this.logger.error(`Import failed: ${error}`);
    }
  }

  private async showConfigManagementMenu(): Promise<void> {
    const { action } = await inquirer.prompt([
      {
        type: 'list',
        name: 'action',
        message: '🔧 Configuration Management:',
        choices: [
          '✅ Validate configuration',
          '💾 Backup configuration',
          '🔄 Restore configuration',
          '📤 Export configuration',
          '📥 Import configuration',
          '📊 Show config stats',
          '❌ Back to main menu'
        ]
      }
    ]);

    switch (action) {
      case '✅ Validate configuration':
        await this.validateConfiguration();
        break;
      case '💾 Backup configuration':
        await this.backupConfiguration();
        break;
      case '🔄 Restore configuration':
        await this.restoreConfiguration();
        break;
      case '📤 Export configuration':
        await this.exportConfiguration();
        break;
      case '📥 Import configuration':
        await this.importConfiguration();
        break;
      case '📊 Show config stats':
        await this.showConfigStats();
        break;
    }
  }

  private async showConfigStats(): Promise<void> {
    const stats = this.config.getConfigStats();

    console.log(boxen(
      `🔧 Configuration Statistics\n\n` +
      `Version: ${stats.version}\n` +
      `Enabled Providers: ${stats.enabledProviders}\n` +
      `Total Projects: ${stats.totalProjects}\n` +
      `Config Size: ${(stats.configSize / 1024).toFixed(1)} KB\n` +
      `Last Modified: ${stats.lastModified.toLocaleString()}`,
      {
        padding: 1,
        borderColor: 'blue',
        title: '🔧 Config Stats'
      }
    ));
  }

  private async initializeSelfTraining(): Promise<void> {
    try {
      // Debug: Self-training system initialization started
      // this.logger.info('🧠 Initializing self-training system...');

      // Create self-training components with minimal configuration for background operation
      this.selfTrainingComponents = await SelfTrainingFactory.initializeWithAgent(
        this.logger,
        this.aiManager,
        this.memoryManager,
        this.codebaseAnalyzer,
        true // Use minimal mode for default initialization to reduce resource usage
      );

      // Configure for quiet background operation with minimal resource usage
      this.selfTrainingComponents.orchestrator.updateConfig({
        enabled: true,
        continuousLearning: true,
        autonomousResearch: false, // Disable autonomous research by default to reduce resource usage
        performanceOptimization: true,
        knowledgeSharing: true,
        adaptiveBehavior: true,
        backgroundProcessing: true,
        learningIntensity: 'low' // Start with low intensity for minimal impact
      });

      // Set up auto-training callback for file operations to learn from user actions
      this.changeTracker.setAutoTrainingCallback(async (operation: string, filePath: string, success: boolean) => {
        if (this.selfTrainingComponents) {
          await this.selfTrainingComponents.autoTrainingEngine.onFileOperation(operation, filePath, success);
        }
      });

      // Debug: Self-training system initialized successfully
      // Only show success message if not in quiet mode (for debugging purposes)
      // this.logger.success('✅ Self-training system initialized - Agent will learn from every interaction');
    } catch (error) {
      // Log errors for debugging - these are important for troubleshooting
      this.logger.debug(`Self-training initialization failed: ${error}`);
      // Don't fail the entire application if self-training fails to initialize
    }
  }

  public async enableFullSelfTraining(): Promise<void> {
    if (!this.selfTrainingComponents) {
      await this.initializeSelfTraining();
    }

    if (this.selfTrainingComponents) {
      // Enable full self-training capabilities
      this.selfTrainingComponents.orchestrator.updateConfig({
        enabled: true,
        continuousLearning: true,
        autonomousResearch: true,
        performanceOptimization: true,
        knowledgeSharing: true,
        adaptiveBehavior: true,
        backgroundProcessing: true,
        learningIntensity: 'medium'
      });

      await this.selfTrainingComponents.orchestrator.startSelfTraining();
      this.logger.success('🚀 Full self-training capabilities enabled');
    }
  }

  public getSelfTrainingStatus(): any {
    if (!this.selfTrainingComponents) {
      return { enabled: false, message: 'Self-training not initialized' };
    }

    return this.selfTrainingComponents.orchestrator.getTrainingStatus();
  }

  private formatDuration(milliseconds: number): string {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    if (hours > 0) {
      return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  }

  public async handleTechnologyTraining(options: any): Promise<void> {
    try {
      // Show header
      console.log(this.ui.header('Technology Training', 'Train the GOC Agent on specific technologies'));

      // Parse technologies
      const technologies = options.technologies.split(',').map((t: string) => t.trim().toLowerCase());

      // Validate technologies
      const supportedTechnologies = ['php', 'html', 'css', 'javascript', 'laravel', 'flutter', 'python', 'vue'];
      const invalidTechnologies = technologies.filter((t: string) => !supportedTechnologies.includes(t));

      if (invalidTechnologies.length > 0) {
        this.logger.error(`Unsupported technologies: ${invalidTechnologies.join(', ')}`);
        this.logger.info(`Supported technologies: ${supportedTechnologies.join(', ')}`);
        return;
      }

      // Initialize training components if not already done
      if (!this.selfTrainingComponents) {
        await this.initializeSelfTraining();
      }

      if (!this.selfTrainingComponents) {
        this.logger.error('Failed to initialize self-training components');
        return;
      }

      // Initialize technology training command with components
      await this.technologyTrainingCommand.initializeTrainingComponents(
        this.selfTrainingComponents.orchestrator,
        this.webLearningEngine,
        this.selfTrainingComponents.knowledgeRepository,
        this.selfTrainingComponents.curiosityEngine
      );

      // Create training configuration
      const config: TechnologyTrainingConfig = {
        technologies,
        depth: options.depth as 'basic' | 'intermediate' | 'advanced' | 'comprehensive',
        focus: options.focus as 'fundamentals' | 'best-practices' | 'latest-trends' | 'all',
        duration: parseInt(options.duration) || 60,
        concurrent: options.concurrent || false
      };

      // Show training plan
      console.log(this.ui.section('Training Configuration',
        `Technologies: ${technologies.join(', ')}\n` +
        `Depth: ${config.depth}\n` +
        `Focus: ${config.focus}\n` +
        `Duration: ${config.duration} minutes\n` +
        `Mode: ${config.concurrent ? 'Concurrent' : 'Sequential'}`
      ));

      // Confirm training
      const { proceed } = await inquirer.prompt([
        {
          type: 'confirm',
          name: 'proceed',
          message: '🚀 Start technology training?',
          default: true
        }
      ]);

      if (!proceed) {
        this.logger.info('Training cancelled');
        return;
      }

      // Start training
      const startTime = Date.now();
      this.logger.info(`🚀 Starting technology training on ${technologies.length} technologies...`);

      await this.technologyTrainingCommand.trainOnTechnologies(config);

      const duration = Date.now() - startTime;
      this.logger.success(`✅ Technology training completed in ${this.formatDuration(duration)}`);

      // Show final progress
      const progress = this.technologyTrainingCommand.getTrainingProgress();
      console.log('\n📊 Final Training Results:');
      for (const [tech, result] of progress) {
        const status = result.status === 'completed' ? '✅' :
                      result.status === 'failed' ? '❌' : '⏳';
        console.log(`${status} ${tech.toUpperCase()}: ${result.progress}% (${result.topicsLearned.length} topics)`);
      }

    } catch (error) {
      this.logger.error(`Technology training failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }
}
