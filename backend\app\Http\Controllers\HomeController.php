<?php

namespace App\Http\Controllers;

use App\Models\SubscriptionPlan;
use Illuminate\Http\Request;

class HomeController extends Controller
{
    /**
     * Show the landing page.
     */
    public function index()
    {
        $plans = SubscriptionPlan::active()->ordered()->get();

        return view('home.index', compact('plans'));
    }

    /**
     * Show the pricing page.
     */
    public function pricing()
    {
        $plans = SubscriptionPlan::active()->ordered()->get();

        return view('home.pricing', compact('plans'));
    }

    /**
     * Show the features page.
     */
    public function features()
    {
        return view('home.features');
    }

    /**
     * Show the documentation page.
     */
    public function docs()
    {
        return view('home.docs');
    }

    /**
     * Show the about page.
     */
    public function about()
    {
        return view('home.about');
    }

    /**
     * Show the contact page.
     */
    public function contact()
    {
        return view('home.contact');
    }

    /**
     * Handle contact form submission.
     */
    public function contactSubmit(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'subject' => 'required|string|max:255',
            'message' => 'required|string|max:2000',
        ]);

        // Here you would typically send an email or store the message
        // For now, we'll just redirect with a success message

        return redirect()->route('contact')->with('success', 'Thank you for your message! We\'ll get back to you soon.');
    }
}
